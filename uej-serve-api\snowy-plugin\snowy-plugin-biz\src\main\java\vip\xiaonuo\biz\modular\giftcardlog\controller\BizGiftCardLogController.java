
package vip.xiaonuo.biz.modular.giftcardlog.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.giftcardlog.entity.BizGiftCardLog;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogAddParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogEditParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogIdParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogPageParam;
import vip.xiaonuo.biz.modular.giftcardlog.service.BizGiftCardLogService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 礼品卡记录控制器
 *
 * <AUTHOR>
 * @date  2024/06/13 09:46
 */
@Tag(name = "礼品卡记录控制器")
@RestController
@Validated
public class BizGiftCardLogController {

    @Resource
    private BizGiftCardLogService bizGiftCardLogService;

    /**
     * 获取礼品卡记录分页
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    @Operation(summary = "获取礼品卡记录分页")
    @SaCheckPermission("/biz/giftcardlog/page")
    @GetMapping("/biz/giftcardlog/page")
    public CommonResult<Page<BizGiftCardLog>> page(BizGiftCardLogPageParam bizGiftCardLogPageParam) {
        return CommonResult.data(bizGiftCardLogService.page(bizGiftCardLogPageParam));
    }

    /**
     * 添加礼品卡记录
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    @Operation(summary = "添加礼品卡记录")
    @CommonLog("添加礼品卡记录")
    @SaCheckPermission("/biz/giftcardlog/add")
    @PostMapping("/biz/giftcardlog/add")
    public CommonResult<String> add(@RequestBody @Valid BizGiftCardLogAddParam bizGiftCardLogAddParam) {
        bizGiftCardLogService.add(bizGiftCardLogAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑礼品卡记录
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    @Operation(summary = "编辑礼品卡记录")
    @CommonLog("编辑礼品卡记录")
    @SaCheckPermission("/biz/giftcardlog/edit")
    @PostMapping("/biz/giftcardlog/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizGiftCardLogEditParam bizGiftCardLogEditParam) {
        bizGiftCardLogService.edit(bizGiftCardLogEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除礼品卡记录
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    @Operation(summary = "删除礼品卡记录")
    @CommonLog("删除礼品卡记录")
    @SaCheckPermission("/biz/giftcardlog/delete")
    @PostMapping("/biz/giftcardlog/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizGiftCardLogIdParam> bizGiftCardLogIdParamList) {
        bizGiftCardLogService.delete(bizGiftCardLogIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取礼品卡记录详情
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    @Operation(summary = "获取礼品卡记录详情")
    @SaCheckPermission("/biz/giftcardlog/detail")
    @GetMapping("/biz/giftcardlog/detail")
    public CommonResult<BizGiftCardLog> detail(@Valid BizGiftCardLogIdParam bizGiftCardLogIdParam) {
        return CommonResult.data(bizGiftCardLogService.detail(bizGiftCardLogIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    @Operation(summary = "获取礼品卡记录动态字段的配置")
    @SaCheckPermission("/biz/giftcardlog/dynamicFieldConfigList")
    @GetMapping("/biz/giftcardlog/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizGiftCardLogService.dynamicFieldConfigList(columnName));
    }
}
