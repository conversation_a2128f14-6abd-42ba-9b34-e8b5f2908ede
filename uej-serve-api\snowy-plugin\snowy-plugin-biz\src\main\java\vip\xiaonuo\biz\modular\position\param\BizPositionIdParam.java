
package vip.xiaonuo.biz.modular.position.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 岗位Id参数
 *
 * <AUTHOR>
 * @date 2022/4/21 16:13
 **/
@Getter
@Setter
public class BizPositionIdParam {

    /** id */
    @Schema(description = "id")
    @NotBlank(message = "id不能为空")
    private String id;
}
