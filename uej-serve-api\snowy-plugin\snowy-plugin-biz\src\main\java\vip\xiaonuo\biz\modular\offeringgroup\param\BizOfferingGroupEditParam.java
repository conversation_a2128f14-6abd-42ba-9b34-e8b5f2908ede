
package vip.xiaonuo.biz.modular.offeringgroup.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import vip.xiaonuo.biz.modular.offering.entity.BizOffering;
import vip.xiaonuo.biz.modular.offeringgroupitem.entity.BizOfferingGroupItem;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 服务或产品组合编辑参数
 *
 * <AUTHOR>
 * @date  2024/06/12 16:57
 **/
@Getter
@Setter
public class BizOfferingGroupEditParam {

    /** ID */
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** Category */
    @Schema(description = "Category")
    private String category;

    /** Name */
    @Schema(description = "Name")
    private String name;

    /** Planning Hours */
    @Schema(description = "Planning Hours")
    private String planningHours;

    /** List Price */
    @Schema(description = "List Price")
    private String listPrice;

    /** Cost Price */
    @Schema(description = "Cost Price")
    private String costPrice;

    /** Group */
    @Schema(description = "Group")
    private String group;

    private List<BizOfferingGroupItem> offerings;

    private String extJson;
}
