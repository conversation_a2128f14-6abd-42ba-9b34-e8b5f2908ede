
package vip.xiaonuo.dbs.modular.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据源库字段列表参数
 *
 * <AUTHOR>
 * @date 2022/7/29 9:59
 */
@Getter
@Setter
public class DbsStorageTableColumnParam {

    /** 表名称 */
    @Schema(description = "表名称")
    @NotBlank(message = "表名称不能为空")
    private String tableName;
}
