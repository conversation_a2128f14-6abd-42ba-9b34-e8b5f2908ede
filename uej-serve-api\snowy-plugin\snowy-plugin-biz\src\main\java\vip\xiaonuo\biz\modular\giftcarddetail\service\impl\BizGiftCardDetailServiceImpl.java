package vip.xiaonuo.biz.modular.giftcarddetail.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.modular.giftcard.entity.BizGiftCard;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.giftcarddetail.entity.BizGiftCardDetail;
import vip.xiaonuo.biz.modular.giftcarddetail.mapper.BizGiftCardDetailMapper;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailAddParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailEditParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailIdParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailPageParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailChangeStatusParam;
import vip.xiaonuo.biz.modular.giftcarddetail.service.BizGiftCardDetailService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.Date;
import java.util.List;

/**
 * 礼品卡信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/07/01 18:08
 **/
@Service
public class BizGiftCardDetailServiceImpl extends ServiceImpl<BizGiftCardDetailMapper, BizGiftCardDetail> implements BizGiftCardDetailService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizGiftCardDetail> page(BizGiftCardDetailPageParam bizGiftCardDetailPageParam) {
        QueryWrapper<BizGiftCardDetail> queryWrapper = new QueryWrapper<BizGiftCardDetail>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizGiftCardDetailPageParam.getCardNumber())) {
            queryWrapper.lambda().eq(BizGiftCardDetail::getCardNumber, bizGiftCardDetailPageParam.getCardNumber());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardDetailPageParam.getActived())) {
            queryWrapper.lambda().eq(BizGiftCardDetail::getActived, bizGiftCardDetailPageParam.getActived());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardDetailPageParam.getMainId())) {
            queryWrapper.lambda().eq(BizGiftCardDetail::getMainId, bizGiftCardDetailPageParam.getMainId());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardDetailPageParam.getOrgId())) {
            queryWrapper.lambda().eq(BizGiftCardDetail::getOrgId, bizGiftCardDetailPageParam.getOrgId());
        }
        if(ObjectUtil.isAllNotEmpty(bizGiftCardDetailPageParam.getSortField(), bizGiftCardDetailPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizGiftCardDetailPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizGiftCardDetailPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizGiftCardDetailPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizGiftCardDetail::getId);
        }
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizGiftCardDetail::getOrgId, loginUserDataScope);
        } else {
            // 如果没有数据范围权限，只能查看自己创建的礼品卡详情
            queryWrapper.lambda().eq(BizGiftCardDetail::getCreateUser, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizGiftCardDetailAddParam bizGiftCardDetailAddParam) {
        // 礼品卡详情添加不需要特殊的权限校验，因为任何用户都可以添加礼品卡详情
        BizGiftCardDetail bizGiftCardDetail = BeanUtil.toBean(bizGiftCardDetailAddParam, BizGiftCardDetail.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizGiftCardDetail.getOrgId())) {
            String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
            if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                bizGiftCardDetail.setOrgId(currentUserOrgId);
            }
        }
        
        this.save(bizGiftCardDetail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizGiftCardDetailEditParam bizGiftCardDetailEditParam) {
        BizGiftCardDetail bizGiftCardDetail = this.queryEntity(bizGiftCardDetailEditParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizGiftCardDetail.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的礼品卡详情，机构id：{}", bizGiftCardDetail.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能编辑自己创建的礼品卡详情
            if(!bizGiftCardDetail.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该礼品卡详情，卡号：{}", bizGiftCardDetail.getCardNumber());
            }
        }
        
        BeanUtil.copyProperties(bizGiftCardDetailEditParam, bizGiftCardDetail);
        this.updateById(bizGiftCardDetail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizGiftCardDetailIdParam> bizGiftCardDetailIdParamList) {
        List<String> giftCardDetailIdList = CollStreamUtil.toList(bizGiftCardDetailIdParamList, BizGiftCardDetailIdParam::getId);
        if(ObjectUtil.isNotEmpty(giftCardDetailIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                List<BizGiftCardDetail> giftCardDetailList = this.listByIds(giftCardDetailIdList);
                for(BizGiftCardDetail giftCardDetail : giftCardDetailList) {
                    if(!loginUserDataScope.contains(giftCardDetail.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的礼品卡详情，机构id：{}", giftCardDetail.getOrgId());
                    }
                }
            } else {
                List<BizGiftCardDetail> giftCardDetailList = this.listByIds(giftCardDetailIdList);
                for(BizGiftCardDetail giftCardDetail : giftCardDetailList) {
                    // 如果没有数据范围权限，只能删除自己创建的礼品卡详情
                    if(!giftCardDetail.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该礼品卡详情，卡号：{}", giftCardDetail.getCardNumber());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(giftCardDetailIdList);
    }

    @Override
    public BizGiftCardDetail detail(BizGiftCardDetailIdParam bizGiftCardDetailIdParam) {
        BizGiftCardDetail bizGiftCardDetail = this.queryEntity(bizGiftCardDetailIdParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizGiftCardDetail.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的礼品卡详情，机构id：{}", bizGiftCardDetail.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能查看自己创建的礼品卡详情
            if(!bizGiftCardDetail.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该礼品卡详情，卡号：{}", bizGiftCardDetail.getCardNumber());
            }
        }
        
        return bizGiftCardDetail;
    }

    @Override
    public BizGiftCardDetail queryEntity(String id) {
        BizGiftCardDetail bizGiftCardDetail = this.getById(id);
        if(ObjectUtil.isEmpty(bizGiftCardDetail)) {
            throw new CommonException("礼品卡信息不存在，id值为：{}", id);
        }
        return bizGiftCardDetail;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizGiftCardDetailServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizGiftCardDetail.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }

    @Override
    public String redeemGiftCard(String cardNo, String pin, String usedAmount) {
        BizGiftCardDetail giftCard = this.lambdaQuery().eq(BizGiftCardDetail::getCardNumber, cardNo).one();
        if (ObjectUtil.isEmpty(giftCard)) {
            return "There is no such gift card, please check the number.";
        }
        if (giftCard.getActived().equals("0")) {
            return "The gift card has not been activated."; //未激活
        }
        if (!giftCard.getPin().equals(pin)) {
            return "The password is incorrect."; //密码不正确
        }
        if (DateUtil.parse(giftCard.getExpDate(), "yyyy-MM-dd HH:mm:ss").isBefore(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")))) {
            return "The gift card has expired."; //逾期
        }
        if (Double.parseDouble(usedAmount) > Double.parseDouble(giftCard.getRestValue())) {
            return "Insufficient balance, remaining amount is " + giftCard.getRestValue()  + "."; //余额不足
        }
        giftCard.setRestValue(String.valueOf(Double.parseDouble(giftCard.getRestValue()) - Double.parseDouble(usedAmount)));
        giftCard.setUsedDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        this.updateById(giftCard);
        return "success";
    }

    @Override
    public String restoreGiftCardBalance(String cardNo, String pin, String restoreAmount) {
        try {
            BizGiftCardDetail giftCard = this.lambdaQuery().eq(BizGiftCardDetail::getCardNumber, cardNo).one();
            if (ObjectUtil.isEmpty(giftCard)) {
                return "Gift card not found: " + cardNo;
            }
            
            // 验证PIN码
            if (!giftCard.getPin().equals(pin)) {
                return "Invalid PIN for gift card: " + cardNo;
            }
            
            // 恢复余额 - 将使用的金额加回到剩余余额中
            double currentRestValue = Double.parseDouble(giftCard.getRestValue());
            double restoreAmountValue = Double.parseDouble(restoreAmount);
            double newRestValue = currentRestValue + restoreAmountValue;
            
            // 确保恢复后的余额不超过原始面值
            double originalValue = Double.parseDouble(giftCard.getValue());
            if (newRestValue > originalValue) {
                newRestValue = originalValue;
            }
            
            giftCard.setRestValue(String.valueOf(newRestValue));
            this.updateById(giftCard);
            
            return "success";
        } catch (Exception e) {
            return "Error restoring gift card balance: " + e.getMessage();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeActivationStatus(BizGiftCardDetailChangeStatusParam bizGiftCardDetailChangeStatusParam) {
        BizGiftCardDetail bizGiftCardDetail = this.queryEntity(bizGiftCardDetailChangeStatusParam.getId());
        
        // 设置激活状态
        bizGiftCardDetail.setActived(bizGiftCardDetailChangeStatusParam.getActived());
        
        // 如果激活，设置激活时间和过期时间
        if ("1".equals(bizGiftCardDetailChangeStatusParam.getActived())) {
            bizGiftCardDetail.setActiveDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            bizGiftCardDetail.setExpDate(DateUtil.format(DateUtil.offsetMonth(new Date(), 36), "yyyy-MM-dd HH:mm:ss")); // 3年后过期
        }
        
        this.updateById(bizGiftCardDetail);
    }
}
