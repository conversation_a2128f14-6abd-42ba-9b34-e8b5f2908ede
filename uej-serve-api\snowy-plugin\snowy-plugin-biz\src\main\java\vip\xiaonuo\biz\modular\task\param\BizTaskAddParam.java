
package vip.xiaonuo.biz.modular.task.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务信息添加参数
 *
 * <AUTHOR>
 * @date  2024/06/12 15:42
 **/
@Getter
@Setter
public class BizTaskAddParam {

    /** Item Name */
    @Schema(description = "Item Name")
    private String itemName;

    /** Create by */
    @Schema(description = "Create by")
    private String creator;

    /** Start Time */
    @Schema(description = "Start Time")
    private String startTime;

    /** Planning end Time */
    @Schema(description = "Planning end Time")
    private String planningEndTime;

    /** actual finish Time */
    @Schema(description = "actual finish Time")
    private String actualFinishTime;

    /** assign */
    @Schema(description = "assign")
    private String assign;

    /** state */
    @Schema(description = "state")
    private String state;

    /** Stop Beginning Time */
    @Schema(description = "Stop Beginning Time")
    private String stopBeginningTime;

    /** Stop End Time */
    @Schema(description = "Stop End Time")
    private String stopEndTime;

    /** Product Name */
    @Schema(description = "Product Name")
    private String extJson;

    /** Total Price */
    @Schema(description = "Total Price")
    private String totalPrice;

    /** Total Cost Price */
    @Schema(description = "Total Cost Price")
    private String totalCostPrice;

    /** Total Planning Hours */
    @Schema(description = "Total Planning Hours")
    private String totalPlanHours;

    /** Total Actual Hours */
    @Schema(description = "Total Actual Hours")
    private String totalActualHours;

    /** Total Invoice Hours */
    @Schema(description = "Total Invoice Hours")
    private String totalInvoiceHours;

    /** Cash */
    @Schema(description = "Cash")
    private String cash;

    /** Card */
    @Schema(description = "Card")
    private String card;

    private String rate;

    /** Surcharge Fee */
    @Schema(description = "Surcharge Fee")
    private String surchargeFee;

    /** Gift_Card */
    @Schema(description = "Gift_Card")
    private String giftCard;

    /** Voucher */
    @Schema(description = "Voucher")
    private String voucher;

    /** Processing */
    @Schema(description = "Processing")
    private String processing;

    /** 备注 */
    private String description;

    List<BizTaskItem> bizTaskItemList;

    /** Id 保存子表时使用 */
    private String id;

    @Schema(description = "保险公司金额")
    private double insuranceValue;

    @Schema(description = "已付金额")
    private double paidValue;

    @Schema(description = "会员id")
    private String customerId;

    @Schema(description = "会员姓名")
    private String customerName;

    @Schema(description = "小费")
    private String tip;
}
