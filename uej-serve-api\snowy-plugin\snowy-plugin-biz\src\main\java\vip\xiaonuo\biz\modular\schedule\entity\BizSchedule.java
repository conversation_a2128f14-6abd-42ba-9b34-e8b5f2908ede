
package vip.xiaonuo.biz.modular.schedule.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 排班实体
 *
 * <AUTHOR>
 * @date  2024/06/18 16:16
 **/
@Getter
@Setter
@TableName("biz_schedule")
public class BizSchedule {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** Date ID */
    @Schema(description = "Date ID")
    private String dateId;

    /** Staff ID */
    @Schema(description = "Staff ID")
    private String staffId;

    /** Staff Name */
    @Schema(description = "Staff Name")
    private String staffName;

    private String organizationId;

    private String organizationName;

    /** Work Time */
    @Schema(description = "Work Time")
    private String workTime;

    /** Open Time */
    @Schema(description = "Open Time")
    private String openTime;

    /** Rest Time */
    @Schema(description = "Rest Time")
    private String restTime;

    /** Assign Date */
    @Schema(description = "Assign Date")
    private String assignDate;

    /** Accept */
    @Schema(description = "Accept")
    private String accept;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String extJson;

    @TableField(exist = false)
    private List<String> staffs;

    @TableField(exist = false)
    private int staffNum;

    @TableField(exist = false)
    private String taskStatus;

    @TableField(exist = false)
    private String work;

    @TableField(exist = false)
    private String free;

    @TableField(exist = false)
    private String color;

    @TableField(exist = false)
    private long sort;

    @TableField(exist = false)
    private BigDecimal money;

    @TableField(exist = false)
    private BigDecimal totalMoney;

    @TableField(exist = false)
    private BigDecimal tip;

    @TableField(exist = false)
    private BigDecimal totalTip;

    @TableField(exist = false)
    private String next;

    @TableField(exist = false)
    private double workingHours;
}
