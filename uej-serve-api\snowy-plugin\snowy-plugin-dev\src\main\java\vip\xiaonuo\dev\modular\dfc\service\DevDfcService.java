
package vip.xiaonuo.dev.modular.dfc.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.dev.modular.dfc.entity.DevDfc;
import vip.xiaonuo.dev.modular.dfc.param.*;
import vip.xiaonuo.dev.modular.dfc.result.DevDfcDbsSelectorResult;

import java.util.List;

/**
 * 动态字段配置Service接口
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 **/
public interface DevDfcService extends IService<DevDfc> {

    /**
     * 获取动态字段配置分页
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    Page<DevDfc> page(DevDfcPageParam devDfcPageParam);

    /**
     * 添加动态字段配置
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    void add(DevDfcAddParam devDfcAddParam);

    /**
     * 编辑动态字段配置
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    void edit(DevDfcEditParam devDfcEditParam);

    /**
     * 删除动态字段配置
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    void delete(List<DevDfcIdParam> devDfcIdParamList);

    /**
     * 获取动态字段配置详情
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    DevDfc detail(DevDfcIdParam devDfcIdParam);

    /**
     * 获取动态字段配置详情
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     **/
    DevDfc queryEntity(String id);

    /**
     * 获取表中字段配置
     *
     * <AUTHOR>
     * @date  2023/08/02 21:31
     */
    List<JSONObject> getList(String dbsId, String tableName, String columnName);

    /**
     * 迁移数据
     *
     * <AUTHOR>
     * @date  2023/08/02 21:31
     */
    void migrate(DevDfcMigrateParam devDfcMigrateParam);

    /* ====动态字段部分所需要用到的选择器==== */

    /**
     * 获取所有数据源信息
     *
     * <AUTHOR>
     * @date 2023/2/1 10:43
     **/
    List<DevDfcDbsSelectorResult> dbsSelector();

    /**
     * 获取所有表信息
     *
     * <AUTHOR>
     * @date 2022/10/25 22:33
     **/
    List<JSONObject> dbTableSelector(DevDfcDbTableSelectorParam devDfcDbTableSelectorParam);

    /**
     * 获取表内所有字段信息
     *
     * <AUTHOR>
     * @date 2022/10/25 22:33
     **/
    List<JSONObject> dbColumnSelector(DevDfcDbColumnSelectorParam dbsTableColumnParam);



}
