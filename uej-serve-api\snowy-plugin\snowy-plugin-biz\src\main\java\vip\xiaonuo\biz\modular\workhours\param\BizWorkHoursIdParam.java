
package vip.xiaonuo.biz.modular.workhours.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 工时Id参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:27
 **/
@Getter
@Setter
public class BizWorkHoursIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;
}
