
package vip.xiaonuo.flw.modular.process.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程迁移参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:45
 */
@Getter
@Setter
public class FlwProcessMigrateParam {

    /** 流程id */
    @Schema(description = "流程id")
    @NotBlank(message = "id不能为空")
    private String id;

    /** 目标节点id */
    @Schema(description = "目标节点id")
    @NotBlank(message = "targetActivityId不能为空")
    private String targetActivityId;
}
