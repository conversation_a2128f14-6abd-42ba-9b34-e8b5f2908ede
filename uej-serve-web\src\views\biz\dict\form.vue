<template>
	<xn-form-container title="Edit dictionary" :width="550" :visible="visible" :destroy-on-close="true" @close="onClose">
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical" :label-col="labelCol">
			<a-form-item label="Parent dictionary：" name="parentId">
				<a-tree-select
					:disabled="true"
					v-model:value="formData.parentId"
					v-model:treeExpandedKeys="defaultExpandedKeys"
					class="xn-wd"
					:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
					placeholder="Please select the parent dictionary"
					allow-clear
					:tree-data="treeData"
					:field-names="{
						children: 'children',
						label: 'name',
						value: 'id'
					}"
					selectable="false"
					treeLine
				/>
			</a-form-item>
			<a-form-item label="dictLabel：" name="dictLabel">
				<a-input v-model:value="formData.dictLabel" placeholder="Please enter dictLabel" allow-clear />
			</a-form-item>
			<a-form-item label="dictValue：" name="dictValue">
				<a-input v-model:value="formData.dictValue" placeholder="Please enter dictValue" allow-clear :disabled="true" />
			</a-form-item>
			<a-form-item label="sortCode：" name="sortCode">
				<a-input-number class="xn-wd" v-model:value="formData.sortCode" :max="1000" />
			</a-form-item>
		</a-form>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" @click="onSubmit">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="dictForm">
	import { required } from '@/utils/formRules'
	import bizDictApi from '@/api/biz/bizDictApi'

	// 定义emit事件
	const emit = defineEmits({ successful: null })
	// 默认是关闭状态
	const visible = ref(false)
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	// 定义树元素
	const treeData = ref([])
	// 默认展开的节点(顶级)
	const defaultExpandedKeys = ref([0])

	// 打开抽屉
	const onOpen = (record, parentId) => {
		visible.value = true
		formData.value = {
			sortCode: 99
		}
		if (parentId) {
			formData.value.parentId = parentId
		}
		if (record) {
			formData.value = Object.assign({}, record)
		}
		bizDictApi.dictTree().then((res) => {
			treeData.value = [
				{
					id: 0,
					parentId: '-1',
					name: 'top-level',
					children: res
				}
			]
		})
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
	}
	// 默认要校验的
	const formRules = {
		dictLabel: [required('Please enter a dictionary name')],
		dictValue: [required('Please select a dictionary value')],
		sortCode: [required('Please select sort')]
	}
	// 表单固定label实现
	const labelCol = ref({
		style: {
			width: '100px'
		}
	})
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value
			.validate()
			.then(() => {
				bizDictApi.submitForm(formData.value).then(() => {
					visible.value = false
					emit('successful')
				})
			})
			.catch(() => {})
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
