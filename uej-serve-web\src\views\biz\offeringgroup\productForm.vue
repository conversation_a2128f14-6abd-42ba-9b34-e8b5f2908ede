<template>
	<DragModal
		:border="false"
		:visible="visible"
		@close="onClose"
		:width="1500"
		title="Select a service or product"
		@ok="imports"
	>
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
			:scroll="{x:'max-content'}"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="offeringFormRef.onOpen()">
						<template #icon><plus-outlined /></template>
						{{ $t('common.addButton') }}
					</a-button>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a @click="">{{ $t('common.imports') }}</a>
				</template>
			</template>
		</s-table>
	</DragModal>
	<offeringForm ref="offeringFormRef"></offeringForm>
</template>

<script setup>
	import { message } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import offeringForm from '@/views/biz/offering/form.vue'
	import bizOfferingApi from '@/api/biz/bizOfferingApi'
	const emit = defineEmits(['successful'])
	const offeringFormRef = ref()
	const searchFormState = ref({})
	const tableRef = ref()
	const visible = ref(false)
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	const columns = [
		{
			title: 'Category',
			dataIndex: 'category'
		},
		{
			title: 'Name',
			dataIndex: 'name'
		},
		{
			title: 'Planning Hours',
			dataIndex: 'planningHours'
		},
		{
			title: 'Invoice Hours',
			dataIndex: 'invoiceHours'
		},
		{
			title: 'List Price',
			dataIndex: 'listPrice'
		},
		{
			title: 'Cost Price',
			dataIndex: 'costPrice'
		}
	]

	const selectedRowKeys = ref([])
	const selectedRowsData = ref([])
	// 列表选择配置
	const options = {
		// columns数字类型字段加入 needTotal: true 可以勾选自动算账
		alert: {
			show: true,
			clear: () => {
				selectedRowKeys.value = ref([])
				selectedRowsData.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
				selectedRowsData.value = selectedRows
			}
		}
	}
	const loadData = (parameter) => {
		const searchFormParam = cloneDeep(searchFormState.value)
		return bizOfferingApi.bizOfferingPage(Object.assign(parameter, searchFormParam)).then((data) => {
			return data
		})
	}

	const onOpen = (record) => {
		visible.value = true
		loadData({ current: 1, size: 10 })
	}

	const onClose = () => {
		searchFormState.value = {}
		clearSelected()
		visible.value = false
	}

	const imports = () => {
		if (selectedRowsData.value.length < 1) {
			message.warning('Select at least one')
		} else {
			const updatedRows = selectedRowsData.value.map((item) => {
				return {
					...item,
					offeringId: item.id,
					offeringName: item.name,
					num:1
				}
			})

			emit('successful', updatedRows)
			onClose()
		}
	}

	const clearSelected = () => {
		if (Array.isArray(tableRef.value)) {
			tableRef.value.forEach((item) => {
				item.clearSelected()
			})
		} else {
			tableRef.value.clearSelected()
		}
		selectedRowsData.value = []
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>

<style lang="less" scoped></style>
