
package vip.xiaonuo.dev.modular.dfc.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态字段配置Id参数
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 **/
@Getter
@Setter
public class DevDfcIdParam {

    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;
}
