<template>
  <xn-form-container
      :title="formData.id ? 'edit giftcard' : 'add giftcard'"
      :width="600"
      v-model:open="open"
      :destroy-on-close="true"
      @close="onClose"
  >
    <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
      <a-row :gutter="16">
        <!-- <a-col :span="12">
          <a-form-item label="Card Number：" name="cardNumber">
            <a-input v-model:value="formData.cardNumber" placeholder="Please enter Card Number" allow-clear disabled/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Pin：" name="pin">
            <a-input v-model:value="formData.pin" placeholder="Please enter Pin" allow-clear/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Create Date：" name="createDate">
            <a-date-picker
                v-model:value="formData.createDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                show-time
                placeholder="Please enter Create Date"
                style="width: 100%"
				disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Active Date：" name="activeDate">
            <a-date-picker
                v-model:value="formData.activeDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                show-time
                placeholder="Please enter Active Date"
                style="width: 100%"
				disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Exp Date：" name="expDate">
            <a-date-picker
                v-model:value="formData.expDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                show-time
                placeholder="Please enter Exp Date"
                style="width: 100%"
				disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Used Date：" name="usedDate">
            <a-date-picker
                v-model:value="formData.usedDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                show-time
                placeholder="Please enter Used Date"
                style="width: 100%"
				disabled
            />
          </a-form-item>
        </a-col> -->

        <a-col :span="12">
          <a-form-item label="Prefix：" name="prefix">
            <a-input v-model:value="formData.prefix" placeholder="Please enter card prefix" allow-clear />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="Start No：" name="startNo">
            <a-input-number v-model:value="formData.startNo" placeholder="Please enter start number" allow-clear min="1" style="width: 100%;" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="Value：" name="value">
            <a-input-number v-model:value="formData.value" placeholder="Please Enter Value" allow-clear min="1"
                            max="1000000" style="width: 100%;" :disabled="formData.actived=='1'"/>

          </a-form-item>
        </a-col>
        <!-- <a-col :span="12">
          <a-form-item label="Rest Value：" name="restValue">
            <a-input-number v-model:value="formData.restValue" placeholder="Rest Value" allow-clear min="1"
                            max="1000000" style="width: 100%;" disabled/>
          </a-form-item>
        </a-col> -->
        <!-- <a-col :span="12">
          <a-form-item label="If Avtived：" name="actived">
            <a-radio-group v-model:value="formData.actived" button-style="solid" :disabled="formData.actived=='1' || !formData.id" @change="onActived">
				<a-radio-button value="0">Not Actived</a-radio-button>
				<a-radio-button value="1">Actived</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col> -->
      </a-row>
    </a-form>
    <a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
          <xn-form-item :fieldConfig="item" :formData="dynamicFormData"/>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
      <a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
    </template>
  </xn-form-container>
</template>

<script setup name="bizGiftCardForm">
import dayjs from 'dayjs'
import {cloneDeep} from 'lodash-es'
import {required} from '@/utils/formRules'
import bizGiftCardApi from '@/api/biz/bizGiftCardApi'
// 抽屉状态
const open = ref(false)
const emit = defineEmits({successful: null})
const formRef = ref()
// 表单数据
const formData = ref({})
const submitLoading = ref(false)
// 动态表单
const dynamicFormRef = ref()
const dynamicFieldConfigList = ref([])
const dynamicFormData = ref({})

// 打开抽屉
const onOpen = (record) => {
  open.value = true
  bizGiftCardApi.bizGiftCardDynamicFieldConfigList({columnName: 'EXT_JSON'}).then((data) => {
    dynamicFieldConfigList.value = data
  })
  formData.value={
	actived:'0',
	createDate:dayjs().format('YYYY-MM-DD HH:mm:ss'),
  prefix: '',
  startNo: ''
  }
  if (record) {
    let recordData = cloneDeep(record)
    formData.value = Object.assign({}, recordData)
    dynamicFormData.value = JSON.parse(formData.value.extJson || null) || {}
  }
}

const onActived=()=>{
	if(formData.value.actived=='1'){
		formData.value.activeDate=dayjs().format('YYYY-MM-DD HH:mm:ss')
		formData.value.expDate=dayjs().add(3, 'year').format('YYYY-MM-DD HH:mm:ss')
	}
}
// 关闭抽屉
const onClose = () => {
  formRef.value.resetFields()
  formData.value = {}
  dynamicFormData.value = {}
  open.value = false
}
// 默认要校验的
const formRules = {
  prefix: [required('Please enter prefix')],
  startNo: [required('Please enter start number')],
}
// 验证并提交数据
const onSubmit = () => {
  const promiseList = []
  promiseList.push(
      new Promise((resolve, reject) => {
        formRef.value
            .validate()
            .then((result) => {
              resolve(result)
            })
            .catch((err) => {
              reject(err)
            })
      })
  )
  promiseList.push(
      new Promise((resolve, reject) => {
        dynamicFormRef.value
            .validate()
            .then((result) => {
              resolve(result)
            })
            .catch((err) => {
              reject(err)
            })
      })
  )
  submitLoading.value = true
  Promise.all(promiseList)
      .then(() => {
        const formDataParam = cloneDeep(formData.value)
        formDataParam.extJson = JSON.stringify(dynamicFormData.value)
        bizGiftCardApi
            .bizGiftCardSubmitForm(formDataParam, formDataParam.id)
            .then(() => {
              onClose()
              emit('successful')
            })
            .finally(() => {
              submitLoading.value = false
            })
      })
      .catch(() => {
      })
}
// 抛出函数
defineExpose({
  onOpen
})
</script>
