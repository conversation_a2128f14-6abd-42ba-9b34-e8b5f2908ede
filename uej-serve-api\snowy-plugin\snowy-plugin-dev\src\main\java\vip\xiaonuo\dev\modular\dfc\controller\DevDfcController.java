
package vip.xiaonuo.dev.modular.dfc.controller;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.dev.modular.dfc.entity.DevDfc;
import vip.xiaonuo.dev.modular.dfc.param.*;
import vip.xiaonuo.dev.modular.dfc.result.DevDfcDbsSelectorResult;
import vip.xiaonuo.dev.modular.dfc.service.DevDfcService;

import javax.validation.Valid;
import java.util.List;

/**
 * 动态字段配置控制器
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 */
@Tag(name = "动态字段配置控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class DevDfcController {

    @Resource
    private DevDfcService devDfcService;

    /**
     * 获取动态字段配置分页
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取动态字段配置分页")
    @GetMapping("/dev/dfc/page")
    public CommonResult<Page<DevDfc>> page(DevDfcPageParam devDfcPageParam) {
        return CommonResult.data(devDfcService.page(devDfcPageParam));
    }

    /**
     * 添加动态字段配置
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 2)
    @Operation(summary = "添加动态字段配置")
    @CommonLog("添加动态字段配置")
    @PostMapping("/dev/dfc/add")
    public CommonResult<String> add(@RequestBody @Valid DevDfcAddParam devDfcAddParam) {
        devDfcService.add(devDfcAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑动态字段配置
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 3)
    @Operation(summary = "编辑动态字段配置")
    @CommonLog("编辑动态字段配置")
    @PostMapping("/dev/dfc/edit")
    public CommonResult<String> edit(@RequestBody @Valid DevDfcEditParam devDfcEditParam) {
        devDfcService.edit(devDfcEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除动态字段配置
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 4)
    @Operation(summary = "删除动态字段配置")
    @CommonLog("删除动态字段配置")
    @PostMapping("/dev/dfc/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<DevDfcIdParam> devDfcIdParamList) {
        devDfcService.delete(devDfcIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取动态字段配置详情
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 5)
    @Operation(summary = "获取动态字段配置详情")
    @GetMapping("/dev/dfc/detail")
    public CommonResult<DevDfc> detail(@Valid DevDfcIdParam devDfcIdParam) {
        return CommonResult.data(devDfcService.detail(devDfcIdParam));
    }

    /**
     * 迁移数据
     *
     * <AUTHOR>
     * @date 2023/8/7 22:53
     */
    @ApiOperationSupport(order = 6)
    @Operation(summary = "迁移数据")
    @PostMapping("/dev/dfc/migrate")
    public CommonResult<String> migrate(@RequestBody @Valid DevDfcMigrateParam devDfcMigrateParam) {
        devDfcService.migrate(devDfcMigrateParam);
        return CommonResult.ok();
    }

    /* ====动态字段部分所需要用到的选择器==== */
    /**
     * 获取所有数据源信息
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 7)
    @Operation(summary = "获取所有数据源信息")
    @GetMapping("/dev/dfc/dbsSelector")
    public CommonResult<List<DevDfcDbsSelectorResult>> dbsSelector() {
        return CommonResult.data(devDfcService.dbsSelector());
    }

    /**
     * 根据数据源对应库所有表信息
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 8)
    @Operation(summary = "获取当前库所有表信息")
    @GetMapping("/dev/dfc/dbTableSelector")
    public CommonResult<List<JSONObject>> dbTableSelector(@Valid DevDfcDbTableSelectorParam devDfcDbTableSelectorParam) {
        return CommonResult.data(devDfcService.dbTableSelector(devDfcDbTableSelectorParam));
    }

    /**
     * 获取对应库数据表内所有字段信息
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 9)
    @Operation(summary = "获取当前库数据表内所有字段信息")
    @GetMapping("/dev/dfc/dbColumnSelector")
    public CommonResult<List<JSONObject>> dbColumnSelector(@Valid DevDfcDbColumnSelectorParam devDfcDbColumnSelectorParam) {
        return CommonResult.data(devDfcService.dbColumnSelector(devDfcDbColumnSelectorParam));
    }

}
