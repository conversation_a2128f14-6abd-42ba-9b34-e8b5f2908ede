
package vip.xiaonuo.biz.modular.offering.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.trans.service.impl.DictionaryTransService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.biz.modular.dict.entity.BizDict;
import vip.xiaonuo.biz.modular.dict.enums.BizDictCategoryEnum;
import vip.xiaonuo.biz.modular.dict.service.BizDictService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.offering.entity.BizOffering;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingAddParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingEditParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingIdParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingPageParam;
import vip.xiaonuo.biz.modular.offering.service.BizOfferingService;
import cn.hutool.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务或产品清单控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 16:09
 */
@Tag(name = "服务或产品清单控制器")
@RestController
@Validated
public class BizOfferingController {

    @Resource
    private BizOfferingService bizOfferingService;

    @Resource
    private BizDictService bizDictService;

    /**
     * 获取服务或产品清单分页
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    @Operation(summary = "获取服务或产品清单分页")
    @SaCheckPermission("/biz/offering/page")
    @GetMapping("/biz/offering/page")
    public CommonResult<Page<BizOffering>> page(BizOfferingPageParam bizOfferingPageParam) {
        return CommonResult.data(bizOfferingService.page(bizOfferingPageParam));
    }

    @Operation(summary = "获取服务或产品清单列表")
    @SaCheckPermission("/biz/offering/list")
    @GetMapping("/biz/offering/list")
    public CommonResult<List<BizOffering>> list() {
        return CommonResult.data(bizOfferingService.list());
    }

    /**
     * 添加服务或产品清单
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    @Operation(summary = "添加服务或产品清单")
    @CommonLog("添加服务或产品清单")
    @SaCheckPermission("/biz/offering/add")
    @PostMapping("/biz/offering/add")
    public CommonResult<String> add(@RequestBody @Valid BizOfferingAddParam bizOfferingAddParam) {
        bizOfferingService.add(bizOfferingAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑服务或产品清单
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    @Operation(summary = "编辑服务或产品清单")
    @CommonLog("编辑服务或产品清单")
    @SaCheckPermission("/biz/offering/edit")
    @PostMapping("/biz/offering/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizOfferingEditParam bizOfferingEditParam) {
        bizOfferingService.edit(bizOfferingEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除服务或产品清单
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    @Operation(summary = "删除服务或产品清单")
    @CommonLog("删除服务或产品清单")
    @SaCheckPermission("/biz/offering/delete")
    @PostMapping("/biz/offering/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizOfferingIdParam> bizOfferingIdParamList) {
        bizOfferingService.delete(bizOfferingIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取服务或产品清单详情
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    @Operation(summary = "获取服务或产品清单详情")
    @SaCheckPermission("/biz/offering/detail")
    @GetMapping("/biz/offering/detail")
    public CommonResult<BizOffering> detail(@Valid BizOfferingIdParam bizOfferingIdParam) {
        return CommonResult.data(bizOfferingService.detail(bizOfferingIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    @Operation(summary = "获取服务或产品清单动态字段的配置")
    @SaCheckPermission("/biz/offering/dynamicFieldConfigList")
    @GetMapping("/biz/offering/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizOfferingService.dynamicFieldConfigList(columnName));
    }

    @Operation(summary = "导入商品信息")
    @PostMapping("/biz/offering/importInfo")
    public CommonResult<String> importInfo(@RequestPart("file") MultipartFile file) throws IOException {
        /*//根据url链接下载文件
        HttpResponse response = HttpUtil.createGet(bizOfferingPageParam.getUrl()).execute();
        //写入文件，存放到根目录下
        String fileName = "offering.xlsx";
        File downloadedFile = new File(System.getProperty("user.dir"), fileName);
        FileUtil.writeBytes(response.bodyBytes(), downloadedFile);
        //解析文件
        List<Map<String, Object>> file = ExcelUtil.getReader(downloadedFile).readAll();*/

        File tempFile = FileUtil.writeBytes(file.getBytes(), FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + "userImportTemplate.xlsx"));
        List<Map<String, Object>> fileMap = ExcelUtil.getReader(tempFile).readAll();
        String name = tempFile.getName();

        List<BizOffering> offerings = new ArrayList<>();
        for (Map<String, Object> map : fileMap) {
            BizOffering offering = new BizOffering();
            offering.setCategory(map.keySet().contains("Type") && ObjectUtil.isNotEmpty(map.get("Type")) && "SERVICE, PARTS".contains(map.get("Type").toString()) ? map.get("Type").toString() : "SERVICE");
            offering.setName(map.keySet().contains("Name") && ObjectUtil.isNotEmpty(map.get("Name")) ? map.get("Name").toString() : "-");
            offering.setInvoiceHours(map.keySet().contains("Invoice Hours") && ObjectUtil.isNotEmpty(map.get("Invoice Hours")) ? map.get("Invoice Hours").toString() : "01:00");
            offering.setPlanningHours(map.keySet().contains("Planning Hours") && ObjectUtil.isNotEmpty(map.get("Planning Hours")) ? map.get("Planning Hours").toString() : "01:00");
            offering.setListPrice(map.keySet().contains("List Price") && ObjectUtil.isNotEmpty(map.get("List Price")) ? map.get("List Price").toString() : "1");
            offering.setCostPrice(map.keySet().contains("Cost Price") && ObjectUtil.isNotEmpty(map.get("Cost Price")) ? map.get("Cost Price").toString() : "1");
            offering.setOrgId(StpLoginUserUtil.getLoginUser().getOrgId());
            offerings.add(offering);
        }
        bizOfferingService.saveBatch(offerings);
        tempFile.delete();
        return CommonResult.ok(name);
    }

    @Operation(summary = "获取产品分类")
    @GetMapping("/biz/offering/getType")
    public CommonResult<List<Tree<String>>> dynamicFieldConfigList() {
        List<BizDict> bizDictList = bizDictService.list(new LambdaQueryWrapper<BizDict>().eq(BizDict::getCategory, BizDictCategoryEnum.BIZ.getValue()).orderByAsc(BizDict::getSortCode));

        List<TreeNode<String>> treeNodeList = bizDictList.stream().map(bizDict ->
                        new TreeNode<>(bizDict.getId(), bizDict.getParentId(),
                                bizDict.getDictLabel(), bizDict.getSortCode()).setExtra(JSONUtil.parseObj(bizDict))).toList();
        System.err.println("size1: " + treeNodeList.size());
        List<Tree<String>> build = TreeUtil.build(treeNodeList, "0");
        List<Tree<String>> result = build.stream().filter(o -> o.getId().equals("1851910769177935874")).toList();
        System.err.println("size2: " + result.size());
        return CommonResult.data(result);
    }
}
