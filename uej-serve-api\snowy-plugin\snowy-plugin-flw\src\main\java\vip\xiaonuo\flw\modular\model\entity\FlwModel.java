
package vip.xiaonuo.flw.modular.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * 模型实体
 *
 * <AUTHOR>
 * @date 2022/5/11 10:38
 **/
@Getter
@Setter
@TableName("ACT_EXT_MODEL")
public class FlwModel extends CommonEntity {

    /** id */
    @Schema(description = "主键")
    private String id;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 定义id */
    @Schema(description = "定义id")
    private String definitionId;

    /** 部署id */
    @Schema(description = "部署id")
    private String deploymentId;

    /** 管理员id */
    @Schema(description = "管理员id")
    @Trans(type = TransType.RPC, targetClassName = "vip.xiaonuo.sys.modular.user.entity.SysUser", fields = "name", alias = "admin", ref = "adminName")
    private String adminId;

    /** 管理员名称 */
    @Schema(description = "管理员名称")
    @TableField(exist = false)
    private String adminName;

    /** 名称 */
    @Schema(description = "名称")
    private String name;

    /** 编码 */
    @Schema(description = "编码")
    private String code;

    /** 版本 */
    @Schema(description = "版本")
    private String versionCode;

    /** 类型（自定义表单 设计表单） */
    @Schema(description = "类型")
    private String formType;

    /** 分类 */
    @Schema(description = "分类")
    private String category;

    /** 图标 */
    @Schema(description = "图标")
    private String icon;

    /** 移动端图标 */
    @Schema(description = "移动端图标")
    private String iconMobile;

    /** 颜色 */
    @Schema(description = "颜色")
    private String color;

    /** 数据库表JSON */
    @Schema(description = "数据库表JSON")
    private String tableJson;

    /** 表单JSON */
    @Schema(description = "表单JSON")
    private String formJson;

    /** 表单URL（暂未使用） */
    @Schema(description = "表单URL")
    private String formUrl;

    /** 流程JSON */
    @Schema(description = "流程JSON")
    private String processJson;

    /** 流程XML */
    @Schema(description = "流程XML")
    private String processXml;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;

    /** 模型状态 */
    @Schema(description = "模型状态")
    private String modelStatus;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;
}
