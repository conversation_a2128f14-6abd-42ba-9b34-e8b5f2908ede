<template>
	<a-row :gutter="{ xs: 0, md: 8 }" class="mb-[10px]">
		<a-col :xs="24" :sm="24" :md="8" :lg="6">
			<a-calendar v-model:value="searchFormState.date" :fullscreen="false" @select="dateChange" class="calendar">
				<template #headerRender="{ value: current, type, onChange, onTypeChange }">
					<div style="padding: 10px">
						<a-row type="flex" justify="space-between">
							<a-col>
								<a-tree-select
									size="small"
									v-model:value="searchFormState.orgId"
									:tree-data="orgList"
									:field-names="{ children: 'children', label: 'name', value: 'id' }"
									tree-default-expand-all
									@change="orgChange"
									style="width: 200px"
									placeholder="请选择组织"
								></a-tree-select>
							</a-col>
							<a-col>
								<a-select
									size="small"
									:dropdown-match-select-width="false"
									class="my-year-select"
									:value="String(current.year())"
									@change="
										(newYear) => {
											onChange(current.year(+newYear))
										}
									"
								>
									<a-select-option v-for="val in getYears(current)" :key="String(val)" class="year-item">
										{{ val }}
									</a-select-option>
								</a-select>
							</a-col>
							<a-col>
								<a-select
									size="small"
									:dropdown-match-select-width="false"
									:value="String(current.month())"
									@change="
										(selectedMonth) => {
											onChange(current.month(parseInt(String(selectedMonth), 10)))
										}
									"
								>
									<a-select-option v-for="(val, index) in getMonths(current)" :key="String(index)" class="month-item">
										{{ val }}
									</a-select-option>
								</a-select>
							</a-col>
						</a-row>
					</div>
				</template>
			</a-calendar>
		</a-col>
		<a-col :xs="24" :sm="24" :md="16" :lg="18">
			<a-card bodyStyle="padding:0 10px;" class="h-full">
				<template #title>
					<a-tabs v-model:activeKey="activeKey" :tabBarStyle="{ padding: '0', marginBottom: '8px' }">
						<a-tab-pane :key="tabs.key" :tab="$t(tabs.label)" v-for="tabs in tabList"></a-tab-pane>
					</a-tabs>
				</template>
				<a-table
					v-if="activeKey == '1'"
					:dataSource="dataSource"
					:columns="columns"
					:pagination="{ hideOnSinglePage: true }"
					size="small"
					:scroll="{ y: 208 }"
				>
					<template #headerCell="{ title, column }">
						<span>{{ $t(`table.${column.title}`) }}</span>
					</template>
				</a-table>
				<a-row v-if="activeKey == '2'"></a-row>
				<a-row v-if="activeKey == '3'">
					<a-col :span="8" v-for="item in Object.keys(legendList)" :key="item" class="flex items-center mb-[10px]">
						<div class="w-[16px] h-[16px]" :style="{ backgroundColor: legendList[item] }"></div>
						{{ $t('dict.' + $TOOL.dictTypeData('TASK_STATE', item)) }}
					</a-col>
				</a-row>
			</a-card>
		</a-col>
	</a-row>

	<FullCalendar ref="fullCalendarRef" :options="calendarOptions" class="h-[600px] bg-white" v-if="fullCalendarLoading">
		<template v-slot:eventContent="arg">
			<div class="text-[14px] whitespace-nowrap overflow-hidden font-4">{{ arg.event.title }}</div>
			<div class="text-[12px] whitespace-nowrap overflow-hidden">
				{{ dayjs(arg.event.start).format('HH:mm:ss') }}-{{ dayjs(arg.event.end).format('HH:mm:ss') }}
			</div>
		</template>
	</FullCalendar>
	<div class="bg-white leading-[40px] w-full text-[14px] border-1 border-[#e8e8e8] border-solid px-[10px]">
		Total Price：{{ totalLeftPrice }} <span class="ml-4">Total Tip: {{ totalLeftTips }}</span>
	</div>

	<Menu
		:x="menuX"
		:y="menuY"
		:visible="menuVisible"
		@menu-item="handleItemClick"
		:data="menus"
		v-click-outside="onClickOutside"
	/>
	<Form ref="formRef" @success="loadData"></Form>
	<TaskForm ref="taskFormRef" @successful="loadData"></TaskForm>
	<a-modal v-model:open="deptVisible" width="400px" title="choose dept" :maskClosable="false" :closable="false">
		<a-tree-select
			v-model:value="searchFormState.orgId"
			:tree-data="orgList"
			:field-names="{ children: 'children', label: 'name', value: 'id' }"
			tree-default-expand-all
			@change="orgChange"
			style="width: 300px"
			placeholder="请选择组织"
		></a-tree-select>
		<template #footer>
			<a-button type="primary" @click="deptChooseConfirm">确认</a-button>
		</template>
	</a-modal>
</template>

<script setup name="processTiming">
	import { cloneDeep } from 'lodash-es'
	import workerTimer from '@/layout/worker'
	import FullCalendar from '@fullcalendar/vue3'
	import dayGridPlugin from '@fullcalendar/daygrid'
	import timeGridPlugin from '@fullcalendar/timegrid'
	import resourceTimelinePlugin from '@fullcalendar/resource-timeline'
	import tippy from 'tippy.js'
	import dayjs from 'dayjs'
	import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
	import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
	dayjs.extend(isSameOrBefore)
	dayjs.extend(isSameOrAfter)

	import Menu from './menu.vue'
	import Form from './form.vue'
	import TaskForm from '@/views/biz/task/form.vue'
	import bizTaskApi from '@/api/biz/bizTaskApi'
	import sysConfig from '@/config/index'
	import bizTaskDataStatisticsApi from '@/api/biz/bizTaskDataStatisticsApi'
	import bizScheduleApi from '@/api/biz/bizScheduleApi'
	import bizConfigApi from '@/api/biz/bizConfigApi'
	import bizOrgApi from '@/api/biz/bizOrgApi'
	import tool from '@/utils/tool'
	import sysConfigApi from '@/api/dev/configApi'
	import { getCurrentInstance, h, render } from 'vue'
	import { useGlobalStore } from '@/store'

	const fullCalendarRef = ref(null)
	const fullCalendarLoading = ref(true)
	const useStore = useGlobalStore()
	const formRef = ref()
	const taskFormRef = ref()
	const searchFormState = ref({
		orgId: useStore.userInfo.orgId
	})

	const menuVisible = ref(false)
	const deptVisible = ref(false)
	const orgList = ref([])
	const menuX = ref(0)
	const menuY = ref(0)
	const timer = ref(null)
	const { proxy } = getCurrentInstance()
	let observer = null

	const formData = ref({})
	const totalLeftPrice = ref(0)
	const totalLeftTips = ref(0)

	const resources = ref([])
	const events = ref([])
	const menus = ref([
		{
			key: 'TASK_STATE_PROGRESS',
			label: 'common.continue',
			title: 'continue'
		},
		{
			key: 'TASK_STATE_STOP',
			label: 'common.pause',
			title: 'pause'
		},
		{
			key: 'TASK_STATE_SWITCHING',
			label: 'common.switching',
			title: 'switching'
		},
		{
			key: 'delete',
			label: 'common.removeButton',
			title: 'task_delete'
		}
	])

	const columns = [
		{
			title: 'TherapistQueue',
			dataIndex: 'staffName'
		},
		{
			title: 'Finish',
			dataIndex: 'work'
		},
		{
			title: 'Free',
			dataIndex: 'free'
		},
		{
			title: 'Next',
			dataIndex: 'next'
		}
	]

	// 默认图例配置，用于权限不足时的备用显示
	const defaultLegendList = {
		TASK_STATE_NEW: '#d9534f',
		TASK_STATE_STOP: '#f0ad4e',
		TASK_STATE_PROGRESS: '#8bc34a',
		TASK_STATE_FINISH: '#666666',
		TASK_STATE_PAID: '#00bcd4',
		TASK_STATE_CANCELLED: '#444',
		TASK_STATE_REMIND: '#444'
	}

	const legendList = ref({
		TASK_STATE_NEW: '#d9534f',
		TASK_STATE_STOP: '#f0ad4e',
		TASK_STATE_PROGRESS: '#8bc34a',
		TASK_STATE_FINISH: '#666666',
		TASK_STATE_PAID: '#00bcd4',
		TASK_STATE_CANCELLED: '#444',
		TASK_STATE_REMIND: '#444'
	})

	const dataSource = ref([])
	const tabList = ref([
		{
			key: '1',
			label: 'common.queue'
		},
		{
			key: '3',
			label: 'common.legend'
		}
	])
	const activeKey = ref('1')

	const getMonths = (value) => {
		const localeData = value.localeData()
		const months = []
		for (let i = 0; i < 12; i++) {
			months.push(localeData.monthsShort(value.month(i)))
		}
		return months
	}
	const getYears = (value) => {
		const year = value.year()
		const years = []
		for (let i = year - 10; i < year + 10; i += 1) {
			years.push(i)
		}
		return years
	}

	const vClickOutside = {
		mounted(el, binding) {
			function eventHandler(e) {
				if (el.contains(e.target)) {
					return false
				}
				// 如果绑定的参数是函数，正常情况也应该是函数，执行
				if (binding.value && typeof binding.value === 'function') {
					binding.value(e)
				}
			}
			// 用于销毁前注销事件监听
			el.__click_outside__ = eventHandler
			// 添加事件监听
			document.addEventListener('click', eventHandler)
		},
		beforeUnmount(el) {
			// 移除事件监听
			document.removeEventListener('click', el.__click_outside__)
			// 删除无用属性
			delete el.__click_outside__
		}
	}

	const orgChange = (value, options) => {
		loadData()
	}

	const deptChooseConfirm = () => {
		if (searchFormState.value.orgId) {
			deptVisible.value = false
		}
	}

	const onClickOutside = () => {
		menuVisible.value = false
	}
	const handleItemClick = (e) => {
		menuVisible.value = false
		if (e == 'TASK_STATE_SWITCHING') {
			formRef.value.onOpen({ taskId: formData.value.id, mainId: formData.value.mainId })
		} else if (e == 'delete') {
			bizTaskDataStatisticsApi.bizTaskDataStatisticsDelete([{ id: formData.value.statistics }]).then(() => {
				loadData()
			})
		} else {
			bizTaskDataStatisticsApi
				.bizTaskDataStatisticsStatusChange(Object.assign({ status: e }, formData.value))
				.then((res) => {
					loadData()
				})
		}
	}

	const disabledDate = (current) => {
		return current && current > dayjs().endOf('day')
	}

	const dateChange = (value) => {
		loadData()
	}

	const loadData = () => {
		const searchFormParam = cloneDeep(searchFormState.value)
		searchFormParam.date = dayjs(searchFormParam.date).format('YYYY-MM-DD')
		const promises = []

		promises.push(
			new Promise((resolve, reject) => {
				bizScheduleApi
					.bizGetScheduledStaff({ organizationId: searchFormParam.orgId, time: searchFormParam.date })
					.then((res) => {
						dataSource.value = res
						resolve(res)
					})
					.catch(() => {
						reject()
					})
			})
		)

		Promise.all(promises).then((res) => {
			sysConfigApi.configPage({ current: 1, size: 100, parentBizKey: 'legend' }).then((res) => {
				const taskStateEntries = res.records
					.filter((item) => item.configKey.includes('TASK_STATE'))
					.map((item) => [item.configKey, item.configValue])
				
				// 如果从API获取到了配置数据，使用API数据；否则使用默认配置
				const apiLegendList = Object.fromEntries(taskStateEntries)
				if (Object.keys(apiLegendList).length > 0) {
					legendList.value = apiLegendList
				} else {
					legendList.value = defaultLegendList
				}

				bizTaskDataStatisticsApi.bizTaskDataStatisticsGetGantt(searchFormParam).then((res) => {
					let leftResources =
						dataSource.value?.map((item, index) => {
							return {
								id: item.staffId,
								title: item.staffName,
								color: item.color,
								money: item.money,
								tip: item.tip,
								businessHours: {
									daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
									startTime: JSON.parse(item.workTime)[0],
									endTime: JSON.parse(item.workTime)[1]
								}
							}
						}) || []

					totalLeftPrice.value = dataSource.value?.reduce((acc, cur) => acc + cur.money, 0) || 0
					totalLeftTips.value = dataSource.value?.reduce((acc, cur) => acc + cur.tip, 0) || 0
					let rightEvents =
						res?.map((item, index) => {
							return {
								...item,
								resourceId: item.staffId,
								title: item.staff + '_' + dayjs(item.startTime).format('YYYY-MM-DD'),
								start: dayjs(item.startTime).toISOString(),
								end: dayjs(item.planningEndTime).toISOString() || dayjs().toISOString(),
								backgroundColor: legendList.value[item.status],
								taskId: item.id
							}
						}) || []

					const hasStaff = rightEvents.some((item) => item.staffId == 'Unregistered')
					if (hasStaff) {
						leftResources.push({
							id: 'Unregistered',
							title: 'Unregistered'
						})
					}

					if (leftResources.length == 0) {
						leftResources.push({
							id: 'Unregistered',
							title: 'Unregistered'
						})
					}

					calendarOptions.value.resources = leftResources
					calendarOptions.value.events = rightEvents



					// #region 重新渲染个人price
					fullCalendarLoading.value = false
					setTimeout(() => {


						fullCalendarLoading.value = true



					}, 100)
					// #endregion


					setTimeout(() => {
						fullCalendarRef.value.getApi().gotoDate(dayjs(searchFormParam.date).format('YYYY-MM-DD'))
					},500)


					observer = new ResizeObserver((entries) => {
						let height1, height2
						entries.forEach((entry) => {
							if (entry.target.classList.contains('fc-timeline-lane-frame')) {
								height1 = entry.contentRect.height
							} else if (entry.target.classList.contains('fc-datagrid-cell-frame')) {
								height2 = entry.contentRect.height
							}

							if (height1 && height2) {
								if (height1 !== height2) {
									fullCalendarLoading.value = false
									setTimeout(() => {
										fullCalendarLoading.value = true
									}, 100)
								}
							}
						})
					})
					const timelineLaneFrame = document.querySelector('.fc-timeline-lane-frame')
					const datagridCellFrame = document.querySelector('.fc-resource .fc-datagrid-cell-frame')
					if (timelineLaneFrame && datagridCellFrame) {
						observer.observe(timelineLaneFrame)
						observer.observe(datagridCellFrame)
					}
				})
			}).catch((error) => {
				// API调用失败时使用默认配置
				console.warn('获取图例配置失败，使用默认配置:', error)
				legendList.value = defaultLegendList
			})
		})
	}

	const addTask = (res) => {
		if (res) {
			taskFormRef.value?.onOpen({ date: dayjs(searchFormState.value.date).format('YYYY-MM-DD'), assign: res.id })
		} else {
			taskFormRef.value?.onOpen({ date: dayjs(searchFormState.value.date).format('YYYY-MM-DD') })
		}
	}

	const handleDblClick = (e) => {
		const now = Date.now()
		const lastClickTime = e.el.lastClickTime || 0
		e.el.lastClickTime = now
		const timeDiff = now - lastClickTime
		if (timeDiff < 300) {
			const { mainId, taskId } = e.event._def.extendedProps
			taskFormRef.value?.onOpen({ id: mainId, mainId: taskId })
		}
	}

	const screenWidth = ref('10%')
	const getWindowInfo = () => {
		if (window.innerWidth < 900) {
			screenWidth.value = '30%'
		}
	}
	window.addEventListener('resize', getWindowInfo)

	const calendarOptions = ref({
		plugins: [dayGridPlugin, timeGridPlugin, resourceTimelinePlugin],
		initialView: 'resourceTimelineFourDays',
		eventDidMount: function (arg) {
			const { mainId, taskId, status } = arg.event._def.extendedProps

			arg.el.addEventListener('contextmenu', (jsEvent) => {
				jsEvent.preventDefault()
				menuX.value = jsEvent.clientX + document.body.scrollLeft + document.documentElement.scrollLeft
				menuY.value = jsEvent.clientY + document.body.scrollTop + document.documentElement.scrollTop

				formData.value.id = mainId
				formData.value.mainId = mainId
				formData.value.statistics = taskId
				menuVisible.value = true
			})
		},
		eventMouseEnter: function (info) {
			let eve = info.event._def.extendedProps

			let product = JSON.parse(eve.product || null)

			let tooltipHtml = '<table style="border: 1px solid black; width: 300px;">'
			tooltipHtml += `<tr><td>card</td><td style="width: 50%; text-align: right;"> ${eve.card || 0}</td></tr>
				<tr><td>cash</td><td style="width: 50%; text-align: right;"> ${eve.cash || 0}</td></tr>
				<tr><td>giftCard</td><td style="width: 50%; text-align: right;"> ${eve.giftCard || 0}</td></tr>
				<tr><td>voucher</td><td style="width: 50%; text-align: right;"> ${eve.voucher || 0}</td></tr>
				<tr><td>insurance</td><td style="width: 50%; text-align: right;"> ${eve.insurance || 0}</td></tr>
				<tr><td>tip</td><td style="width: 50%; text-align: right;"> ${eve.tip || 0}</td></tr>
				`
			if (product) {
				for (let item of product) {
					tooltipHtml +=
						'<tr><td style="width: 50%;">' +
						item.productName +
						'</td><td style="width: 50%; text-align: right;">' +
						item.planningHours +
						'</td></tr>'
				}
			}
			tooltipHtml += '</table>'

			tippy(info.el, {
				content: tooltipHtml,

				arrow: true,
				// 鼠标放在提示中提示不消失
				interactive: true,
				appendTo: document.body,
				animation: 'scale', //显示动画
				theme: 'light', //显示主题
				allowHTML: true //是否允许html文本
			})
		},
		eventChange: function (arg) {},
		views: {
			resourceTimelineFourDays: {
				type: 'resourceTimeline',
				duration: { days: 1 },
				slotMinTime: '06:00:00',
				slotMaxTime: '24:00:00',
				slotLabelFormat: [{ hour: 'numeric', minute: '2-digit', omitZeroMinute: true, meridiem: 'short' }]
			}
		},

		resourceAreaWidth: '14%',
		headerToolbar: false,
		timezone: 'local',
		editable: false, // 是否可以进行（拖动、缩放）修改
		eventStartEditable: false, // Event日程开始时间可以改变，默认true，如果是false其实就是指日程块不能随意拖动，只能上下拉伸改变他的endTime
		eventDurationEditable: false, // Event日程的开始结束时间距离是否可以改变，默认true，如果是false则表示开始结束时间范围不能拉伸，只能拖拽
		selectable: true, // 是否可以选中日历格
		weekends: true, // 是否在任何日历视图中包括周六/周日列。
		navLinks: false, // 确定日名和周名是否可单击
		eventClick: function (e) {
			const now = Date.now()
			const lastClickTime = e.el.lastClickTime || 0
			e.el.lastClickTime = now
			const timeDiff = now - lastClickTime
			if (timeDiff < 300) {
				const { mainId, taskId } = e.event._def.extendedProps
				taskFormRef.value?.onOpen({ id: mainId, mainId: taskId })
			} else {
				return false
			}
		},
		resourceAreaHeaderContent: '',
		schedulerLicenseKey: 'GPL-My-Project-Is-Open-Source', // 此配置是为了消除右下角的版权提示
		eventOverlap: false,
		nowIndicator: true,
		resourceLabelDidMount: function (info) {
			info.el.style.color = info.resource._resource.extendedProps?.color || '#333'
			let aButton = h(
				'button',
				{
					class: 'add-btn',
					onClick: () => addTask(info.resource._resource)
				},
				proxy.$t('common.addButton')
			)

			const button = info.el.querySelector('.fc-datagrid-cell-main')
			const toolBox = info.el.querySelector('.fc-datagrid-cell-cushion')

			const PriceTip = {
				render() {
					return h('div', { class: 'price-tip flex flex-col h-full' }, [
						h('div', null, `price:${info.resource._resource.extendedProps?.money || 0}`),
						h('div', null, `tip:${info.resource._resource.extendedProps?.tip || 0}`)
					])
				}
			}

			if (!toolBox.querySelector('div.price-tip')) {
				render(h(PriceTip), toolBox)
			}

			toolBox.style.cssText = 'display: flex; flex-direction: row; align-items: flex-start;'
			button.style.cssText = 'display: flex; flex-direction: column; align-items: flex-start;flex:1'
			render(aButton, button)

			//左上角添加按钮
			let hButton = h(
				'button',
				{
					class: 'add-btn',
					onClick: () => addTask()
				},
				proxy.$t('common.addButton')
			)
			const headerLeftTitle = document.querySelector('.fc-datagrid-cell-cushion')
			if (headerLeftTitle) {
				render(hButton, headerLeftTitle)
			}
		},
		resources: [],
		events: []
	})

	const getOrgNodeById = (tree, id) => {
		for (const node of tree) {
			if (node.id === id) return node
			if (node.children) {
				const found = getOrgNodeById(node.children, id)
				if (found) return found
			}
		}
		return null
	}

	onMounted(() => {
		bizOrgApi.orgTree().then((res) => {
			if (res && res.length > 0) {
				const userOrgId = useStore.userInfo.orgId
				const userOrgNode = getOrgNodeById(res, userOrgId)
				if (userOrgNode) {
					orgList.value = [userOrgNode]
					searchFormState.value.orgId = userOrgId
					loadData()
				}
			}
		})
	})

	onActivated(() => {
		loadData()
		if (!timer.value) {
			timer.value = workerTimer.setInterval(() => {
				loadData()
			}, 60000)
		}
	})

	onUnmounted(() => {
		observer && observer.disconnect()
		observer = null
		workerTimer.clearInterval(timer.value)
		timer.value = null
	})
</script>
<style scoped lang="less">
	:deep(.calendar .ant-radio-group) {
		display: none;
	}

	:deep(.ant-card-head) {
		padding: 0 10px;
	}

	:deep(.fc-timeline-lane-frame, .fc-datagrid-cell-frame) {
		min-height: 52px !important;
	}
</style>

<style>
	.add-btn {
		background-color: white;
		border: 2px solid #e7e7e7;
		color: black;
		text-align: center;
		text-decoration: none;
		display: inline-block;
		font-size: 16px;
		padding: 0 6px;
		transition-duration: 0.4s;
		cursor: pointer;
	}
	.add-btn:hover {
		background-color: #e7e7e7;
	}
</style>
