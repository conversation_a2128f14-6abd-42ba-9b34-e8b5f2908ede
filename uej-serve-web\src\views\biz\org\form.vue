<template>
	<xn-form-container
		:title="formData.id ? 'edit org' : 'add org'"
		:width="550"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>

		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-form-item label="Superior organization：" name="parentId">
				<a-tree-select
					v-model:value="formData.parentId"
					style="width: 100%"
					:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
					placeholder="Please select a parent organization"
					allow-clear
					tree-default-expand-all
					:tree-data="treeData"
					:field-names="{
						children: 'children',
						label: 'name',
						value: 'id'
					}"
					selectable="false"
					tree-line
				/>
			</a-form-item>
			<a-form-item label="Organization name：" name="name">
				<a-input v-model:value="formData.name" placeholder="Please enter the organization name" allow-clear />
			</a-form-item>
			<a-form-item label="Mechanism classification：" name="category">
				<a-select
					v-model:value="formData.category"
					:options="orgCategoryOptions"
					style="width: 100%"
					placeholder="Please select an organization category"
				/>
			</a-form-item>
			<a-form-item label="sort：" name="sortCode">
				<a-input-number style="width: 100%" v-model:value="formData.sortCode" :max="100" />
			</a-form-item>
			<a-form-item label="Designated supervisor：" name="directorId">
				<xn-user-selector
					:org-tree-api="selectorApiFunction.orgTreeApi"
					:user-page-api="selectorApiFunction.userPageApi"
					:user-list-by-id-list-api="selectorApiFunction.checkedUserListApi"
					:radio-model="true"
					v-model:value="formData.directorId"
				/>
			</a-form-item>
		</a-form>
		<a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
			<xn-form-item
				v-for="(item, index) in dynamicFieldConfigList"
				:key="index"
				:index="index"
				:fieldConfig="item"
				:formData="dynamicFormData"
			/>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" :loading="submitLoading" @click="onSubmit">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="bizOrgForm">
	import { required } from '@/utils/formRules'
	import bizOrgApi from '@/api/biz/bizOrgApi'
	import userCenterApi from '@/api/sys/userCenterApi'
	import XnFormItem from '@/components/XnFormItem/index.vue'
	import tool from '@/utils/tool'

	// 定义emit事件
	const emit = defineEmits({ successful: null })
	// 默认是关闭状态
	const visible = ref(false)
	const formRef = ref()
	// 表单数据，也就是默认给一些数据
	const formData = ref({})
	// 定义机构元素
	const treeData = ref([])
	const submitLoading = ref(false)
	// 动态表单
	const dynamicFormRef = ref()
	const dynamicFieldConfigList = ref([])
	const dynamicFormData = ref({})
	// 打开抽屉
	const onOpen = (record, parentId) => {
		visible.value = true
		formData.value = {
			sortCode: 99
		}
		dynamicFormData.value = {}
		bizOrgApi
			.orgDynamicFieldConfigList({
				columnName: 'EXT_JSON'
			})
			.then((data) => {
				dynamicFieldConfigList.value = data
			})
		if (parentId) {
			formData.value.parentId = parentId
		}
		if (record) {
			const param = {
				id: record.id
			}
			bizOrgApi.orgDetail(param).then((data) => {
				formData.value = Object.assign({}, data)
				dynamicFormData.value = JSON.parse(formData.value.extJson) || {}
			})
		}
		// 获取机构树并加入顶级
		bizOrgApi.orgTreeSelector().then((res) => {
			treeData.value = [
				{
					id: 0,
					parentId: '-1',
					name: '顶级',
					children: res
				}
			]
		})
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
	}
	// 默认要校验的
	const formRules = {
		name: [required('请输入机构名称')],
		category: [required('请选择机构分类')],
		sortCode: [required('请选择排序')]
	}
	// 机构分类字典
	const orgCategoryOptions = tool.dictList('ORG_CATEGORY')
	// 验证并提交数据
	const onSubmit = () => {
		const promiseList = []
		promiseList.push(
			new Promise((resolve, reject) => {
				formRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		promiseList.push(
			new Promise((resolve, reject) => {
				dynamicFormRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		Promise.all(promiseList)
			.then(() => {
				submitLoading.value = true
				formData.value.extJson = JSON.stringify(dynamicFormData.value)
				bizOrgApi
					.submitForm(formData.value, formData.value.id)
					.then(() => {
						visible.value = false
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			})
			.catch(() => {})
	}
	// 传递设计器需要的API
	const selectorApiFunction = {
		orgTreeApi: (param) => {
			return bizOrgApi.orgTreeSelector(param).then((data) => {
				return Promise.resolve(data)
			})
		},
		userPageApi: (param) => {
			return bizOrgApi.orgUserSelector(param).then((data) => {
				return Promise.resolve(data)
			})
		},
		checkedUserListApi: (param) => {
			return userCenterApi.userCenterGetUserListByIdList(param).then((data) => {
				return Promise.resolve(data)
			})
		}
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
