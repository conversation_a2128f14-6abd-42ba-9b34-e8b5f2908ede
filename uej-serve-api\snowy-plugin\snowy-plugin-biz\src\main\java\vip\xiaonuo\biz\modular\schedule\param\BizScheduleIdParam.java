
package vip.xiaonuo.biz.modular.schedule.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 排班Id参数
 *
 * <AUTHOR>
 * @date  2024/06/18 16:16
 **/
@Getter
@Setter
public class BizScheduleIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;
}
