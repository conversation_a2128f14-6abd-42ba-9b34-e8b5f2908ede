package vip.xiaonuo.biz.modular.attendance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.attendance.entity.BizAttendance;
import vip.xiaonuo.biz.modular.attendance.mapper.BizAttendanceMapper;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceAddParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceEditParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceIdParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendancePageParam;
import vip.xiaonuo.biz.modular.attendance.service.BizAttendanceService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;

import java.util.List;

/**
 * 考勤记录Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/01 13:54
 **/
@Service
public class BizAttendanceServiceImpl extends ServiceImpl<BizAttendanceMapper, BizAttendance> implements BizAttendanceService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizAttendance> page(BizAttendancePageParam bizAttendancePageParam) {
        QueryWrapper<BizAttendance> queryWrapper = new QueryWrapper<BizAttendance>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizAttendancePageParam.getStaffName())) {
            queryWrapper.lambda().eq(BizAttendance::getStaffName, bizAttendancePageParam.getStaffName());
        }
        if(ObjectUtil.isNotEmpty(bizAttendancePageParam.getOrgName())) {
            queryWrapper.lambda().eq(BizAttendance::getOrgName, bizAttendancePageParam.getOrgName());
        }
        if(ObjectUtil.isAllNotEmpty(bizAttendancePageParam.getSortField(), bizAttendancePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizAttendancePageParam.getSortOrder());
            queryWrapper.orderBy(true, bizAttendancePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizAttendancePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BizAttendance::getCreateTime);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizAttendance::getOrgId, loginUserDataScope);
        } else {
            // 如果没有数据范围权限，只能查看自己的考勤记录
            queryWrapper.lambda().eq(BizAttendance::getStaffId, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizAttendanceAddParam bizAttendanceAddParam) {
        // 校验数据范围
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizAttendanceAddParam.getOrgId())) {
                throw new CommonException("您没有权限在该机构下添加考勤记录，机构id：{}", bizAttendanceAddParam.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能为自己添加考勤记录
            if(!bizAttendanceAddParam.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限为该员工添加考勤记录，员工id：{}", bizAttendanceAddParam.getStaffId());
            }
        }
        
        BizAttendance bizAttendance = BeanUtil.toBean(bizAttendanceAddParam, BizAttendance.class);
        this.save(bizAttendance);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizAttendanceEditParam bizAttendanceEditParam) {
        BizAttendance bizAttendance = this.queryEntity(bizAttendanceEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizAttendance.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的考勤记录，机构id：{}", bizAttendance.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能编辑自己的考勤记录
            if(!bizAttendance.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该员工的考勤记录，员工id：{}", bizAttendance.getStaffId());
            }
        }
        
        BeanUtil.copyProperties(bizAttendanceEditParam, bizAttendance);
        this.updateById(bizAttendance);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizAttendanceIdParam> bizAttendanceIdParamList) {
        List<String> attendanceIdList = CollStreamUtil.toList(bizAttendanceIdParamList, BizAttendanceIdParam::getId);
        if(ObjectUtil.isNotEmpty(attendanceIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            List<BizAttendance> attendanceList = this.listByIds(attendanceIdList);
            
            for(BizAttendance attendance : attendanceList) {
                if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                    if(!loginUserDataScope.contains(attendance.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的考勤记录，机构id：{}", attendance.getOrgId());
                    }
                } else {
                    // 如果没有数据范围权限，只能删除自己的考勤记录
                    if(!attendance.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该员工的考勤记录，员工id：{}", attendance.getStaffId());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(attendanceIdList);
    }

    @Override
    public BizAttendance detail(BizAttendanceIdParam bizAttendanceIdParam) {
        BizAttendance bizAttendance = this.queryEntity(bizAttendanceIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizAttendance.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的考勤记录，机构id：{}", bizAttendance.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能查看自己的考勤记录
            if(!bizAttendance.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该员工的考勤记录，员工id：{}", bizAttendance.getStaffId());
            }
        }
        
        return bizAttendance;
    }

    @Override
    public BizAttendance queryEntity(String id) {
        BizAttendance bizAttendance = this.getById(id);
        if(ObjectUtil.isEmpty(bizAttendance)) {
            throw new CommonException("考勤记录不存在，id值为：{}", id);
        }
        return bizAttendance;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizAttendanceServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizAttendance.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}
