package vip.xiaonuo.biz.modular.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.task.entity.BizTaskGiftCard;

import java.util.List;

/**
 * 任务礼品卡关联表Service接口
 *
 * <AUTHOR>
 * @date  2024/12/24
 **/
public interface BizTaskGiftCardService extends IService<BizTaskGiftCard> {

    /**
     * 根据任务ID获取礼品卡列表
     *
     * @param taskId 任务ID
     * @return 礼品卡列表
     */
    List<BizTaskGiftCard> getByTaskId(String taskId);

    /**
     * 保存任务礼品卡列表
     *
     * @param taskId 任务ID
     * @param giftCards 礼品卡列表
     */
    void saveTaskGiftCards(String taskId, List<BizTaskGiftCard> giftCards);

    /**
     * 删除任务的所有礼品卡
     *
     * @param taskId 任务ID
     */
    void deleteByTaskId(String taskId);
} 