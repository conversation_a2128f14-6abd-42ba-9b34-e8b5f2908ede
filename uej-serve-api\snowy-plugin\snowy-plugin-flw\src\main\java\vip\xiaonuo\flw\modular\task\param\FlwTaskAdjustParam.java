
package vip.xiaonuo.flw.modular.task.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 审批调整参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:45
 */
@Getter
@Setter
public class FlwTaskAdjustParam {

    /** 任务Id */
    @Schema(description = "任务Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 填写数据 */
    @Schema(description = "填写数据")
    private String dataJson;
}
