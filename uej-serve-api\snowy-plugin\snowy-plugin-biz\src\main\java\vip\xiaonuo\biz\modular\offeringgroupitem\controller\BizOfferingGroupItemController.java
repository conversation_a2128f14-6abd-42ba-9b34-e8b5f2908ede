
package vip.xiaonuo.biz.modular.offeringgroupitem.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.offeringgroupitem.entity.BizOfferingGroupItem;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemAddParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemEditParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemIdParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemPageParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.service.BizOfferingGroupItemService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 套餐明细控制器
 *
 * <AUTHOR>
 * @date  2024/06/13 09:58
 */
@Tag(name = "套餐明细控制器")
@RestController
@Validated
public class BizOfferingGroupItemController {

    @Resource
    private BizOfferingGroupItemService bizOfferingGroupItemService;

    /**
     * 获取套餐明细分页
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    @Operation(summary = "获取套餐明细分页")
    @SaCheckPermission("/biz/offeringgroupitem/page")
    @GetMapping("/biz/offeringgroupitem/page")
    public CommonResult<Page<BizOfferingGroupItem>> page(BizOfferingGroupItemPageParam bizOfferingGroupItemPageParam) {
        return CommonResult.data(bizOfferingGroupItemService.page(bizOfferingGroupItemPageParam));
    }

    /**
     * 添加套餐明细
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    @Operation(summary = "添加套餐明细")
    @CommonLog("添加套餐明细")
    @SaCheckPermission("/biz/offeringgroupitem/add")
    @PostMapping("/biz/offeringgroupitem/add")
    public CommonResult<String> add(@RequestBody @Valid BizOfferingGroupItemAddParam bizOfferingGroupItemAddParam) {
        bizOfferingGroupItemService.add(bizOfferingGroupItemAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑套餐明细
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    @Operation(summary = "编辑套餐明细")
    @CommonLog("编辑套餐明细")
    @SaCheckPermission("/biz/offeringgroupitem/edit")
    @PostMapping("/biz/offeringgroupitem/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizOfferingGroupItemEditParam bizOfferingGroupItemEditParam) {
        bizOfferingGroupItemService.edit(bizOfferingGroupItemEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除套餐明细
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    @Operation(summary = "删除套餐明细")
    @CommonLog("删除套餐明细")
    @SaCheckPermission("/biz/offeringgroupitem/delete")
    @PostMapping("/biz/offeringgroupitem/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizOfferingGroupItemIdParam> bizOfferingGroupItemIdParamList) {
        bizOfferingGroupItemService.delete(bizOfferingGroupItemIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取套餐明细详情
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    @Operation(summary = "获取套餐明细详情")
    @SaCheckPermission("/biz/offeringgroupitem/detail")
    @GetMapping("/biz/offeringgroupitem/detail")
    public CommonResult<BizOfferingGroupItem> detail(@Valid BizOfferingGroupItemIdParam bizOfferingGroupItemIdParam) {
        return CommonResult.data(bizOfferingGroupItemService.detail(bizOfferingGroupItemIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    @Operation(summary = "获取套餐明细动态字段的配置")
    @SaCheckPermission("/biz/offeringgroupitem/dynamicFieldConfigList")
    @GetMapping("/biz/offeringgroupitem/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizOfferingGroupItemService.dynamicFieldConfigList(columnName));
    }
}
