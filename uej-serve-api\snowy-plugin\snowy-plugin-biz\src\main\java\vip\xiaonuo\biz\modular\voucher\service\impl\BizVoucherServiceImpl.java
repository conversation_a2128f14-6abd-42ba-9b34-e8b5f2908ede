package vip.xiaonuo.biz.modular.voucher.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.voucher.entity.BizVoucher;
import vip.xiaonuo.biz.modular.voucher.mapper.BizVoucherMapper;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherAddParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherEditParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherIdParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherPageParam;
import vip.xiaonuo.biz.modular.voucher.service.BizVoucherService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.biz.modular.giftcardlog.entity.BizGiftCardLog;
import vip.xiaonuo.biz.modular.giftcardlog.service.BizGiftCardLogService;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.Date;
import java.util.List;

/**
 * 代金券信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 17:17
 **/
@Service
public class BizVoucherServiceImpl extends ServiceImpl<BizVoucherMapper, BizVoucher> implements BizVoucherService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Resource
    private BizGiftCardLogService bizGiftCardLogService;

    @Override
    public Page<BizVoucher> page(BizVoucherPageParam bizVoucherPageParam) {
        QueryWrapper<BizVoucher> queryWrapper = new QueryWrapper<BizVoucher>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizVoucherPageParam.getVoucherNumber())) {
            queryWrapper.lambda().eq(BizVoucher::getVoucherNumber, bizVoucherPageParam.getVoucherNumber());
        }
        if(ObjectUtil.isNotEmpty(bizVoucherPageParam.getPid())) {
            queryWrapper.lambda().eq(BizVoucher::getPid, bizVoucherPageParam.getPid());
        } else {
            queryWrapper.lambda().isNull(BizVoucher::getPid);
        }
        if(ObjectUtil.isAllNotEmpty(bizVoucherPageParam.getSortField(), bizVoucherPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizVoucherPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizVoucherPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizVoucherPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizVoucher::getId);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizVoucher::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizVoucher::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizVoucher::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizVoucherAddParam bizVoucherAddParam) {
        BizVoucher bizVoucher = BeanUtil.toBean(bizVoucherAddParam, BizVoucher.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizVoucher.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizVoucher.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizVoucher);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizVoucherEditParam bizVoucherEditParam) {
        BizVoucher bizVoucher = this.queryEntity(bizVoucherEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查代金券所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizVoucher.getOrgId()) && !loginUserDataScope.contains(bizVoucher.getOrgId())) {
                throw new CommonException("您没有权限编辑该代金券，券号：{}", bizVoucher.getVoucherNumber());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizVoucher.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该代金券，券号：{}", bizVoucher.getVoucherNumber());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该代金券，券号：{}", bizVoucher.getVoucherNumber());
            }
        }
        
        BeanUtil.copyProperties(bizVoucherEditParam, bizVoucher);
        this.updateById(bizVoucher);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizVoucherIdParam> bizVoucherIdParamList) {
        List<String> voucherIdList = CollStreamUtil.toList(bizVoucherIdParamList, BizVoucherIdParam::getId);
        if(ObjectUtil.isNotEmpty(voucherIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizVoucher> voucherList = this.listByIds(voucherIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查代金券所属机构是否在权限范围内
                for(BizVoucher voucher : voucherList) {
                    if(ObjectUtil.isNotEmpty(voucher.getOrgId()) && !loginUserDataScope.contains(voucher.getOrgId())) {
                        throw new CommonException("您没有权限删除该代金券，券号：{}", voucher.getVoucherNumber());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizVoucher voucher : voucherList) {
                        if(!voucher.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该代金券，券号：{}", voucher.getVoucherNumber());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除代金券");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(voucherIdList);
    }

    @Override
    public BizVoucher detail(BizVoucherIdParam bizVoucherIdParam) {
        BizVoucher bizVoucher = this.queryEntity(bizVoucherIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查代金券所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizVoucher.getOrgId()) && !loginUserDataScope.contains(bizVoucher.getOrgId())) {
                throw new CommonException("您没有权限查看该代金券，券号：{}", bizVoucher.getVoucherNumber());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizVoucher.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该代金券，券号：{}", bizVoucher.getVoucherNumber());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该代金券，券号：{}", bizVoucher.getVoucherNumber());
            }
        }
        
        return bizVoucher;
    }

    @Override
    public BizVoucher queryEntity(String id) {
        BizVoucher bizVoucher = this.getById(id);
        if(ObjectUtil.isEmpty(bizVoucher)) {
            throw new CommonException("代金券信息不存在，id值为：{}", id);
        }
        return bizVoucher;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizVoucherServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizVoucher.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }

    @Override
    public String redeemVoucher(String voucherNo, String productPrice) {
        BizVoucher voucher = this.lambdaQuery().eq(BizVoucher::getVoucherNumber, voucherNo).one();
        if (ObjectUtil.isEmpty(voucher)) {
            return "There is no such coupon, please check the number.";
        }
        
        // 校验数据范围权限 - 确保只能使用自己组织的代金券
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查代金券所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(voucher.getOrgId()) && !loginUserDataScope.contains(voucher.getOrgId())) {
                return "You don't have permission to use this coupon.";
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!voucher.getCreateUser().equals(userId)) {
                    return "You don't have permission to use this coupon.";
                }
            } catch (Exception e) {
                return "You don't have permission to use this coupon.";
            }
        }
        
        if (voucher.getStatus().equals("1")) {
            return "The coupon has been used."; //已使用
        }
        if (DateUtil.parse(voucher.getValidityDate(), "yyyy-MM-dd HH:mm:ss").isBefore(DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")))) {
            return "The coupon has expired."; //逾期
        }
        if (Double.parseDouble(voucher.getThreshold()) > Double.parseDouble(productPrice) || ObjectUtil.isEmpty(voucher.getThreshold())) {
            return "It hasn't reached the required threshold."; //未达到最低消费门槛
        }
        voucher.setStatus("1");
        voucher.setUsedDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        this.updateById(voucher);
        return "success" + voucher.getValue();
    }

    @Override
    public void restoreVoucherByTaskId(String taskId) {
        try {
            // 查询该任务关联的voucher使用记录
            List<BizGiftCardLog> voucherLogs = bizGiftCardLogService.lambdaQuery()
                    .eq(BizGiftCardLog::getTaskId, taskId)
                    .eq(BizGiftCardLog::getType, "voucher")
                    .list();

            if (voucherLogs != null && !voucherLogs.isEmpty()) {
                for (BizGiftCardLog voucherLog : voucherLogs) {
                    // 根据voucher号码找到对应的voucher记录
                    BizVoucher voucher = this.lambdaQuery()
                            .eq(BizVoucher::getVoucherNumber, voucherLog.getCardNo())
                            .one();
                    
                    if (voucher != null && "1".equals(voucher.getStatus())) {
                        // 恢复voucher状态为未使用
                        voucher.setStatus("0");
                        voucher.setUsedDate(null);
                        this.updateById(voucher);
                    }
                }
            }
        } catch (Exception e) {
            // 记录错误日志，但不阻止操作
            System.err.println("恢复任务 " + taskId + " 的voucher状态时发生错误: " + e.getMessage());
        }
    }
}
