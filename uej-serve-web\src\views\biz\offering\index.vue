<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Category" name="type">
						<a-tree-select
							v-model:value="searchFormState.type"
							show-search
							style="width: 100%"
							:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
							placeholder="Please Select Category"
							allow-clear
							tree-default-expand-all
							:fieldNames="{ children: 'children', label: 'dictLabel', value: 'dictValue', key: 'id' }"
							:tree-data="categoryList"
							tree-node-filter-prop="label"
						>
						</a-tree-select>
					</a-form-item>
				</a-col>
				<a-col v-if="false" :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Type" name="category">
						<a-input v-model:value="searchFormState.category" placeholder="Please enter Type" />
					</a-form-item>
				</a-col>
				<a-col  :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Name" name="name">
						<a-input v-model:value="searchFormState.name" placeholder="Please enter Name" @pressEnter="tableRef.refresh(true)" />
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-button type="primary" @click="tableRef.refresh(true)">{{ $t('common.searchButton') }}</a-button>
					<a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
				</a-col>
			</a-row>
		</a-form>
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
			:scroll="{ x: 'content' }"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('bizOfferingAdd')">
						<template #icon><plus-outlined /></template>
						{{ $t('common.addButton') }}
					</a-button>
					<xn-batch-delete
						v-if="hasPerm('bizOfferingBatchDelete')"
						:selectedRowKeys="selectedRowKeys"
						@batchDelete="deleteBatchBizOffering"
						:buttonName="$t('common.batchRemoveButton')"
					/>
					<a-button type="primary" @click="templateVisible = true" v-if="hasPerm('bizOfferingImport')">
						<template #icon><plus-outlined /></template>
						{{ $t('common.imports') }}
					</a-button>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a @click="formRef.onOpen(record)" v-if="hasPerm('bizOfferingEdit')">{{ $t('common.editButton') }}</a>
						<a-divider type="vertical" v-if="hasPerm(['bizOfferingEdit', 'bizOfferingDelete'], 'and')" />
						<a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizOffering(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('bizOfferingDelete')">{{
								$t('common.removeButton')
							}}</a-button>
						</a-popconfirm>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh(true)" />
	<templateModal
		v-model:visible="templateVisible"
		:download-url="selectorApiFunction.downloadUrl"
		:upload-url="selectorApiFunction.uploadApi"
		@successful="tableRef.refresh(true)"
	></templateModal>
</template>

<script setup name="offering">
	import { cloneDeep } from 'lodash-es'
	import Form from './form.vue'
	import templateModal from './templateModal.vue'
	import bizOfferingApi from '@/api/biz/bizOfferingApi'
	import bizProductCategoryApi from "@/api/biz/bizProductCategoryApi";
	const searchFormState = ref({})
	const searchFormRef = ref()
	const tableRef = ref()
	const formRef = ref()
	const templateVisible = ref(false)
	const categoryList = ref([])

	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	const columns = [
		{
			title: 'Category',
			dataIndex: 'type'
		},
		{
			title: 'Type',
			dataIndex: 'category'
		},
		{
			title: 'Name',
			dataIndex: 'name'
		},
		{
			title: 'Planning Hours',
			dataIndex: 'planningHours'
		},
		{
			title: 'Invoice Hours',
			dataIndex: 'invoiceHours'
		},
		{
			title: 'List Price',
			dataIndex: 'listPrice'
		},
		{
			title: 'Cost Price',
			dataIndex: 'costPrice'
		}
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['bizOfferingEdit', 'bizOfferingDelete'])) {
		columns.push({
			title: 'action',
			dataIndex: 'action',
			align: 'center',
			width: '150px'
		})
	}
	const selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		// columns数字类型字段加入 needTotal: true 可以勾选自动算账
		alert: {
			show: true,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		const searchFormParam = cloneDeep(searchFormState.value)
		return bizOfferingApi.bizOfferingPage(Object.assign(parameter, searchFormParam)).then((data) => {
			return data
		})
	}

	const getCategoryList = () => {
		/*
		bizOfferingApi.bizOfferingCategoryList().then((data) => {
			categoryList.value = [
				{
					dictLabel: 'Top',
					dictValue: 'Top',
					id: 0,
					children: data[0].children
				}
			]
		})

		 */
		bizProductCategoryApi.bizProductCategoryTree().then((data) => {
			categoryList.value = [
				{
					dictLabel: 'Top',
					dictValue: 'Top',
					id: 0,
					children: data[0].children
				}
			]
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 删除
	const deleteBizOffering = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		bizOfferingApi.bizOfferingDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchBizOffering = (params) => {
		bizOfferingApi.bizOfferingDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}

	const selectorApiFunction = {
		downloadUrl: 'https://oa-wanqi20-1255648699.cos.ap-shanghai.myqcloud.com/2024/10/15/1846114707511132162.xlsx',
		uploadApi: (param) => {
			return bizOfferingApi.bizOfferingImport(param).then((data) => {
				return Promise.resolve(data)
			})
		}
	}

	onMounted(() => {
		getCategoryList()
	})
</script>
