
package vip.xiaonuo.biz.modular.schedule.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleAddParam;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleEditParam;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleIdParam;
import vip.xiaonuo.biz.modular.schedule.param.BizSchedulePageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 排班Service接口
 *
 * <AUTHOR>
 * @date  2024/06/18 16:16
 **/
public interface BizScheduleService extends IService<BizSchedule> {

    /**
     * 获取排班分页
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    Page<BizSchedule> page(BizSchedulePageParam bizSchedulePageParam);

    /**
     * 添加排班
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    void add(BizScheduleAddParam bizScheduleAddParam);

    /**
     * 编辑排班
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    void edit(BizScheduleEditParam bizScheduleEditParam);

    /**
     * 删除排班
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    void delete(List<BizScheduleIdParam> bizScheduleIdParamList);

    /**
     * 获取排班详情
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    BizSchedule detail(BizScheduleIdParam bizScheduleIdParam);

    /**
     * 获取排班详情
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     **/
    BizSchedule queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
