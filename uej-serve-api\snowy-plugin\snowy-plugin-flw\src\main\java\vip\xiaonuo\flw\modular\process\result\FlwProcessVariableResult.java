
package vip.xiaonuo.flw.modular.process.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程变量结果
 *
 * <AUTHOR>
 * @date 2022/5/11 15:51
 **/
@Getter
@Setter
public class FlwProcessVariableResult {

    /** 变量id */
    @Schema(description = "变量id")
    private String id;

    /** 流程实例id */
    @Schema(description = "流程实例id")
    private String processInstanceId;

    /** 执行实例id */
    @Schema(description = "执行实例id")
    private String executionId;

    /** 变量名称 */
    @Schema(description = "变量名称")
    private String name;

    /** 变量类型 */
    @Schema(description = "变量类型")
    private String typeName;

    /** 变量值 */
    @Schema(description = "变量值")
    private String value;
}
