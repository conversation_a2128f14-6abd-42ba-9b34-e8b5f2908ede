
package vip.xiaonuo.biz.modular.insurancecompany.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保险公司添加参数
 *
 * <AUTHOR>
 * @date  2024/08/09 09:42
 **/
@Getter
@Setter
public class BizInsuranceCompanyAddParam {

    /** name */
    @Schema(description = "name")
    private String name;

}
