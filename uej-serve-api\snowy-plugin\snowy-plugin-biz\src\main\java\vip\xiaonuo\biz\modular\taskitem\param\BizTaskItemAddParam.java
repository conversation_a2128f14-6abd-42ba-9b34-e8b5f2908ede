
package vip.xiaonuo.biz.modular.taskitem.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 任务子表添加参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:34
 **/
@Getter
@Setter
public class BizTaskItemAddParam {

    /** 主表id */
    @Schema(description = "主表id")
    private String mainId;

    /** Product Id */
    @Schema(description = "Product Id")
    private String productId;

    /** Product Name */
    @Schema(description = "Product Name")
    private String productName;

    /** Planning Hours */
    @Schema(description = "Planning Hours")
    private String planningHours;

    /** List Price */
    @Schema(description = "List Price")
    private String listPrice;

    private String extJson;
}
