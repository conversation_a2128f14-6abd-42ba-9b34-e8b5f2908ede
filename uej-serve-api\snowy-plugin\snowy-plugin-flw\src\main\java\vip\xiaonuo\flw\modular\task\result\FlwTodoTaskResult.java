
package vip.xiaonuo.flw.modular.task.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 待办任务结果
 *
 * <AUTHOR>
 * @date 2022/5/22 16:19
 */
@Getter
@Setter
public class FlwTodoTaskResult {

    /** 任务id */
    @Schema(description = "任务id")
    private String id;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 流程实例id */
    @Schema(description = "流程实例id")
    private String processInstanceId;

    /** 流水号 */
    @Schema(description = "流水号")
    private String sn;

    /** 标题 */
    @Schema(description = "标题")
    private String title;

    /** 摘要 */
    @Schema(description = "摘要")
    private String abstractTitle;

    /** 流程定义id */
    @Schema(description = "流程定义id")
    private String processDefinitionId;

    /** 当前节点id */
    @Schema(description = "当前节点id")
    private String currentActivityId;

    /** 当前节点名称 */
    @Schema(description = "当前节点名称")
    private String currentActivityName;

    /** 办理人（顿号分割） */
    @Schema(description = "办理人（顿号分割）")
    private String assignees;

    /** 发起人id */
    @Schema(description = "发起人id")
    private String initiator;

    /** 发起人姓名 */
    @Schema(description = "发起人姓名")
    private String initiatorName;

    /** 发起人组织id */
    @Schema(description = "发起人组织id")
    private String initiatorOrgId;

    /** 发起人组织名称 */
    @Schema(description = "发起人组织名称")
    private String initiatorOrgName;

    /** 发起人职位id */
    @Schema(description = "发起人职位id")
    private String initiatorPositionId;

    /** 发起人职位名称 */
    @Schema(description = "发起人职位名称")
    private String initiatorPositionName;

    /** 发起时间 */
    @Schema(description = "发起时间")
    private String initiatorTime;
}
