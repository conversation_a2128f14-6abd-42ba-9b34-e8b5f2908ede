
package vip.xiaonuo.biz.modular.workhours.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.workhours.entity.BizWorkHours;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursAddParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursEditParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursIdParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursPageParam;
import vip.xiaonuo.biz.modular.workhours.service.BizWorkHoursService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工时控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 17:27
 */
@Tag(name = "工时控制器")
@RestController
@Validated
public class BizWorkHoursController {

    @Resource
    private BizWorkHoursService bizWorkHoursService;

    /**
     * 获取工时分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    @Operation(summary = "获取工时分页")
    @SaCheckPermission("/biz/workhours/page")
    @GetMapping("/biz/workhours/page")
    public CommonResult<Page<BizWorkHours>> page(BizWorkHoursPageParam bizWorkHoursPageParam) {
        return CommonResult.data(bizWorkHoursService.page(bizWorkHoursPageParam));
    }

    /**
     * 添加工时
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    @Operation(summary = "添加工时")
    @CommonLog("添加工时")
    @SaCheckPermission("/biz/workhours/add")
    @PostMapping("/biz/workhours/add")
    public CommonResult<String> add(@RequestBody @Valid BizWorkHoursAddParam bizWorkHoursAddParam) {
        bizWorkHoursService.add(bizWorkHoursAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑工时
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    @Operation(summary = "编辑工时")
    @CommonLog("编辑工时")
    @SaCheckPermission("/biz/workhours/edit")
    @PostMapping("/biz/workhours/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizWorkHoursEditParam bizWorkHoursEditParam) {
        bizWorkHoursService.edit(bizWorkHoursEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除工时
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    @Operation(summary = "删除工时")
    @CommonLog("删除工时")
    @SaCheckPermission("/biz/workhours/delete")
    @PostMapping("/biz/workhours/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizWorkHoursIdParam> bizWorkHoursIdParamList) {
        bizWorkHoursService.delete(bizWorkHoursIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取工时详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    @Operation(summary = "获取工时详情")
    @SaCheckPermission("/biz/workhours/detail")
    @GetMapping("/biz/workhours/detail")
    public CommonResult<BizWorkHours> detail(@Valid BizWorkHoursIdParam bizWorkHoursIdParam) {
        return CommonResult.data(bizWorkHoursService.detail(bizWorkHoursIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    @Operation(summary = "获取工时动态字段的配置")
    @SaCheckPermission("/biz/workhours/dynamicFieldConfigList")
    @GetMapping("/biz/workhours/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizWorkHoursService.dynamicFieldConfigList(columnName));
    }
}
