
package vip.xiaonuo.dbs.modular.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * 数据源实体
 *
 * <AUTHOR>
 * @date 2022/3/9 9:13
 **/
@Getter
@Setter
@TableName("EXT_DATABASE")
public class DbsStorage extends CommonEntity {

    /** id */
    @Schema(description = "id")
    private String id;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 名称 */
    @Schema(description = "名称")
    private String poolName;

    /** 连接URL */
    @Schema(description = "连接URL")
    private String url;

    /** 用户名 */
    @Schema(description = "用户名")
    private String username;

    /** 密码 */
    @Schema(description = "密码")
    private String password;

    /** 驱动名称 */
    @Schema(description = "驱动名称")
    private String driverName;

    /** 分类 */
    @Schema(description = "分类")
    private String category;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private String extJson;
}
