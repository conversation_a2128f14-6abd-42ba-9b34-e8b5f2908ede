<template>
	<xn-form-container
		:title="formData.id ? 'edit offering' : 'add offering'"
		:width="700"
		v-model:open="open"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="Category：" name="type">
						<a-tree-select
							v-model:value="formData.type"
							show-search
							style="width: 100%"
							:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
							placeholder="Please Select Category"
							allow-clear
							tree-default-expand-all
							:fieldNames="{ children: 'children', label: 'dictLabel', value: 'dictValue', key: 'id' }"
							:tree-data="categoryList"
							tree-node-filter-prop="label"
							@select="onChange"
						>
						</a-tree-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Type：" name="category">
						<a-select
							v-model:value="formData.category"
							:options="categoryOptions"
							style="width: 100%"
							placeholder="Please enter Type"
						>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Name：" name="name">
						<a-input v-model:value="formData.name" placeholder="Please enter Name" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Planning Hours：" name="planningHours">
						<a-time-picker
							v-model:value="formData.planningHours"
							:minute-step="5"
							format="HH:mm"
							value-format="HH:mm"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Invoice Hours：" name="invoiceHours">
						<a-time-picker v-model:value="formData.invoiceHours" :minute-step="5" format="HH:mm" value-format="HH:mm" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="List Price：" name="listPrice">
						<a-input-number
							v-model:value="formData.listPrice"
							:min="0"
							placeholder="Please enter List Price"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Cost Price：" name="costPrice">
						<a-input-number
							v-model:value="formData.costPrice"
							:min="0"
							placeholder="Please enter Cost Price"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
					<xn-form-item :fieldConfig="item" :formData="dynamicFormData" />
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="bizOfferingForm">
	import tool from '@/utils/tool'
	import dayjs from 'dayjs'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import bizOfferingApi from '@/api/biz/bizOfferingApi'
	import bizProductCategoryApi from '@/api/biz/bizProductCategoryApi'
	import XnEditor from '@/components/Editor/index.vue'
	// 抽屉状态
	const open = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)
	// 动态表单
	const dynamicFormRef = ref()
	const dynamicFieldConfigList = ref([])
	const dynamicFormData = ref({})

	const categoryList = ref([])

	const categoryOptions = tool.dictList('PRODUCT_CATEGORY')

	// 打开抽屉
	const onOpen = (record) => {
		open.value = true
		getCategoryList()
		bizOfferingApi.bizOfferingDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
			dynamicFieldConfigList.value = data
		})
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)

			dynamicFormData.value = JSON.parse(formData.value.extJson || null) || {}
		}
	}

	const getCategoryList = () => {
		/*
		bizOfferingApi.bizOfferingCategoryList().then((data) => {
			categoryList.value = [
				{
					dictLabel: 'Top',
					dictValue: 'Top',
					id: 0,
					children: data[0].children
				}
			]
		})

		 */
		bizProductCategoryApi.bizProductCategoryTree().then((data) => {
			categoryList.value = [
				{
					dictLabel: 'Top',
					dictValue: 'Top',
					id: 0,
					children: data[0].children
				}
			]
		})
	}

	const onChange = (value, options) => {
		formData.value.typeId=options.id
	}

	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		dynamicFormData.value = {}
		open.value = false
	}
	// 默认要校验的
	const formRules = {}
	// 验证并提交数据
	const onSubmit = () => {
		const promiseList = []
		promiseList.push(
			new Promise((resolve, reject) => {
				formRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		promiseList.push(
			new Promise((resolve, reject) => {
				dynamicFormRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		submitLoading.value = true
		Promise.all(promiseList)
			.then(() => {
				const formDataParam = cloneDeep(formData.value)
				formDataParam.extJson = JSON.stringify(dynamicFormData.value)
				bizOfferingApi
					.bizOfferingSubmitForm(formDataParam, formDataParam.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			})
			.catch(() => {})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
