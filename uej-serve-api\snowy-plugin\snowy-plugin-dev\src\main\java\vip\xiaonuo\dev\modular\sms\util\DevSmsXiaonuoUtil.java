
package vip.xiaonuo.dev.modular.sms.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.dromara.sms4j.dingzhong.config.DingZhongConfig;
import org.dromara.sms4j.javase.config.SEInitializer;
import org.dromara.sms4j.provider.config.SmsConfig;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.dev.api.DevConfigApi;

/**
 * 小诺短信工具类
 *
 * <AUTHOR>
 * @date 2022/1/2 17:05
 */
@Slf4j
public class DevSmsXiaonuoUtil {

    private static SmsBlend smsBlend;

    private static final String SNOWY_SMS_XIAONUO_ACCESS_KEY_ID_KEY = "SNOWY_SMS_XIAONUO_ACCESS_KEY_ID";
    private static final String SNOWY_SMS_XIAONUO_ACCESS_KEY_SECRET_KEY = "SNOWY_SMS_XIAONUO_ACCESS_KEY_SECRET";
    private static final String SNOWY_SMS_XIAONUO_REQUEST_URL_KEY = "SNOWY_SMS_XIAONUO_REQUEST_URL";
    private static final String SNOWY_SMS_XIAONUO_DEFAULT_SIGN_NAME_KEY = "SNOWY_SMS_XIAONUO_DEFAULT_SIGN_NAME";

    /**
     * 初始化操作的客户端
     *
     * <AUTHOR>
     * @date 2022/1/5 23:24
     */
    private static void initClient(String signName) {

        DevConfigApi devConfigApi = SpringUtil.getBean(DevConfigApi.class);

        /* accessKeyId */
        String accessKeyId = devConfigApi.getValueByKey(SNOWY_SMS_XIAONUO_ACCESS_KEY_ID_KEY);

        if(ObjectUtil.isEmpty(accessKeyId)) {
            throw new CommonException("小诺短信操作客户端未正确配置：accessKeyId为空");
        }

        /* accessKeySecret */
        String accessKeySecret = devConfigApi.getValueByKey(SNOWY_SMS_XIAONUO_ACCESS_KEY_SECRET_KEY);

        if(ObjectUtil.isEmpty(accessKeySecret)) {
            throw new CommonException("小诺短信操作客户端未正确配置：accessKeySecret为空");
        }

        /* requestUrl */
        String requestUrl = devConfigApi.getValueByKey(SNOWY_SMS_XIAONUO_REQUEST_URL_KEY);

        if(ObjectUtil.isEmpty(requestUrl)) {
            throw new CommonException("小诺短信操作客户端未正确配置：requestUrl为空");
        }

        DingZhongConfig dingZhongConfig = new DingZhongConfig();
        dingZhongConfig.setAccessKeyId(accessKeyId);
        dingZhongConfig.setAccessKeySecret(accessKeySecret);
        dingZhongConfig.setRequestUrl(requestUrl);
        dingZhongConfig.setSignature(signName);
        dingZhongConfig.setConfigId("XIAONUO");
        SEInitializer.initializer().fromConfig(new SmsConfig(), CollectionUtil.newArrayList(dingZhongConfig));
        smsBlend = SmsFactory.getSmsBlend(dingZhongConfig.getConfigId());
    }

    /**
     * 发送短信
     *
     * @param phoneNumbers 手机号码，支持对多个手机号码发送短信，手机号码之间以半角逗号（,）分隔。
     * @param signName 短信签名，为空则使用默认签名
     * @param message 短信内容
     * @return 发送的结果信息
     * <AUTHOR>
     * @date 2022/2/24 13:42
     **/
    public static String sendSms(String phoneNumbers, String signName, String message) {
        try {
            if(ObjectUtil.isEmpty(signName)) {
               signName = getDefaultSignName();
            }
            // 初始化客户端
            initClient(signName);
            // 发送短信
            SmsResponse smsResponse = smsBlend.massTexting(StrUtil.split(phoneNumbers, StrUtil.COMMA), message);
            if(smsResponse.isSuccess()) {
                return JSONUtil.toJsonStr(smsResponse.getData());
            } else {
                throw new CommonException("短信发送失败");
            }
        } catch (Exception e) {
            throw new CommonException(e.getMessage());
        }
    }

    /**
     * 获取默认签名
     *
     * <AUTHOR>
     * @date 2024/1/26 16:40
     **/
    public static String getDefaultSignName() {
        // 签名为空，则获取默认签名
        DevConfigApi devConfigApi = SpringUtil.getBean(DevConfigApi.class);
        String signName = devConfigApi.getValueByKey(SNOWY_SMS_XIAONUO_DEFAULT_SIGN_NAME_KEY);
        if(ObjectUtil.isEmpty(signName)) {
            throw new CommonException("小诺短信操作客户端未正确配置：signName为空");
        }
        return signName;
    }
}
