<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
            <a-row :gutter="24">
                <a-col :span="6">
                    <a-form-item label="remark" name="remark">
                        <a-input v-model:value="searchFormState.remark" placeholder="Please enter remark" />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="shop name" name="shopName">
                        <a-input v-model:value="searchFormState.shopName" placeholder="Please enter shop name" />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-button type="primary" @click="tableRef.refresh(true)">{{ $t('common.searchButton') }}</a-button>
                    <a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="tableRef"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.id"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection"
        >
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('bizDailyBalanceAdd')">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.addButton') }}
                    </a-button>
                    <xn-batch-delete
                        v-if="hasPerm('bizDailyBalanceBatchDelete')"
                        :selectedRowKeys="selectedRowKeys"
                        @batchDelete="deleteBatchBizDailyBalance"
						:buttonName="$t('common.batchRemoveButton')"
                    />
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('bizDailyBalanceEdit')">{{ $t('common.editButton') }}</a>
                        <a-divider type="vertical" v-if="hasPerm(['bizDailyBalanceEdit', 'bizDailyBalanceDelete'], 'and')" />
                        <a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizDailyBalance(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('bizDailyBalanceDelete')">{{ $t('common.removeButton') }}</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="tableRef.refresh(true)" />
</template>

<script setup name="dailybalance">
    import { cloneDeep } from 'lodash-es'
    import Form from './form.vue'
    import bizDailyBalanceApi from '@/api/biz/bizDailyBalanceApi'
    const searchFormState = ref({})
    const searchFormRef = ref()
    const tableRef = ref()
    const formRef = ref()
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    const columns = [
        {
            title: 'item',
            dataIndex: 'item'
        },
        {
            title: 'receivable amount',
            dataIndex: 'receivableAmount'
        },
        {
            title: 'received amount',
            dataIndex: 'receivedAmount'
        },
        {
            title: 'balance',
            dataIndex: 'balance'
        },
        {
            title: 'remark',
            dataIndex: 'remark'
        },
        {
            title: 'orgId',
            dataIndex: 'orgId'
        },
        {
            title: 'orgName',
            dataIndex: 'orgName'
        },
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['bizDailyBalanceEdit', 'bizDailyBalanceDelete'])) {
        columns.push({
            title: 'action',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        })
    }
    const selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        // columns数字类型字段加入 needTotal: true 可以勾选自动算账
        alert: {
            show: true,
            clear: () => {
                selectedRowKeys.value = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
        const searchFormParam = cloneDeep(searchFormState.value)
        return bizDailyBalanceApi.bizDailyBalancePage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    // 重置
    const reset = () => {
        searchFormRef.value.resetFields()
        tableRef.value.refresh(true)
    }
    // 删除
    const deleteBizDailyBalance = (record) => {
        let params = [
            {
                id: record.id
            }
        ]
        bizDailyBalanceApi.bizDailyBalanceDelete(params).then(() => {
            tableRef.value.refresh(true)
        })
    }
    // 批量删除
    const deleteBatchBizDailyBalance = (params) => {
        bizDailyBalanceApi.bizDailyBalanceDelete(params).then(() => {
            tableRef.value.clearRefreshSelected()
        })
    }
</script>
