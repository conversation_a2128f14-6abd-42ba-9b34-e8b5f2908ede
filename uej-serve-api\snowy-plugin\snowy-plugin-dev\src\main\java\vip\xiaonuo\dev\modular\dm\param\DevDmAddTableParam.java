
package vip.xiaonuo.dev.modular.dm.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态字段配置添加参数
 *
 * <AUTHOR>
 * @date 2023/08/04 08:18
 **/
@Getter
@Setter
public class DevDmAddTableParam {

    /**
     * 数据源
     */
    @Schema(description = "数据源")
    @NotEmpty(message = "dbsId不能为空")
    private String dbsId;

    /**
     * 表名称
     */
    @Schema(description = "表名称")
    @NotEmpty(message = "tableName不能为空")
    private String tableName;

    /**
     * 表注释
     */
    @Schema(description = "表注释")
    @NotEmpty(message = "tableRemark不能为空")
    private String tableRemark;


}
