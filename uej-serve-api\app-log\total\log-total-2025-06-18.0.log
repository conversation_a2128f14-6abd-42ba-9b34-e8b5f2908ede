2025-06-18T15:01:32.031+08:00  INFO 11860 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 11860 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-18T15:01:32.035+08:00  INFO 11860 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-18T15:01:35.651+08:00  INFO 11860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-18T15:01:35.658+08:00  INFO 11860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-18T15:01:36.118+08:00  INFO 11860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 428 ms. Found 0 Redis repository interfaces.
2025-06-18T15:01:36.706+08:00  WARN 11860 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-18T15:01:37.137+08:00  WARN 11860 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-18T15:01:37.597+08:00  WARN 11860 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-18T15:01:38.022+08:00  INFO 11860 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-18T15:01:38.045+08:00  INFO 11860 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-18T15:01:38.046+08:00  INFO 11860 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-18T15:01:38.169+08:00  INFO 11860 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-18T15:01:38.170+08:00  INFO 11860 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6064 ms
2025-06-18T15:01:38.638+08:00  INFO 11860 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-18T15:01:39.214+08:00  INFO 11860 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-18T15:01:39.217+08:00  INFO 11860 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-18T15:01:39.220+08:00  INFO 11860 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-18T15:01:39.220+08:00  INFO 11860 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-18T15:01:39.729+08:00  INFO 11860 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-18T15:01:46.193+08:00  INFO 11860 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-18T15:01:47.360+08:00  INFO 11860 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-18T15:01:49.273+08:00  INFO 11860 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@6a08113d, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-18T15:01:49.301+08:00  INFO 11860 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-18T15:01:49.302+08:00  INFO 11860 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-18T15:01:49.302+08:00  INFO 11860 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-18T15:01:49.302+08:00  INFO 11860 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-18T15:01:49.314+08:00  INFO 11860 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-18T15:01:49.772+08:00  INFO 11860 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@38503309)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@49620576, clock: SystemClock, configuration: Configuration(false)]
2025-06-18T15:01:52.149+08:00  INFO 11860 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-18T15:01:52.155+08:00  INFO 11860 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-18T15:01:52.373+08:00  INFO 11860 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-18T15:01:55.402+08:00  INFO 11860 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-18T15:01:55.561+08:00  WARN 11860 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'container'
2025-06-18T15:01:55.574+08:00  INFO 11860 --- [main] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-18T15:01:55.593+08:00  INFO 11860 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-18T15:01:55.597+08:00  INFO 11860 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-18T15:01:55.605+08:00  INFO 11860 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-18T15:01:55.605+08:00  INFO 11860 --- [main] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-18T15:01:55.605+08:00  INFO 11860 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-18T15:01:55.607+08:00  INFO 11860 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-18T15:01:55.607+08:00  INFO 11860 --- [main] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-18T15:01:55.607+08:00  INFO 11860 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-18T15:01:55.623+08:00  INFO 11860 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-18T15:01:55.648+08:00 ERROR 11860 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.context.ApplicationContextException: Failed to start bean 'container'
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:287)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:467)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:256)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:201)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:979)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:628)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at vip.xiaonuo.Application.main(Application.java:40)
Caused by: org.springframework.data.redis.listener.adapter.RedisListenerExecutionFailedException: org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.lazyListen(RedisMessageListenerContainer.java:382)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.start(RedisMessageListenerContainer.java:360)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:284)
	... 11 common frames omitted
Caused by: org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1805)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1736)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1538)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.lambda$getConnection$0(LettuceConnectionFactory.java:1518)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.doInLock(LettuceConnectionFactory.java:1478)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1515)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1199)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:1006)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.lambda$initialize$0(RedisMessageListenerContainer.java:1239)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.doInLock(RedisMessageListenerContainer.java:1453)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.initialize(RedisMessageListenerContainer.java:1233)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.doSubscribe(RedisMessageListenerContainer.java:427)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.lazyListen(RedisMessageListenerContainer.java:403)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.lazyListen(RedisMessageListenerContainer.java:373)
	... 13 common frames omitted
Caused by: org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:104)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1734)
	... 25 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 127.0.0.1/<unresolved>:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:350)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:215)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:112)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:112)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$getConnection$0(LettucePoolingConnectionProvider.java:93)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:211)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:201)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:71)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:566)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:306)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:233)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:99)
	... 26 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:335)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-18T15:02:26.356+08:00  INFO 23660 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 23660 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-18T15:02:26.358+08:00  INFO 23660 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-18T15:02:28.625+08:00  INFO 23660 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-18T15:02:28.630+08:00  INFO 23660 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-18T15:02:28.856+08:00  INFO 23660 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 208 ms. Found 0 Redis repository interfaces.
2025-06-18T15:02:29.239+08:00  WARN 23660 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-18T15:02:29.574+08:00  WARN 23660 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-18T15:02:30.016+08:00  WARN 23660 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-18T15:02:30.393+08:00  INFO 23660 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-18T15:02:30.409+08:00  INFO 23660 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-18T15:02:30.409+08:00  INFO 23660 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-18T15:02:30.512+08:00  INFO 23660 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-18T15:02:30.512+08:00  INFO 23660 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4104 ms
2025-06-18T15:02:30.853+08:00  INFO 23660 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-18T15:02:31.156+08:00  INFO 23660 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-18T15:02:31.157+08:00  INFO 23660 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-18T15:02:31.159+08:00  INFO 23660 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-18T15:02:31.159+08:00  INFO 23660 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-18T15:02:31.506+08:00  INFO 23660 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-18T15:02:35.918+08:00  INFO 23660 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-18T15:02:36.805+08:00  INFO 23660 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-18T15:02:38.992+08:00  INFO 23660 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@c3e5e3c, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-18T15:02:39.022+08:00  INFO 23660 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-18T15:02:39.022+08:00  INFO 23660 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-18T15:02:39.022+08:00  INFO 23660 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-18T15:02:39.022+08:00  INFO 23660 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-18T15:02:39.032+08:00  INFO 23660 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-18T15:02:39.414+08:00  INFO 23660 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@6f65b61d)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@3b6098fd, clock: SystemClock, configuration: Configuration(false)]
2025-06-18T15:02:44.021+08:00  INFO 23660 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-18T15:02:44.032+08:00  INFO 23660 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-18T15:02:44.382+08:00  INFO 23660 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-18T15:02:50.927+08:00  INFO 23660 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-18T15:02:51.133+08:00  INFO 23660 --- [main] vip.xiaonuo.Application                  : Started Application in 25.848 seconds (process running for 27.256)
2025-06-18T15:02:51.305+08:00  INFO 23660 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-18T15:03:36.734+08:00  INFO 23660 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-18T15:03:36.735+08:00  INFO 23660 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-18T15:03:36.741+08:00  INFO 23660 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-06-18T15:03:37.086+08:00  WARN 23660 --- [http-nio-10082-exec-2] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [134] milliseconds.
2025-06-18T15:18:23.673+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.672+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.674+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.674+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.676+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.677+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.679+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.680+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.680+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:23.693+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-06-18T15:18:33.741+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.741+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.742+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.743+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.743+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.744+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.744+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.744+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:18:33.758+08:00  WARN 23660 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-06-18T15:18:43.006+08:00  WARN 23660 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T15:23:03.778+08:00  WARN 23660 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.910+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.912+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.913+08:00  WARN 23660 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.914+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.915+08:00  WARN 23660 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.915+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.917+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.919+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.923+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:05:54.950+08:00  WARN 23660 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-06-18T16:06:05.431+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.431+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.433+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.434+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.435+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.435+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.436+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.445+08:00  WARN 23660 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.449+08:00  WARN 23660 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:06:05.455+08:00  WARN 23660 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-06-18T16:12:07.957+08:00  INFO 23660 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-18T16:12:08.038+08:00  INFO 23660 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-18T16:12:08.052+08:00  INFO 23660 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-18T16:12:08.066+08:00  INFO 23660 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-18T16:12:08.067+08:00  INFO 23660 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-18T16:12:08.067+08:00  INFO 23660 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-18T16:12:08.071+08:00  INFO 23660 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-18T16:12:08.071+08:00  INFO 23660 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-18T16:12:08.071+08:00  INFO 23660 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-18T16:46:31.630+08:00  INFO 25788 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 25788 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-18T16:46:31.632+08:00  INFO 25788 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-18T16:46:34.482+08:00  INFO 25788 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-18T16:46:34.487+08:00  INFO 25788 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-18T16:46:34.760+08:00  INFO 25788 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 253 ms. Found 0 Redis repository interfaces.
2025-06-18T16:46:35.332+08:00  WARN 25788 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-18T16:46:35.808+08:00  WARN 25788 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-18T16:46:36.255+08:00  WARN 25788 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-18T16:46:36.766+08:00  INFO 25788 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-18T16:46:36.787+08:00  INFO 25788 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-18T16:46:36.787+08:00  INFO 25788 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-18T16:46:36.904+08:00  INFO 25788 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-18T16:46:36.905+08:00  INFO 25788 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5223 ms
2025-06-18T16:46:37.347+08:00  INFO 25788 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-18T16:46:37.734+08:00  INFO 25788 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-18T16:46:37.736+08:00  INFO 25788 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-18T16:46:37.737+08:00  INFO 25788 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-18T16:46:37.737+08:00  INFO 25788 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-18T16:46:38.136+08:00  INFO 25788 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-18T16:46:43.402+08:00  INFO 25788 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-18T16:46:44.591+08:00  INFO 25788 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-18T16:46:47.250+08:00  INFO 25788 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@a6924ad, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-18T16:46:47.283+08:00  INFO 25788 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-18T16:46:47.284+08:00  INFO 25788 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-18T16:46:47.284+08:00  INFO 25788 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-18T16:46:47.284+08:00  INFO 25788 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-18T16:46:47.296+08:00  INFO 25788 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-18T16:46:47.770+08:00  INFO 25788 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@2a8eed58)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@3f4fd0dc, clock: SystemClock, configuration: Configuration(false)]
2025-06-18T16:46:50.767+08:00  INFO 25788 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-18T16:46:50.774+08:00  INFO 25788 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-18T16:46:50.971+08:00  INFO 25788 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-18T16:46:54.461+08:00  INFO 25788 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-18T16:46:54.594+08:00  INFO 25788 --- [main] vip.xiaonuo.Application                  : Started Application in 24.397 seconds (process running for 25.927)
2025-06-18T16:46:54.671+08:00  INFO 25788 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-18T16:47:07.192+08:00  INFO 25788 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-18T16:47:07.192+08:00  INFO 25788 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-18T16:47:07.195+08:00  INFO 25788 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-18T16:47:07.582+08:00  WARN 25788 --- [http-nio-10082-exec-1] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [245] milliseconds.
2025-06-18T16:54:43.877+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.878+08:00  WARN 25788 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.878+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.879+08:00  WARN 25788 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.880+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.881+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.883+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.884+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.885+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T16:54:43.907+08:00  WARN 25788 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-06-18T17:05:24.685+08:00  WARN 25788 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:05:24.691+08:00  WARN 25788 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-18T17:05:31.041+08:00  WARN 25788 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:05:31.047+08:00  WARN 25788 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-18T17:12:45.965+08:00  WARN 25788 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:12:45.971+08:00  WARN 25788 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-18T17:12:51.353+08:00  WARN 25788 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:12:51.359+08:00  WARN 25788 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-18T17:16:27.491+08:00  WARN 25788 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:16:27.497+08:00  WARN 25788 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-18T17:23:37.599+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.600+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.601+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.603+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.605+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.606+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.606+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.615+08:00  WARN 25788 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.616+08:00  WARN 25788 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-18T17:23:37.627+08:00  WARN 25788 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-06-18T17:23:54.216+08:00  INFO 25788 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-18T17:23:54.247+08:00  INFO 25788 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-18T17:23:54.256+08:00  INFO 25788 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-18T17:23:54.271+08:00  INFO 25788 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-18T17:23:54.272+08:00  INFO 25788 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-18T17:23:54.273+08:00  INFO 25788 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-18T17:23:54.279+08:00  INFO 25788 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-18T17:23:54.279+08:00  INFO 25788 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-18T17:23:54.280+08:00  INFO 25788 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-18T17:24:04.068+08:00  INFO 28476 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 28476 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-18T17:24:04.071+08:00  INFO 28476 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-18T17:24:07.937+08:00  INFO 28476 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-18T17:24:07.942+08:00  INFO 28476 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-18T17:24:08.284+08:00  INFO 28476 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 319 ms. Found 0 Redis repository interfaces.
2025-06-18T17:24:08.870+08:00  WARN 28476 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-18T17:24:09.346+08:00  WARN 28476 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-18T17:24:09.889+08:00  WARN 28476 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-18T17:24:10.468+08:00  INFO 28476 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-18T17:24:10.488+08:00  INFO 28476 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-18T17:24:10.488+08:00  INFO 28476 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-18T17:24:10.642+08:00  INFO 28476 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-18T17:24:10.643+08:00  INFO 28476 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6497 ms
2025-06-18T17:24:11.167+08:00  INFO 28476 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-18T17:24:11.543+08:00  INFO 28476 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-18T17:24:11.545+08:00  INFO 28476 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-18T17:24:11.546+08:00  INFO 28476 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-18T17:24:11.546+08:00  INFO 28476 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-18T17:24:12.090+08:00  INFO 28476 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-18T17:24:17.191+08:00  INFO 28476 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-18T17:24:18.317+08:00  INFO 28476 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-18T17:24:21.229+08:00  INFO 28476 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@6e2e11ee, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-18T17:24:21.267+08:00  INFO 28476 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-18T17:24:21.267+08:00  INFO 28476 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-18T17:24:21.267+08:00  INFO 28476 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-18T17:24:21.267+08:00  INFO 28476 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-18T17:24:21.278+08:00  INFO 28476 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-18T17:24:21.723+08:00  INFO 28476 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@2cf6dd4d)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@293988dc, clock: SystemClock, configuration: Configuration(false)]
2025-06-18T17:24:24.468+08:00  INFO 28476 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-18T17:24:24.473+08:00  INFO 28476 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-18T17:24:24.644+08:00  INFO 28476 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-18T17:24:28.231+08:00  INFO 28476 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-18T17:24:28.379+08:00  INFO 28476 --- [main] vip.xiaonuo.Application                  : Started Application in 25.863 seconds (process running for 27.453)
2025-06-18T17:24:28.482+08:00  INFO 28476 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-18T17:24:35.078+08:00  INFO 28476 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-18T17:24:35.079+08:00  INFO 28476 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-18T17:24:35.083+08:00  INFO 28476 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-06-18T17:24:35.344+08:00  WARN 28476 --- [http-nio-10082-exec-3] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [103] milliseconds.
2025-06-18T17:24:35.345+08:00  WARN 28476 --- [http-nio-10082-exec-1] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [103] milliseconds.
2025-06-18T17:27:45.383+08:00  INFO 28476 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-18T17:27:45.413+08:00  INFO 28476 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-18T17:27:45.420+08:00  INFO 28476 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-18T17:27:45.434+08:00  INFO 28476 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-18T17:27:45.435+08:00  INFO 28476 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-18T17:27:45.435+08:00  INFO 28476 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-18T17:27:45.440+08:00  INFO 28476 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-18T17:27:45.441+08:00  INFO 28476 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-18T17:27:45.441+08:00  INFO 28476 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
