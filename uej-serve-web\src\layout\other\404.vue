<template>
	<a-result status="404" title="404" :sub-title="$t('common.404')">
		<template #extra>
			<a-button type="primary" @click="gohome">{{ $t('login.backLogin') }}</a-button>
			<a-button type="dashed" @click="goback">{{ $t('login.backGoOne') }}</a-button>
		</template>
	</a-result>
</template>
<script setup>
	import { useRouter } from 'vue-router'
	const router = useRouter()
	const gohome = () => {
		location.href = '/'
	}
	const goback = () => {
		router.go(-1)
	}
</script>
