
package vip.xiaonuo.biz.modular.offeringgroupitem.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.offeringgroupitem.entity.BizOfferingGroupItem;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemAddParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemEditParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemIdParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 套餐明细Service接口
 *
 * <AUTHOR>
 * @date  2024/06/13 09:58
 **/
public interface BizOfferingGroupItemService extends IService<BizOfferingGroupItem> {

    /**
     * 获取套餐明细分页
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    Page<BizOfferingGroupItem> page(BizOfferingGroupItemPageParam bizOfferingGroupItemPageParam);

    /**
     * 添加套餐明细
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    void add(BizOfferingGroupItemAddParam bizOfferingGroupItemAddParam);

    /**
     * 编辑套餐明细
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    void edit(BizOfferingGroupItemEditParam bizOfferingGroupItemEditParam);

    /**
     * 删除套餐明细
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    void delete(List<BizOfferingGroupItemIdParam> bizOfferingGroupItemIdParamList);

    /**
     * 获取套餐明细详情
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     */
    BizOfferingGroupItem detail(BizOfferingGroupItemIdParam bizOfferingGroupItemIdParam);

    /**
     * 获取套餐明细详情
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
     **/
    BizOfferingGroupItem queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/13 09:58
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
