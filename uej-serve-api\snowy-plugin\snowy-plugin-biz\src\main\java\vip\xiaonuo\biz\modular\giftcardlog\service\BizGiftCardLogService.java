
package vip.xiaonuo.biz.modular.giftcardlog.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.giftcardlog.entity.BizGiftCardLog;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogAddParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogEditParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogIdParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 礼品卡记录Service接口
 *
 * <AUTHOR>
 * @date  2024/06/13 09:46
 **/
public interface BizGiftCardLogService extends IService<BizGiftCardLog> {

    /**
     * 获取礼品卡记录分页
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    Page<BizGiftCardLog> page(BizGiftCardLogPageParam bizGiftCardLogPageParam);

    /**
     * 添加礼品卡记录
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    void add(BizGiftCardLogAddParam bizGiftCardLogAddParam);

    /**
     * 编辑礼品卡记录
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    void edit(BizGiftCardLogEditParam bizGiftCardLogEditParam);

    /**
     * 删除礼品卡记录
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    void delete(List<BizGiftCardLogIdParam> bizGiftCardLogIdParamList);

    /**
     * 获取礼品卡记录详情
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     */
    BizGiftCardLog detail(BizGiftCardLogIdParam bizGiftCardLogIdParam);

    /**
     * 获取礼品卡记录详情
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
     **/
    BizGiftCardLog queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/13 09:46
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
