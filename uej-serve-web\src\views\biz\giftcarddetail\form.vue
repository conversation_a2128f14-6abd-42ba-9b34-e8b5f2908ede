<template>
	<xn-form-container
		:title="formData.id ? 'edit giftcard' : 'add giftcard'"
		:width="700"
		v-model:open="open"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<!-- <a-col :span="12">
					<a-form-item label="gift_card_id：" name="mainId">
						<a-input v-model:value="formData.mainId" placeholder="Please enter gift_card_id" allow-clear disabled />
					</a-form-item>
				</a-col> -->
				<a-col :span="12">
					<a-form-item label="Card Number：" name="cardNumber">
						<a-input v-model:value="formData.cardNumber" placeholder="Please enter Card Number" allow-clear disabled />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Pin：" name="pin">
						<a-input v-model:value="formData.pin" placeholder="Please enter Pin" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Create Date：" name="createDate">
						<a-date-picker
							v-model:value="formData.createDate"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="Please enter Create Date"
							style="width: 100%"
							disabled
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Active Date：" name="activeDate">
						<a-date-picker
							v-model:value="formData.activeDate"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="Please enter Active Date"
							style="width: 100%"
							disabled
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Exp Date：" name="expDate">
						<a-date-picker
							v-model:value="formData.expDate"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="Please enter Exp Date"
							style="width: 100%"
							disabled
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Used Date：" name="usedDate">
						<a-date-picker
							v-model:value="formData.usedDate"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="Please enter Used Date"
							style="width: 100%"
							disabled
						/>
					</a-form-item>
				</a-col>
				<!-- <a-col :span="12">
					<a-form-item label="Value：" name="value">
						<a-input-number
							v-model:value="formData.value"
							placeholder="Please Enter Value"
							allow-clear
							min="1"
							max="1000000"
							style="width: 100%"
							:disabled="formData.actived == '1'"
						/>
					</a-form-item>
				</a-col> -->
				<a-col :span="12">
					<a-form-item label="Rest Value：" name="restValue">
						<a-input-number
							v-model:value="formData.restValue"
							placeholder="Rest Value"
							allow-clear
							min="1"
							max="1000000"
							style="width: 100%"
							disabled
						/>
					</a-form-item>
				</a-col>

				<a-col :span="12">
					<a-form-item label="If Avtived：" name="actived">
						<a-radio-group
							v-model:value="formData.actived"
							button-style="solid"
							:disabled="formData.actived == '1' || !formData.id"
							@change="onActived"
						>
							<a-radio-button value="0">Not Actived</a-radio-button>
							<a-radio-button value="1">Actived</a-radio-button>
						</a-radio-group>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Shop Name：" name="orgId">
						<a-select v-model:value="formData.orgId" :options="orgList" style="width: 100%"></a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12" v-if="numVisible">
					<a-form-item label="Number：" name="num">
						<a-input-number
							v-model:value="formData.num"
							:min="0"
							placeholder="Please enter Number"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
					<xn-form-item :fieldConfig="item" :formData="dynamicFormData" />
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="bizGiftCardDetailForm">
	import dayjs from 'dayjs'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import { useGlobalStore } from '@/store'
	import bizOrgApi from '@/api/biz/bizOrgApi'
	import bizGiftCardDetailApi from '@/api/biz/bizGiftCardDetailApi'
	// 抽屉状态
	const userStore = useGlobalStore()
	const open = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)
	const numVisible = ref(false)
	// 动态表单
	const dynamicFormRef = ref()
	const dynamicFieldConfigList = ref([])
	const dynamicFormData = ref({})
	const orgList = ref([])

	// 打开抽屉
	const onOpen = (record) => {
		open.value = true
		bizGiftCardDetailApi.bizGiftCardDetailDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
			dynamicFieldConfigList.value = data
		})
		bizOrgApi.orgPage({ current: 1, size: 100 }).then((res) => {
			orgList.value = res.records.map((item) => {
				return {
					value: item.id,
					label: item.name
				}
			})
		})
		formData.value = {
			actived: '0',
			createDate: dayjs().format('YYYY-MM-DD HH:mm:ss')
		}
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			dynamicFormData.value = JSON.parse(formData.value.extJson || null) || {}
		}
	}
	const onActived = () => {
		if (formData.value.actived == '1') {
			formData.value.activeDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
			formData.value.expDate = dayjs().add(3, 'year').format('YYYY-MM-DD HH:mm:ss')
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		dynamicFormData.value = {}
		numVisible.value = false
		open.value = false
	}
	// 默认要校验的
	const formRules = {
		orgId: [required('required')],
	}
	// 验证并提交数据
	const onSubmit = () => {
		const promiseList = []
		promiseList.push(
			new Promise((resolve, reject) => {
				formRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		promiseList.push(
			new Promise((resolve, reject) => {
				dynamicFormRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)

		Promise.all(promiseList)
			.then(() => {
				submitLoading.value = true
				const formDataParam = cloneDeep(formData.value)
				formDataParam.extJson = JSON.stringify(dynamicFormData.value)
				formDataParam.orgId = userStore.userInfo.orgId
				formDataParam.orgName = userStore.userInfo.orgName
				bizGiftCardDetailApi
					.bizGiftCardDetailSubmitForm(formDataParam, formDataParam.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			})
			.catch(() => {})
	}

	const setMainId = (record) => {
		formData.value.mainId = record.mainId
	}

	const batch = () => {
		numVisible.value = true
	}
	// 抛出函数
	defineExpose({
		onOpen,
		setMainId,
		batch
	})
</script>
