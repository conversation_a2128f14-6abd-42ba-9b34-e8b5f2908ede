package vip.xiaonuo.biz.modular.voucher.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.voucher.entity.BizVoucher;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherAddParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherEditParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherIdParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 代金券信息Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 17:17
 **/
public interface BizVoucherService extends IService<BizVoucher> {

    /**
     * 获取代金券信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    Page<BizVoucher> page(BizVoucherPageParam bizVoucherPageParam);

    /**
     * 添加代金券信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    void add(BizVoucherAddParam bizVoucherAddParam);

    /**
     * 编辑代金券信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    void edit(BizVoucherEditParam bizVoucherEditParam);

    /**
     * 删除代金券信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    void delete(List<BizVoucherIdParam> bizVoucherIdParamList);

    /**
     * 获取代金券信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    BizVoucher detail(BizVoucherIdParam bizVoucherIdParam);

    /**
     * 获取代金券信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     **/
    BizVoucher queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);

    /** 核销优惠券 */
    String redeemVoucher(String voucherNo, String productPrice);

    /** 根据任务ID恢复优惠券状态 */
    void restoreVoucherByTaskId(String taskId);
}
