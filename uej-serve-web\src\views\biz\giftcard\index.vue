<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Card Number" name="cardNumber">
						<a-input v-model:value="searchFormState.cardNumber" placeholder="Please enter Card Number" @pressEnter="giftCardPageChange(1)"/>
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="If Avtived" name="actived">
						<a-select v-model:value="searchFormState.actived"  placeholder="Please select Avtived" >
							<a-select-option value="0">Not Actived</a-select-option>
							<a-select-option value="1">Actived</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-button type="primary" @click="giftCardPageChange(1)">{{ $t('common.searchButton') }}</a-button>
					<a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
				</a-col>
			</a-row>
		</a-form>
		<a-row :gutter="24" style="margin-bottom: 20px">
			<a-col :span="24">
				<a-space>
					<a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('bizGiftCardAdd')">
						<template #icon>
							<plus-outlined />
						</template>
						{{ $t('common.addButton') }}
					</a-button>
					<!-- <xn-batch-delete
						v-if="hasPerm('bizGiftCardBatchDelete')"
						:selectedRowKeys="selectedRowKeys"
						@batchDelete="deleteBatchBizGiftCard"
						:buttonName="$t('common.batchRemoveButton')"
					/> -->
				</a-space>
			</a-col>
		</a-row>
		<XnVipCardList
			:loading="loading"
			:dataSource="tableSource"
			:page="giftCardPage"
			:actions="actions"
			@action="handleAction"
			@page-change="giftCardPageChange"
		></XnVipCardList>
	</a-card>
	<a-modal v-model:open="logVisible" width="1200px" title="Records" @cancel="handleCancel" :footer="null" >
		<bizGiftCardDetail :mainId="formData.id" ref="bizGiftCardDetailRef"></bizGiftCardDetail>
	</a-modal>
	<Form ref="formRef" @successful="giftCardPageChange" />
</template>

<script setup name="giftcard">
	import { cloneDeep } from 'lodash-es'
	import Form from './form.vue'
	import bizGiftCardApi from '@/api/biz/bizGiftCardApi'
	import bizGiftCardDetail from '@/views/biz/giftcarddetail/index.vue'

	const searchFormState = ref({})
	const searchFormRef = ref()
	const bizGiftCardDetailRef=ref()
	const tableRef = ref()
	const formRef = ref()
	const loading = ref(false)
	const tableSource = ref([])
	const formData=ref({})

	const logVisible = ref(false)

	const giftCardPage = ref({
		size: 12,
		current: 1,
		total: 0
	})

	const selectedRowKeys = ref([])

	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		giftCardPageChange(1)
	}
	// 删除
	const deleteBizGiftCard = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		bizGiftCardApi.bizGiftCardDelete(params).then(() => {
			giftCardPageChange(1)
		})
	}
	// 批量删除
	const deleteBatchBizGiftCard = (params) => {
		bizGiftCardApi.bizGiftCardDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}

	const actions = [
		{
			icon: 'ReadFilled',
			color: '#009dff',
			label: 'common.log',
			key: 'log'
		}
	]

	 // 操作栏通过权限判断是否显示
	//  if (hasPerm(['bizGiftCardEdit'])) {
    //     actions.unshift({
	// 		icon: 'EditOutlined',
	// 		color: '#009f21',
	// 		label: 'common.editButton',
	// 		key: 'edit'
	// 	})
    // }
	if (hasPerm(['bizGiftCardDelete'])) {
        actions.push({
			icon: 'DeleteOutlined',
			color: '#f5222d',
			label: 'common.removeButton',
			key: 'danger'
		})
    }

	//获取服务团队成员
	const getGiftCard = () => {
		loading.value = true
		bizGiftCardApi
			.bizGiftCardPage(Object.assign(searchFormState.value,giftCardPage.value))
			.then((res) => {
				tableSource.value = res.records.map((item) => {
					return {
						title: 'No. ' + item.id,
						subTitle: item.createDate ,
						img: 'https://oa-wanqi20-1255648699.cos.ap-shanghai.myqcloud.com/2024/6/12/1800867229097611265.png',
						badge: {
							text:'Gift Card' ,
							color: item.actived == 1 ? 'red' : 'green'
						},
						expDate: item.expDate,
						contents: [
							{
								label: 'Value',
								value: item.value
							}
						],
						record: {
							...item
						}
					}
				})
				giftCardPage.value.total = res.total
				loading.value = false
			})
			.catch(() => {
				loading.value = false
			})
	}

	//cardList 分页回调
	const giftCardPageChange = (val) => {
		giftCardPage.value.current = val ? val : 1
		getGiftCard()
	}

	const handleAction = (action) => {
		if (action.key === 'edit') {
			formRef.value.onOpen(action.record)
		} else if (action.key === 'danger') {
			deleteBizGiftCard(action.record)
		} else if (action.key === 'log') {
			showLog(action)
		}
	}

	giftCardPageChange(1)

	const showLog = (action) => {
		formData.value=action.record
		logVisible.value = true
		nextTick(()=>{
			bizGiftCardDetailRef.value.refresh()
		})
	}

	const handleCancel = () => {
		logVisible.value = false
	}
</script>
