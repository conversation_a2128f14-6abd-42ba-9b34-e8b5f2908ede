package vip.xiaonuo.biz.modular.task.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.biz.modular.giftcard.service.BizGiftCardService;
import vip.xiaonuo.biz.modular.giftcarddetail.entity.BizGiftCardDetail;
import vip.xiaonuo.biz.modular.giftcarddetail.service.BizGiftCardDetailService;
import vip.xiaonuo.biz.modular.giftcardlog.entity.BizGiftCardLog;
import vip.xiaonuo.biz.modular.giftcardlog.service.BizGiftCardLogService;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.biz.modular.schedule.service.BizScheduleService;
import vip.xiaonuo.biz.modular.task.entity.BizTaskGiftCard;
import vip.xiaonuo.biz.modular.task.service.BizTaskGiftCardService;
import vip.xiaonuo.biz.modular.taskdatastatistics.entity.BizTaskDataStatistics;
import vip.xiaonuo.biz.modular.taskdatastatistics.service.BizTaskDataStatisticsService;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;
import vip.xiaonuo.biz.modular.taskitem.service.BizTaskItemService;
import vip.xiaonuo.biz.modular.voucher.entity.BizVoucher;
import vip.xiaonuo.biz.modular.voucher.service.BizVoucherService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.param.BizTaskAddParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskEditParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskIdParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskPageParam;
import vip.xiaonuo.biz.modular.task.service.BizTaskService;
import cn.hutool.json.JSONObject;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import vip.xiaonuo.flw.modular.template.service.FlwTemplateSnService;
import vip.xiaonuo.sys.modular.user.entity.SysUser;
import vip.xiaonuo.sys.modular.user.service.SysUserService;

import java.util.List;
import java.util.stream.Stream;

/**
 * 任务信息控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 15:42
 */
@Tag(name = "任务信息控制器")
@RestController
@Validated
public class BizTaskController {

    @Resource
    private BizTaskService bizTaskService;

    @Resource
    private BizTaskDataStatisticsService bizTaskDataStatisticsService;

    @Resource
    private BizTaskItemService bizTaskItemService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private FlwTemplateSnService flwTemplateSnService;

    @Resource
    private BizVoucherService bizVoucherService;

    @Resource
    private BizGiftCardDetailService bizGiftCardDetailService;

    @Resource
    private BizGiftCardLogService bizGiftCardLogService;

    @Resource
    private BizScheduleService bizScheduleService;

    @Resource
    private BizTaskGiftCardService bizTaskGiftCardService;

    /**
     * 获取任务信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    @Operation(summary = "获取任务信息分页")
    @SaCheckPermission("/biz/task/page")
    @GetMapping("/biz/task/page")
    public CommonResult<Page<BizTask>> page(BizTaskPageParam bizTaskPageParam) {
        return CommonResult.data(bizTaskService.page(bizTaskPageParam));
    }

    /**
     * 添加任务信息
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    @Operation(summary = "添加任务信息")
    @CommonLog("添加任务信息")
    @SaCheckPermission("/biz/task/add")
    @PostMapping("/biz/task/add")
    public CommonResult<String> add(@RequestBody @Valid BizTask bizTask) {
        List<BizTaskItem> bizTaskItemList = bizTask.getBizTaskItemList();
        String staffName = getStaffInfo(bizTask.getAssign()).get("staffName");
        String taskName = getTaskName(bizTaskItemList.get(0).getProductId());
        String orgId = getStaffInfo(bizTask.getAssign()).get("orgId");
        bizTask.setItemName(taskName);
        bizTask.setStaff(staffName);
        if (staffName.equals("Unregistered")) {
            bizTask.setAssign("Unregistered");
        }
        bizTask.setOrgId(orgId);
        // 处理多礼品卡
        double totalGiftCardAmount = 0;
        if (bizTask.getGiftCardList() != null && !bizTask.getGiftCardList().isEmpty()) {
            for (BizTask.GiftCardInfo giftCard : bizTask.getGiftCardList()) {
                if (giftCard.getAmount() > 0) {
                    // 核销礼品卡，更新余额
                    String result = bizGiftCardDetailService.redeemGiftCard(giftCard.getCardNo(), giftCard.getPin(), String.valueOf(giftCard.getAmount()));
                    if (!result.equals("success")) {
                        return CommonResult.error(result);
                    }
                    totalGiftCardAmount += giftCard.getAmount();
                }
            }
        }
        bizTask.setGiftCard(String.valueOf(totalGiftCardAmount));
        // 安全转换金额字段，处理空值情况
        double cashAmount = parseDoubleValue(bizTask.getCash());
        bizTask.setTotalCostPrice(String.valueOf(cashAmount + totalGiftCardAmount));
        bizTask.setTotalDiscount(bizTask.getVoucher());
        // 核销优惠券
        String voucherValue = "0";
        if (ObjectUtil.isNotEmpty(bizTask.getVoucherNo())) {
            String result = bizVoucherService.redeemVoucher(bizTask.getVoucherNo(), bizTask.getTotalPrice());
            if (!result.contains("success")) {
                return CommonResult.error(result);
            }
            voucherValue = result.replace("success", "");
        }
        bizTask.setVoucher(voucherValue);
        // 安全转换金额字段，处理空值情况
        double voucherAmount = parseDoubleValue(voucherValue);
        double cardAmount = parseDoubleValue(bizTask.getCard());
        double insuranceAmount = bizTask.getInsuranceValue(); // double类型，已经是数值
        double totalPriceAmount = parseDoubleValue(bizTask.getTotalPrice());

        double paidValue = voucherAmount + totalGiftCardAmount + cashAmount + cardAmount + insuranceAmount;
        bizTask.setPaidValue(paidValue);
        if (paidValue == totalPriceAmount) {
            bizTask.setState("TASK_STATE_FINISH");
        }
        
        // 确保String类型的金额字段正确保存，将计算后的值保存回String字段
        bizTask.setCash(String.valueOf(cashAmount));
        bizTask.setCard(String.valueOf(cardAmount));
        
        bizTaskService.save(bizTask);

        // 保存多礼品卡信息
        if (bizTask.getGiftCardList() != null && !bizTask.getGiftCardList().isEmpty()) {
            List<BizTaskGiftCard> taskGiftCards = bizTask.getGiftCardList().stream()
                    .filter(gc -> gc.getAmount() > 0)
                    .map(gc -> {
                        BizTaskGiftCard taskGiftCard = new BizTaskGiftCard();
                        taskGiftCard.setTaskId(bizTask.getId());
                        taskGiftCard.setCardNo(gc.getCardNo());
                        taskGiftCard.setPin(gc.getPin());
                        taskGiftCard.setAmount(gc.getAmount());
                        taskGiftCard.setBalance(gc.getBalance());
                        taskGiftCard.setAvailableBalance(gc.getAvailableBalance());
                        taskGiftCard.setVerified(gc.isVerified());
                        return taskGiftCard;
                    }).toList();
            bizTaskGiftCardService.saveTaskGiftCards(bizTask.getId(), taskGiftCards);
        }

        // 记录礼品卡和优惠券的使用信息
        saveHistoryOfGiftCardAndVoucherNew(bizTask.getId(), bizTask.getGiftCardList(), bizTask.getVoucherNo(), voucherValue);

        // 保存甘特图统计信息
        BizTaskDataStatistics statics = new BizTaskDataStatistics();
        statics.setMainId(bizTask.getId());
        statics.setTaskName(taskName);
        statics.setStatus(bizTask.getState());
        statics.setRemark(bizTask.getDescription());
        statics.setProduct(JSON.toJSONString(bizTask.getBizTaskItemList()));
        statics.setStaff(staffName);
        statics.setOrgId(orgId);
        statics.setStaffId(staffName.equals("Unregistered") ? "Unregistered" : bizTask.getAssign());
        statics.setStartTime(bizTask.getStartTime());
        statics.setActualHours("0");
        statics.setCustomerId(bizTask.getCustomerId());
        statics.setCustomerName(bizTask.getCustomerName());
        if (bizTask.getState().equals("TASK_STATE_FINISH")) {
            statics.setProgress("1");
            statics.setActualHours(bizTask.getActualFinishTime());
        }
        String hour = bizTask.getTotalPlanHours().split(":")[0];
        String minute = bizTask.getTotalPlanHours().split(":")[1];
        Double totalMinutes = Convert.toDouble(hour) * 60 + Convert.toDouble(minute);
        Double totalHours = Convert.toDouble(hour) + Convert.toDouble(minute) / 60;
        statics.setPlanningHours(String.format("%.3f", totalHours));
        LocalDateTime start = LocalDateTime.parse(bizTask.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        statics.setPlanningEndTime(start.plusMinutes(Convert.toLong(totalMinutes)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        bizTaskDataStatisticsService.save(statics);

        BizTaskDataStatistics task = new BizTaskDataStatistics();
        BeanUtil.copyProperties(statics, task);
        task.setId(null);
        task.setParentId(statics.getId());
        task.setPlanningHours(null);
        task.setActualHours(null);
        bizTaskDataStatisticsService.save(task);

        // 保存任务子表信息
        for (BizTaskItem bizTaskItem : bizTaskItemList) {
            bizTaskItem.setMainId(bizTask.getId());
            bizTaskItem.setStaffId(bizTask.getAssign());
            bizTaskItem.setStaffName(staffName);
            bizTaskItem.setOrgId(orgId);
        }
        bizTaskItemService.saveBatch(bizTaskItemList);

        // 如果这个用户今天没有排班，则自动新增排班
        String assignDate = DateUtil.format(DateUtil.parse(bizTask.getStartTime(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd");
        List<BizSchedule> scheduleAlready = bizScheduleService.lambdaQuery()
                .eq(BizSchedule::getStaffId, bizTask.getAssign())
                .eq(BizSchedule::getAssignDate, assignDate)
                .isNotNull(BizSchedule::getWorkTime).list();
        if (scheduleAlready.isEmpty()) {
            BizSchedule bizSchedule = new BizSchedule();
            bizSchedule.setStaffId(statics.getStaffId());
            bizSchedule.setStaffName(staffName);
            bizSchedule.setAssignDate(assignDate);
            bizSchedule.setOrganizationId(orgId);
            bizSchedule.setOrganizationName("-");
            bizSchedule.setWorkTime("[\"08:00:00\",\"20:00:00\"]");
            bizScheduleService.save(bizSchedule);
        }
        return CommonResult.ok();
    }


    /**
     * 编辑任务信息
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    @Operation(summary = "编辑任务信息")
    @CommonLog("编辑任务信息")
    @SaCheckPermission("/biz/task/edit")
    @PostMapping("/biz/task/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizTask bizTask) {
        // 检查任务当前状态，如果是已完成状态，需要特殊权限
        BizTask taskOld = bizTaskService.queryEntity(bizTask.getId());
        if ("TASK_STATE_FINISH".equals(taskOld.getState())) {
            // 检查是否有编辑已完成任务的权限
            StpUtil.checkPermission("/biz/task/editFinished");
        }

        // 如果修改了开始时间，分配人员，则同步修改
        List<BizTaskDataStatistics> statistics = bizTaskDataStatisticsService.lambdaQuery().eq(BizTaskDataStatistics::getMainId, bizTask.getId()).list();
        BizTaskDataStatistics statisticsOne = statistics.stream().filter(statistic -> statistic.getId().equals(bizTask.getMainId())).toList().get(0);

        // 改开始时间
        // todo 结束时间计算
        if (!statisticsOne.getStartTime().equals(bizTask.getStartTime())) {
            statisticsOne.setStartTime(bizTask.getStartTime());
            Double hours = getHours(taskOld.getStartTime(), bizTask.getStartTime());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime endTime = LocalDateTime.parse(statisticsOne.getPlanningEndTime(), formatter).plus(Duration.ofHours(Convert.toLong(hours)));
            statisticsOne.setPlanningEndTime(endTime.format(formatter));
        }
        // 改员工
        if (ObjectUtil.isNotEmpty(bizTask.getAssign()) || !taskOld.getAssign().equals(bizTask.getAssign())) {
            String staffName = getStaffInfo(bizTask.getAssign()).get("staffName");
            String orgId = getStaffInfo(bizTask.getAssign()).get("orgId");
            bizTask.setStaff(staffName);
            bizTask.setOrgId(orgId);
            statisticsOne.setStaff(staffName);
            statisticsOne.setStaffId(bizTask.getAssign());
            statisticsOne.setOrgId(orgId);
        }
        // 改关联产品
        if (ObjectUtil.isNotEmpty(bizTask.getTotalPlanHours()) && ObjectUtil.isNotEmpty(taskOld.getTotalPlanHours()) && !taskOld.getTotalPlanHours().equals(bizTask.getTotalPlanHours())) {
            String hour = bizTask.getTotalPlanHours().split(":")[0];
            String minute = bizTask.getTotalPlanHours().split(":")[1];
            long totalMinutes = Long.parseLong(hour) * 60 + Long.parseLong(minute);
            LocalDateTime start = LocalDateTime.parse(bizTask.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            statisticsOne.setPlanningEndTime(start.plusMinutes(totalMinutes).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            statisticsOne.setProduct(JSON.toJSONString(bizTask.getBizTaskItemList()));
        }
        statisticsOne.setStatus(bizTask.getState());
        bizTaskDataStatisticsService.updateById(statisticsOne);

        // 在处理新的礼品卡之前，先恢复原有礼品卡的余额
        restoreGiftCardBalances(bizTask.getId());

        // 在处理新的voucher之前，先恢复原有voucher的状态
        bizVoucherService.restoreVoucherByTaskId(bizTask.getId());

        // 删除任务原有的使用记录，避免重复记录
        bizGiftCardLogService.remove(new LambdaQueryWrapper<BizGiftCardLog>().eq(BizGiftCardLog::getTaskId, bizTask.getId()));

        // 处理多礼品卡
        double totalGiftCardAmount = 0;
        if (bizTask.getGiftCardList() != null && !bizTask.getGiftCardList().isEmpty()) {
            for (BizTask.GiftCardInfo giftCard : bizTask.getGiftCardList()) {
                if (giftCard.getAmount() > 0) {
                    // 核销礼品卡，更新余额
                    String result = bizGiftCardDetailService.redeemGiftCard(giftCard.getCardNo(), giftCard.getPin(), String.valueOf(giftCard.getAmount()));
                    if (!result.equals("success")) {
                        return CommonResult.error(result);
                    }
                    totalGiftCardAmount += giftCard.getAmount();
                }
            }
        }
        bizTask.setGiftCard(String.valueOf(totalGiftCardAmount));

        // 核销优惠券
        String voucherValue = "0";
        if (ObjectUtil.isNotEmpty(bizTask.getVoucherNo())) {
            String result = bizVoucherService.redeemVoucher(bizTask.getVoucherNo(), bizTask.getTotalPrice());
            if (!result.contains("success")) {
                return CommonResult.error(result);
            }
            voucherValue = result.replace("success", "");
        }
        bizTask.setVoucher(voucherValue);

                // 安全转换金额字段，处理空值情况
        double voucherAmount = parseDoubleValue(voucherValue);
        double cashAmount = parseDoubleValue(bizTask.getCash());
        double cardAmount = parseDoubleValue(bizTask.getCard());
        double insuranceAmount = bizTask.getInsuranceValue(); // double类型，已经是数值
        double previousPaidAmount = bizTask.getPaidValue(); // double类型，已经是数值
        double totalPriceAmount = parseDoubleValue(bizTask.getTotalPrice());
        
        double paidValue = voucherAmount + totalGiftCardAmount + cashAmount + cardAmount + insuranceAmount + previousPaidAmount;
        bizTask.setPaidValue(paidValue);
        if (paidValue == totalPriceAmount) {
            bizTask.setState("TASK_STATE_FINISH");
        }
        
        // 确保String类型的金额字段正确保存，将计算后的值保存回String字段
        bizTask.setCash(String.valueOf(cashAmount));
        bizTask.setCard(String.valueOf(cardAmount));
        
        bizTaskService.updateById(bizTask);

        // 保存多礼品卡信息
        if (bizTask.getGiftCardList() != null && !bizTask.getGiftCardList().isEmpty()) {
            List<BizTaskGiftCard> taskGiftCards = bizTask.getGiftCardList().stream()
                    .filter(gc -> gc.getAmount() > 0)
                    .map(gc -> {
                        BizTaskGiftCard taskGiftCard = new BizTaskGiftCard();
                        taskGiftCard.setTaskId(bizTask.getId());
                        taskGiftCard.setCardNo(gc.getCardNo());
                        taskGiftCard.setPin(gc.getPin());
                        taskGiftCard.setAmount(gc.getAmount());
                        taskGiftCard.setBalance(gc.getBalance());
                        taskGiftCard.setAvailableBalance(gc.getAvailableBalance());
                        taskGiftCard.setVerified(gc.isVerified());
                        return taskGiftCard;
                    }).toList();
            bizTaskGiftCardService.saveTaskGiftCards(bizTask.getId(), taskGiftCards);
        }

        // 记录礼品卡和优惠券的使用信息
        saveHistoryOfGiftCardAndVoucherNew(bizTask.getId(), bizTask.getGiftCardList(), bizTask.getVoucherNo(), voucherValue);

        // 更新任务子表信息
        bizTaskItemService.remove(new LambdaQueryWrapper<BizTaskItem>().eq(BizTaskItem::getMainId, bizTask.getId()));
        for (BizTaskItem taskItem : bizTask.getBizTaskItemList()) {
            taskItem.setId(null);
            taskItem.setMainId(bizTask.getId());
        }
        bizTaskItemService.saveBatch(bizTask.getBizTaskItemList());

        // 如果将状态改为结束，更新最后一个任务以及主任务
        if (bizTask.getState().equals("TASK_STATE_FINISH")) {
            BizTaskDataStatistics taskMain = statistics.get(0);
            BizTaskDataStatistics taskLast = statistics.get(statistics.size() - 1);
            taskMain.setStatus("TASK_STATE_FINISH");
            taskLast.setStatus("TASK_STATE_FINISH");
            taskLast.setProgress("1");
            Double actualHours = getHours(taskLast.getStartTime(), DateUtil.now());
            Double totalActualHours = actualHours + Convert.toDouble(taskMain.getActualHours());
            taskMain.setActualHours(String.format("%.3f", totalActualHours));
        }
        bizTaskDataStatisticsService.updateBatchById(statistics);
        return CommonResult.ok();
    }

    /**
     * 删除任务信息
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    @Operation(summary = "删除任务信息")
    @CommonLog("删除任务信息")
    @SaCheckPermission("/biz/task/delete")
    @PostMapping("/biz/task/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizTaskIdParam> bizTaskIdParamList) {
        List<String> ids = bizTaskIdParamList.stream().map(BizTaskIdParam::getId).toList();

        // 在删除任务前，先恢复每个任务的礼品卡余额
        for (String taskId : ids) {
            restoreGiftCardBalances(taskId);
            // 恢复每个任务的voucher状态
            bizVoucherService.restoreVoucherByTaskId(taskId);
        }

        // 删除任务相关数据
        bizTaskService.delete(bizTaskIdParamList);
        bizTaskItemService.remove(new LambdaQueryWrapper<BizTaskItem>().in(BizTaskItem::getMainId, ids));
        bizTaskDataStatisticsService.remove(new LambdaQueryWrapper<BizTaskDataStatistics>().in(BizTaskDataStatistics::getMainId, ids));

        // 删除任务礼品卡关联数据
        for (String taskId : ids) {
            bizTaskGiftCardService.deleteByTaskId(taskId);
        }

        // 删除任务相关的礼品卡使用记录
        bizGiftCardLogService.remove(new LambdaQueryWrapper<BizGiftCardLog>().in(BizGiftCardLog::getTaskId, ids));

        return CommonResult.ok();
    }

    /**
     * 获取任务信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    @Operation(summary = "获取任务信息详情")
    @SaCheckPermission("/biz/task/detail")
    @GetMapping("/biz/task/detail")
    public CommonResult<BizTask> detail(@Valid BizTaskIdParam bizTaskIdParam, @Valid String taskId) {
        BizTaskDataStatistics taskDataStatistics = bizTaskDataStatisticsService.queryEntity(taskId);
        BizTask detail = bizTaskService.detail(bizTaskIdParam);
        detail.setStartTime(taskDataStatistics.getStartTime());
        detail.setState(taskDataStatistics.getStatus());
        detail.setPlanningEndTime(taskDataStatistics.getPlanningEndTime());
        detail.setBizTaskItemList(bizTaskItemService.list(new LambdaQueryWrapper<BizTaskItem>().eq(BizTaskItem::getMainId, detail.getId())));

        // 加载礼品卡列表
        List<BizTaskGiftCard> taskGiftCards = bizTaskGiftCardService.getByTaskId(detail.getId());
        if (!taskGiftCards.isEmpty()) {
            List<BizTask.GiftCardInfo> giftCardList = taskGiftCards.stream().map(tgc -> {
                BizTask.GiftCardInfo giftCardInfo = new BizTask.GiftCardInfo();
                giftCardInfo.setCardNo(tgc.getCardNo());
                giftCardInfo.setPin(tgc.getPin());
                giftCardInfo.setAmount(tgc.getAmount());
                giftCardInfo.setBalance(tgc.getBalance());
                giftCardInfo.setAvailableBalance(tgc.getAvailableBalance());
                giftCardInfo.setVerified(tgc.isVerified());
                return giftCardInfo;
            }).toList();
            detail.setGiftCardList(giftCardList);
        }

        return CommonResult.data(detail);
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    @Operation(summary = "获取任务信息动态字段的配置")
    @SaCheckPermission("/biz/task/dynamicFieldConfigList")
    @GetMapping("/biz/task/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizTaskService.dynamicFieldConfigList(columnName));
    }

    /** 计算主任务进度 */
    private static Double getProgress(Double actualHours, Double planningHours) {
        if (planningHours == 0 || planningHours == null) {
            return 0.0;
        }
        return actualHours / planningHours;
    }

    /** 计算时间差，返回小时 */
    private static Double getHours(String startTime, String endTime) {
        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        long seconds = Duration.between(start, end).toSeconds();
        return seconds / 3600.0;
    }


    /**
     * 记录礼品卡和优惠券的使用记录
     */
    private void saveHistoryOfGiftCardAndVoucher(String taskId, String giftCardNo, String giftCardValue, String voucherNo, String voucherValue) {
        List<BizGiftCardLog> logs = new ArrayList<>();

        // 获取当前登录用户的orgId
        String currentUserOrgId = null;
        try {
            currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
        } catch (Exception e) {
            // 获取失败时继续执行，orgId为空
        }

        if (ObjectUtil.isNotEmpty(giftCardNo)) {
            BizGiftCardLog giftCard = new BizGiftCardLog();
            giftCard.setTaskId(taskId);
            giftCard.setCardNo(giftCardNo);
            giftCard.setUseValue(giftCardValue);
            giftCard.setType("giftcard");
            // 设置orgId
            if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                giftCard.setOrgId(currentUserOrgId);
            }
            logs.add(giftCard);
        }
        if (ObjectUtil.isNotEmpty(voucherNo)) {
            BizGiftCardLog voucher = new BizGiftCardLog();
            voucher.setTaskId(taskId);
            voucher.setCardNo(voucherNo);
            voucher.setUseValue(voucherValue);
            voucher.setType("voucher");
            // 设置orgId
            if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                voucher.setOrgId(currentUserOrgId);
            }
            logs.add(voucher);
        }
        bizGiftCardLogService.saveBatch(logs);
    }

    /**
     * 记录多礼品卡和优惠券的使用记录
     */
    private void saveHistoryOfGiftCardAndVoucherNew(String taskId, List<BizTask.GiftCardInfo> giftCardList, String voucherNo, String voucherValue) {
        List<BizGiftCardLog> logs = new ArrayList<>();

        // 获取当前登录用户的orgId
        String currentUserOrgId = null;
        try {
            currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
        } catch (Exception e) {
            // 获取失败时继续执行，orgId为空
        }

        // 记录多张礼品卡
        if (giftCardList != null && !giftCardList.isEmpty()) {
            for (BizTask.GiftCardInfo giftCard : giftCardList) {
                if (giftCard.getAmount() > 0) {
                    BizGiftCardLog giftCardLog = new BizGiftCardLog();
                    giftCardLog.setTaskId(taskId);
                    giftCardLog.setCardNo(giftCard.getCardNo());
                    giftCardLog.setUseValue(String.valueOf(giftCard.getAmount()));
                    giftCardLog.setType("giftcard");
                    // 设置orgId
                    if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                        giftCardLog.setOrgId(currentUserOrgId);
                    }
                    logs.add(giftCardLog);
                }
            }
        }

        // 记录优惠券
        if (ObjectUtil.isNotEmpty(voucherNo)) {
            BizGiftCardLog voucher = new BizGiftCardLog();
            voucher.setTaskId(taskId);
            voucher.setCardNo(voucherNo);
            voucher.setUseValue(voucherValue);
            voucher.setType("voucher");
            // 设置orgId
            if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                voucher.setOrgId(currentUserOrgId);
            }
            logs.add(voucher);
        }

        if (!logs.isEmpty()) {
            bizGiftCardLogService.saveBatch(logs);
        }
    }

    private String getTaskName(String productId) {
        String taskName = flwTemplateSnService.getTemplateValueByCode("FLJpIOdlIX");
        if (taskName == null) {
            return new Date().getTime() + "";
        }
        // 去除"TASK"前缀以及年份，在月日与序号中间拼接第一个产品号
        return new StringBuffer(taskName.substring(8)).insert(4, "-" + productId + "-").toString();
    }

    private Map<String, String> getStaffInfo(String userId) {
        Map<String, String> result = new HashMap<>();
        result.put("staffName", "Unregistered");
        result.put("orgId", "Unregistered");
        if (StrUtil.isNotBlank(userId) && !"Unregistered".equals(userId)) {
            SysUser user = sysUserService.lambdaQuery().eq(SysUser::getId, userId).one();
            result.put("staffName", user.getName());
            result.put("orgId", user.getOrgId());
        }
        return result;
    }

    /**
     * 恢复礼品卡余额
     * @param taskId 任务ID
     */
    private void restoreGiftCardBalances(String taskId) {
        try {
            // 获取任务关联的礼品卡列表
            List<BizTaskGiftCard> taskGiftCards = bizTaskGiftCardService.getByTaskId(taskId);

            if (taskGiftCards != null && !taskGiftCards.isEmpty()) {
                for (BizTaskGiftCard taskGiftCard : taskGiftCards) {
                    // 调用礼品卡服务恢复余额
                    bizGiftCardDetailService.restoreGiftCardBalance(
                            taskGiftCard.getCardNo(),
                            taskGiftCard.getPin(),
                            String.valueOf(taskGiftCard.getAmount())
                    );
                }
            }
        } catch (Exception e) {
            // 记录错误日志，但不阻止删除操作
            System.err.println("恢复任务 " + taskId + " 的礼品卡余额时发生错误: " + e.getMessage());
        }
    }

    /**
     * 检查是否有编辑已完成任务的权限
     *
     * <AUTHOR>
     * @date 2024/12/17
     */
    @Operation(summary = "检查是否有编辑已完成任务的权限")
    @SaCheckPermission("/biz/task/editFinished")
    @GetMapping("/biz/task/editFinished")
    public CommonResult<Boolean> editFinished() {
        return CommonResult.data(true);
    }

    /**
     * 安全地将字符串转换为double值，处理null和空字符串的情况
     * @param value 要转换的字符串值
     * @return 转换后的double值，如果输入为null或空字符串则返回0.0
     */
    private double parseDoubleValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return 0.0;
        }
        try {
            return Double.parseDouble(value.trim());
        } catch (NumberFormatException e) {
            // 如果转换失败，记录警告并返回0.0
            return 0.0;
        }
    }
}
