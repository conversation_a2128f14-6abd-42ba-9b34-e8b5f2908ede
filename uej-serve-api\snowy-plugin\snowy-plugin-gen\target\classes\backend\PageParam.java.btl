
package ${packageName}.${moduleName}.modular.${busName}.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ${functionName}查询参数
 *
 * <AUTHOR>
 * @date ${genTime}
 **/
@Getter
@Setter
public class ${className}PageParam {

    /** 当前页 */
    @Schema(description = "当前页码")
    private Integer current;

    /** 每页条数 */
    @Schema(description = "每页条数")
    private Integer size;

    /** 排序字段 */
    @Schema(description = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @Schema(description = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @Schema(description = "关键词")
    private String searchKey;

    <% for(var i = 0; i < configList.~size; i++) { %>
    <% if(configList[i].needPage) { %>
    <% if(configList[i].effectType == 'datepicker') { %>
    /** ${configList[i].fieldRemark}开始 */
    @Schema(description = "${configList[i].fieldRemark}开始")
    private String start${configList[i].fieldNameCamelCaseFirstUpper};

    /** ${configList[i].fieldRemark}结束 */
    @Schema(description = "${configList[i].fieldRemark}结束")
    private String end${configList[i].fieldNameCamelCaseFirstUpper};

    <% } else { %>
    /** ${configList[i].fieldRemark} */
    @Schema(description = "${configList[i].fieldRemark}")
    private ${configList[i].fieldJavaType} ${configList[i].fieldNameCamelCase};

    <% } %>
    <% } %>
    <% } %>
}
