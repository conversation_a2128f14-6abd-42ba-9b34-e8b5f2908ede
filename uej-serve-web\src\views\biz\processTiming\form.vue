<template>
	<DragModal :border="false" :visible="visible" @close="onClose" :width="1000" title="Task switching" @ok="submit">
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal">
			<a-row :gutter="30">
				<a-col :span="12">
					<a-form-item label="staffId:" name="staffId">
						<xn-user-selector
							:org-tree-api="selectorApiFunction.orgTreeApi"
							:user-page-api="selectorApiFunction.userPageApi"
							:user-list-by-id-list-api="selectorApiFunction.checkedUserListApi"
							:radio-model="true"
							v-model:value="formData.staffId"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="startTime:" name="startTime">
						<a-date-picker
							v-model:value="formData.startTime"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="Please enter startTime"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="planningEndTime:" name="planningEndTime">
						<a-date-picker
							v-model:value="formData.planningEndTime"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="Please enter planningEndTime"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-transfer
			v-model:target-keys="targetKeys"
			:data-source="dataSource"
			:show-search="false"
			:filter-option="(inputValue, item) => item.title.indexOf(inputValue) !== -1"
			:show-select-all="false"
			@change="onChange"
		>
			<template
				#children="{ direction, filteredItems, selectedKeys, disabled: listDisabled, onItemSelectAll, onItemSelect }"
			>
				<a-table
					:row-selection="
						getRowSelection({
							disabled: listDisabled,
							selectedKeys,
							onItemSelectAll,
							onItemSelect
						})
					"
					:columns="columns"
					:data-source="filteredItems"
					size="small"
					:style="{ pointerEvents: listDisabled ? 'none' : null }"
					:custom-row="
						({ key, disabled: itemDisabled }) => ({
							onClick: () => {
								if (itemDisabled || listDisabled) return
								onItemSelect(key, !selectedKeys.includes(key))
							}
						})
					"
				/>
			</template>
		</a-transfer>
	</DragModal>
</template>

<script setup>
	import { cloneDeep } from 'lodash-es'
	import { message } from 'ant-design-vue'
	import { required } from '@/utils/formRules'
	import bizScheduleDateApi from '@/api/biz/bizScheduleDateApi'
	import bizOrgApi from '@/api/biz/bizOrgApi'
	import userCenterApi from '@/api/sys/userCenterApi'
	import bizTaskDataStatisticsApi from '@/api/biz/bizTaskDataStatisticsApi'
	import dayjs from 'dayjs'
	const visible = ref(false)
	const formRef = ref()
	const formData = ref({})
	const emit = defineEmits(['success'])

	const dataSource = ref([])
	const columns = [
		{
			dataIndex: 'productName',
			title: 'productName'
		},
		{
			title: 'listPrice',
			dataIndex: 'listPrice'
		},
		{
			title: 'planningHours',
			dataIndex: 'planningHours'
		},
		{
			title: 'type',
			dataIndex: 'type'
		}
	]
	const targetKeys = ref()
	const onChange = (nextTargetKeys) => {

	}
	const getRowSelection = ({ disabled, selectedKeys, onItemSelectAll, onItemSelect }) => {
		return {
			getCheckboxProps: (item) => ({
				disabled: disabled || item.disabled
			}),
			onSelectAll(selected, selectedRows) {
				const treeSelectedKeys = selectedRows.filter((item) => !item.disabled).map(({ key }) => key)
				onItemSelectAll(treeSelectedKeys, selected)
			},
			onSelect({ key }, selected) {
				onItemSelect(key, selected)
			},
			selectedRowKeys: selectedKeys
		}
	}

	const onOpen = (record) => {
		visible.value = true
		let recordData = cloneDeep(record)
		formData.value = {
			id: recordData.taskId,
			mainId: recordData.mainId,
			startTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
		}
		bizTaskDataStatisticsApi
			.bizTaskDataStatisticsGetRemainProduct({ taskId: recordData.taskId, mainId: recordData.mainId })
			.then((res) => {
				dataSource.value = res.map((item) => {
					return {
						...item,
						key: item.id
					}
				})
			})
	}
	// 默认要校验的
	const formRules = {
		staffId: [required('staffId')]
	}
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		dataSource.value = []
		targetKeys.value = []
		visible.value = false
	}
	const submit = () => {
		formRef.value.validate().then(() => {
			const formDataParam = cloneDeep(formData.value)
			if (!targetKeys.value || targetKeys.value.length === 0) {
				return message.error('Select the service or product you want to hand over')
			}
			formDataParam.bizTaskItemList = dataSource.value.filter((item) => targetKeys.value.includes(item.key))
			bizTaskDataStatisticsApi.bizTaskDataStatisticsTransfer(formDataParam).then((res) => {
				emit('success', formDataParam)
				onClose()
			})
		})
	}
	// 传递设计器需要的API
	const selectorApiFunction = {
		orgTreeApi: (param) => {
			return bizOrgApi.orgTreeSelector(param).then((data) => {
				return Promise.resolve(data)
			})
		},
		userPageApi: (param) => {
			return bizOrgApi.orgUserSelector(param).then((data) => {
				return Promise.resolve(data)
			})
		},
		checkedUserListApi: (param) => {
			return userCenterApi.userCenterGetUserListByIdList(param).then((data) => {
				return Promise.resolve(data)
			})
		}
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>

<style lang="less" scoped></style>
