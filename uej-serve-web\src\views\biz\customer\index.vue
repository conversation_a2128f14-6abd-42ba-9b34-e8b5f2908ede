<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
            <a-row :gutter="24">
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
                    <a-form-item label="Phone" name="phone">
                        <a-input v-model:value="searchFormState.phone" placeholder="Please enter Phone" @pressEnter="tableRef.refresh(true)"
                        />
                    </a-form-item>
                </a-col>
                <!--a-col :xs="24" :sm="12" :md="8" :xl="6">
                    <a-form-item label="First Name" name="firstName">
                        <a-input v-model:value="searchFormState.firstName" placeholder="Please enter First Name" />
                    </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :xl="6">
                    <a-form-item label="Sur Name" name="lastName">
                        <a-input v-model:value="searchFormState.lastName" placeholder="Please enter Sur Name" />
                    </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :xl="6" v-show="advanced">
                    <a-form-item label="Gender" name="gender">
                        <a-input v-model:value="searchFormState.gender" placeholder="Please enter Gender" />
                    </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :xl="6" v-show="advanced">
                    <a-form-item label="Level" name="level">
                        <a-input v-model:value="searchFormState.level" placeholder="Please enter Level" />
                    </a-form-item>
                </a-col-->
                <a-col :xs="24" :sm="12" :md="8" :xl="6">
                    <a-button type="primary" @click="tableRef.refresh(true)">{{ $t('common.searchButton') }}</a-button>
                    <a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
                    <a @click="toggleAdvanced" style="margin-left: 8px">
                        {{ advanced ? $t('common.packUp') : $t('common.unfold') }}
                        <component :is="advanced ? 'up-outlined' : 'down-outlined'"/>
                    </a>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="tableRef"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.id"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection"
			:scroll="{x:'max-content'}"
        >
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('bizCustomerAdd')">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.addButton') }}
                    </a-button>
                    <xn-batch-delete
                        v-if="hasPerm('bizCustomerBatchDelete')"
                        :selectedRowKeys="selectedRowKeys"
                        @batchDelete="deleteBatchBizCustomer"
						:buttonName="$t('common.batchRemoveButton')"
                    />
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'signature'">
							<a @click="openQrcodeDialog(record)">view</a>
						</template>
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('bizCustomerEdit')">{{ $t('common.editButton') }}</a>
                        <a-divider type="vertical" v-if="hasPerm(['bizCustomerEdit', 'bizCustomerDelete'], 'and')" />
                        <a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizCustomer(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('bizCustomerDelete')">{{ $t('common.removeButton') }}</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="tableRef.refresh(true)" />
</template>

<script setup name="customer">
	import VueQrcode from '@chenfengyuan/vue-qrcode'
	import { h } from 'vue'
	import { message, Empty,Modal  } from 'ant-design-vue'
    import { cloneDeep } from 'lodash-es'
    import Form from './form.vue'
    import bizCustomerApi from '@/api/biz/bizCustomerApi'
    const searchFormState = ref({})
    const searchFormRef = ref()
    const tableRef = ref()
    const formRef = ref()
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    // 查询区域显示更多控制
    const advanced = ref(false)
    const toggleAdvanced = () => {
        advanced.value = !advanced.value
    }
    const columns = [
        {
            title: 'First Name',
            dataIndex: 'firstName'
        },
        {
            title: 'Sur Name',
            dataIndex: 'lastName'
        },
        {
            title: 'Gender',
            dataIndex: 'gender'
        },
        {
            title: 'Level',
            dataIndex: 'level'
        },
        {
            title: 'Reward Points',
            dataIndex: 'rewardPoints'
        },
        {
            title: 'Attend Time',
            dataIndex: 'attendTime'
        },
        {
            title: 'Email',
            dataIndex: 'email'
        },

        {
            title: 'Phone',
            dataIndex: 'phone'
        },
		{
            title: 'insurance company',
            dataIndex: 'insuranceCompany'
        },
		{
            title: 'memberId',
            dataIndex: 'memberId'
        },
		{
			title:'signature',
			dataIndex: 'signature',
		},
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['bizCustomerEdit', 'bizCustomerDelete'])) {
        columns.push({
            title: 'action',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        })
    }
    const selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        // columns数字类型字段加入 needTotal: true 可以勾选自动算账
        alert: {
            show: true,
            clear: () => {
                selectedRowKeys.value = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
        const searchFormParam = cloneDeep(searchFormState.value)
        return bizCustomerApi.bizCustomerPage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    // 重置
    const reset = () => {
        searchFormRef.value.resetFields()
        tableRef.value.refresh(true)
    }
    // 删除
    const deleteBizCustomer = (record) => {
        let params = [
            {
                id: record.id
            }
        ]
        bizCustomerApi.bizCustomerDelete(params).then(() => {
            tableRef.value.refresh(true)
        })
    }
    // 批量删除
    const deleteBatchBizCustomer = (params) => {
        bizCustomerApi.bizCustomerDelete(params).then(() => {
            tableRef.value.clearRefreshSelected()
        })
    }
	const openQrcodeDialog = (record) => {
		Modal.info({
			title: record.name,
			content: h(
				VueQrcode,
				{
					value: window.location.origin + '/biz/signature?id=' + record.id
				},
				{}
			),
			onOk() {}
		})
	}
</script>
