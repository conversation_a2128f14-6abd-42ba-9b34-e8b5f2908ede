
package vip.xiaonuo.flw.modular.task.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.List;

/**
 * 审批加签参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:45
 */
@Getter
@Setter
public class FlwTaskAddSignParam {

    /** 任务Id */
    @Schema(description = "任务Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 当前节点id */
    @Schema(description = "当前节点id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "currentActivityId不能为空")
    private String currentActivityId;

    /** 加签人员id集合 */
    @Valid
    @Schema(description = "加签人员id集合", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "assigneeList不能为空")
    private List<String> assigneeList;

    /** 意见 */
    @Schema(description = "意见")
    private String comment;


    /** 填写数据 */
    @Schema(description = "填写数据")
    private String dataJson;

    /** 附件信息 */
    @Schema(description = "附件信息")
    private List<FlwTaskAttachmentParam> attachmentList;
}
