<template>
	<DragModal
		:border="false"
		:visible="props.visible"
		@close="onClose"
		:width="500"
		title="Import"
		@ok="submit"
		:confirmLoading="loading"
		:destroyOnClose="true"
	>
		<a-alert
			type="info"
			message="Please download the template, fill in the data, and then import it"
			banner
			class="mb-[16px]"
		>
			<template #action>
				<a-button type="primary" size="small" @click="downloadTemplate">Download template</a-button>
			</template>
		</a-alert>
		<a-form ref="formRef" :model="formData" :rules="formRules">
			<a-form-item label="file" name="file">
				<a-upload
					v-model:file-list="fileList"
					:before-upload="beforeUpload"
					:max-count="1"
					accept=".xlsx,.xls"
					@remove="handleRemove"
				>
					<a-button>
						<upload-outlined></upload-outlined>
						Select File
					</a-button>
				</a-upload>
			</a-form-item>
		</a-form>
	</DragModal>
</template>

<script setup>
	import { UploadOutlined } from '@ant-design/icons-vue'
	import { message } from 'ant-design-vue'
	import { required } from '@/utils/formRules'
	
	const props = defineProps({
		visible: {
			type: Boolean,
			default: false
		},
		downloadUrl: {
			type: String,
			default: ''
		},
		uploadUrl: {
			type: Function,
			default: () => undefined
		}
	})
	
	const loading = ref(false)
	const emit = defineEmits(['update:visible', 'successful'])
	const formRef = ref()
	const formData = ref({})
	const fileList = ref([])

	const formRules = {
		file: [required('Please select a file')]
	}

	const onClose = () => {
		loading.value = false
		formRef.value.resetFields()
		formData.value = {}
		fileList.value = []
		emit('update:visible', false)
	}

	const downloadTemplate = () => {
		const a = document.createElement('a')
		a.href = props.downloadUrl
		a.click()
		a.remove()
	}

	const beforeUpload = (file) => {
		// 检查文件类型
		const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
						file.type === 'application/vnd.ms-excel'
		if (!isExcel) {
			message.error('只能上传 Excel 文件!')
			return false
		}
		
		// 检查文件大小 (限制为10MB)
		const isLt10M = file.size / 1024 / 1024 < 10
		if (!isLt10M) {
			message.error('文件大小不能超过 10MB!')
			return false
		}
		
		formData.value.file = file
		return false // 阻止自动上传
	}

	const handleRemove = () => {
		formData.value.file = null
		fileList.value = []
	}

	const submit = () => {
		if (!formData.value.file) {
			message.error('Please select a file')
			return
		}
		
		loading.value = true
		
		// 创建FormData对象，直接上传文件
		const uploadFormData = new FormData()
		uploadFormData.append('file', formData.value.file)
		
		// 直接调用导入接口，传递文件而不是URL
		props.uploadUrl(uploadFormData).then(() => {
			message.success('Import successful')
			emit('successful')
			onClose()
		}).catch((error) => {
			console.error('Import failed:', error)
			message.error('Import failed')
		}).finally(() => {
			loading.value = false
		})
	}
</script>

<style lang="less" scoped></style>
