
package vip.xiaonuo.biz.modular.offering.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务或产品清单添加参数
 *
 * <AUTHOR>
 * @date  2024/06/12 16:09
 **/
@Getter
@Setter
public class BizOfferingAddParam {

    /** Category */
    @Schema(description = "Category")
    private String category;

    /** Name */
    @Schema(description = "Name")
    private String name;

    /** Planning Hours */
    @Schema(description = "Planning Hours")
    private String planningHours;

    /** Invoice Hours */
    @Schema(description = "Invoice Hours")
    private String invoiceHours;

    /** List Price */
    @Schema(description = "List Price")
    private String listPrice;

    /** Cost Price */
    @Schema(description = "Cost Price")
    private String costPrice;

    private String typeId;

    private String type;

    private String extJson;
}
