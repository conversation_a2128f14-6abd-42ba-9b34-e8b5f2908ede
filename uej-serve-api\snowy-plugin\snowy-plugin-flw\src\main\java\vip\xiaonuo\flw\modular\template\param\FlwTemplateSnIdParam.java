
package vip.xiaonuo.flw.modular.template.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 流水号模板Id参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:14
 */
@Getter
@Setter
public class FlwTemplateSnIdParam {

    /** id */
    @Schema(description = "id")
    @NotBlank(message = "id不能为空")
    private String id;
}
