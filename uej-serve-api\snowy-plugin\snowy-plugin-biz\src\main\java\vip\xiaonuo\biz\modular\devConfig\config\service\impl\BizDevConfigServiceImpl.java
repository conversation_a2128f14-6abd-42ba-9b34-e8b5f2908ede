
package vip.xiaonuo.biz.modular.devConfig.config.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.modular.devConfig.config.entity.BizDevConfig;
import vip.xiaonuo.biz.modular.devConfig.config.enums.BizDevConfigCategoryEnum;
import vip.xiaonuo.biz.modular.devConfig.config.mapper.BizDevConfigMapper;
import vip.xiaonuo.biz.modular.devConfig.config.param.*;
import vip.xiaonuo.biz.modular.devConfig.config.service.BizDevConfigService;
import vip.xiaonuo.common.cache.CommonCacheOperator;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.ten.api.TenApi;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 配置Service接口实现类
 *
 * <AUTHOR>
 * @date 2022/4/22 10:41
 **/
@Service
public class BizDevConfigServiceImpl extends ServiceImpl<BizDevConfigMapper, BizDevConfig> implements BizDevConfigService {

    private static final String CONFIG_CACHE_KEY = "dev-config:";

    private static final String SNOWY_SYS_DEFAULT_PASSWORD_KEY = "SNOWY_SYS_DEFAULT_PASSWORD";

    @Resource
    private TenApi tenApi;

    @Resource
    private CommonCacheOperator commonCacheOperator;

    @Override
    public String getValueByKey(String key) {
        // 缓存的键前缀
        String cacheKeyPrefix = CONFIG_CACHE_KEY + tenApi.getCurrentTenDomain() + ":";

        // 从缓存中取
        Object cacheValue = commonCacheOperator.get(cacheKeyPrefix + key);
        if(ObjectUtil.isNotEmpty(cacheValue)) {
            return Convert.toStr(cacheValue);
        }
        BizDevConfig devConfig = this.getOne(new LambdaQueryWrapper<BizDevConfig>().eq(BizDevConfig::getConfigKey, key));
        if(ObjectUtil.isNotEmpty(devConfig)) {
            // 更新到缓存
            commonCacheOperator.put(cacheKeyPrefix + key, devConfig.getConfigValue());
            return devConfig.getConfigValue();
        }
        return null;
    }

    @Override
    public Page<BizDevConfig> page(DevConfigPageParam devConfigPageParam) {
        QueryWrapper<BizDevConfig> queryWrapper = new QueryWrapper<BizDevConfig>().checkSqlInjection();
        // 查询部分字段
        queryWrapper.lambda().select(BizDevConfig::getId, BizDevConfig::getConfigKey, BizDevConfig::getConfigValue,
                BizDevConfig::getCategory, BizDevConfig::getRemark, BizDevConfig::getSortCode);
        if(ObjectUtil.isNotEmpty(devConfigPageParam.getSearchKey())) {
            queryWrapper.lambda().like(BizDevConfig::getConfigKey, devConfigPageParam.getSearchKey());
        }
        if(ObjectUtil.isAllNotEmpty(devConfigPageParam.getSortField(), devConfigPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(devConfigPageParam.getSortOrder());
            queryWrapper.orderBy(true, devConfigPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(devConfigPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizDevConfig::getSortCode);
        }
        queryWrapper.lambda().eq(BizDevConfig::getCategory, BizDevConfigCategoryEnum.BIZ_DEFINE.getValue());
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<BizDevConfig> sysBaseList() {
        DevConfigListParam devConfigListParam = new DevConfigListParam();
        devConfigListParam.setCategory(BizDevConfigCategoryEnum.SYS_BASE.getValue());
        return this.list(devConfigListParam).stream().filter(devConfig -> !devConfig.getConfigKey()
                .equals(SNOWY_SYS_DEFAULT_PASSWORD_KEY)).collect(Collectors.toList());
    }

    @Override
    public List<BizDevConfig> list(DevConfigListParam devConfigListParam) {
        LambdaQueryWrapper<BizDevConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 查询部分字段
        lambdaQueryWrapper.select(BizDevConfig::getId, BizDevConfig::getConfigKey, BizDevConfig::getConfigValue,
                BizDevConfig::getCategory, BizDevConfig::getSortCode);
        if(ObjectUtil.isNotEmpty(devConfigListParam.getCategory())) {
            lambdaQueryWrapper.eq(BizDevConfig::getCategory, devConfigListParam.getCategory());
        }
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public void add(DevConfigAddParam devConfigAddParam) {
        checkParam(devConfigAddParam);
        BizDevConfig devConfig = BeanUtil.toBean(devConfigAddParam, BizDevConfig.class);
        devConfig.setCategory(BizDevConfigCategoryEnum.BIZ_DEFINE.getValue());
        this.save(devConfig);
    }

    private void checkParam(DevConfigAddParam devConfigAddParam) {
        boolean hasSameConfig = this.count(new LambdaQueryWrapper<BizDevConfig>()
                .eq(BizDevConfig::getConfigKey, devConfigAddParam.getConfigKey())) > 0;
        if (hasSameConfig) {
            throw new CommonException("存在重复的配置，配置键为：{}", devConfigAddParam.getConfigKey());
        }
    }

    @Override
    public void edit(DevConfigEditParam devConfigEditParam) {
        BizDevConfig devConfig = this.queryEntity(devConfigEditParam.getId());
        checkParam(devConfigEditParam);
        BeanUtil.copyProperties(devConfigEditParam, devConfig);
        devConfig.setCategory(BizDevConfigCategoryEnum.BIZ_DEFINE.getValue());
        this.updateById(devConfig);
        // 移除对应的缓存
        commonCacheOperator.remove(CONFIG_CACHE_KEY + tenApi.getCurrentTenDomain() + ":" + devConfig.getConfigKey());
    }

    private void checkParam(DevConfigEditParam devConfigEditParam) {
        boolean hasSameConfig = this.count(new LambdaQueryWrapper<BizDevConfig>()
                .eq(BizDevConfig::getConfigKey, devConfigEditParam.getConfigKey())
                .ne(BizDevConfig::getId, devConfigEditParam.getId())) > 0;
        if (hasSameConfig) {
            throw new CommonException("存在重复的配置，配置键为：{}", devConfigEditParam.getConfigKey());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<DevConfigIdParam> devConfigIdParamList) {
        List<String> devConfigIdList = CollStreamUtil.toList(devConfigIdParamList, DevConfigIdParam::getId);
        if(ObjectUtil.isNotEmpty(devConfigIdList)) {
            List<BizDevConfig> devConfigList = this.listByIds(devConfigIdList);
            if(ObjectUtil.isNotEmpty(devConfigList)) {
                List<String> devConfigResultList = CollectionUtil.newArrayList(devConfigList.stream()
                        .map(BizDevConfig::getCategory).collect(Collectors.toSet()));
                if (devConfigResultList.size() != 1 || !devConfigResultList.get(0).equals(BizDevConfigCategoryEnum.BIZ_DEFINE.getValue())) {
                    throw new CommonException("不可删除系统内置配置");
                }
                List<BizDevConfig> deleteDevConfigList = this.listByIds(devConfigIdList);
                // 执行删除
                this.removeByIds(devConfigIdList);

                deleteDevConfigList.forEach(devConfig -> {
                    // 移除对应的缓存
                    commonCacheOperator.remove(CONFIG_CACHE_KEY + tenApi.getCurrentTenDomain() + ":" + devConfig.getConfigKey());
                });
            }
        }
    }

    @Override
    public BizDevConfig detail(DevConfigIdParam devConfigIdParam) {
        return this.queryEntity(devConfigIdParam.getId());
    }

    @Override
    public BizDevConfig queryEntity(String id) {
        BizDevConfig devConfig = this.getById(id);
        if(ObjectUtil.isEmpty(devConfig)) {
            throw new CommonException("配置不存在，id值为：{}", id);
        }
        return devConfig;
    }

    @Override
    public void editBatch(List<DevConfigBatchParam> devConfigBatchParamList) {
        devConfigBatchParamList.forEach(devConfigBatchParam -> {
            this.update(new LambdaUpdateWrapper<BizDevConfig>()
                    .eq(BizDevConfig::getConfigKey, devConfigBatchParam.getConfigKey())
                    .set(BizDevConfig::getConfigValue, devConfigBatchParam.getConfigValue()));
            // 移除对应的缓存
            commonCacheOperator.remove(CONFIG_CACHE_KEY + tenApi.getCurrentTenDomain() + ":" + devConfigBatchParam.getConfigKey());
        });
    }
}
