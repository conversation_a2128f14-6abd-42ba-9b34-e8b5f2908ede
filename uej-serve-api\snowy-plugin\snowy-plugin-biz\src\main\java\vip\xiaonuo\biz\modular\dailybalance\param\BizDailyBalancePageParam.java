
package vip.xiaonuo.biz.modular.dailybalance.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目结款记录表查询参数
 *
 * <AUTHOR>
 * @date  2024/12/02 13:50
 **/
@Getter
@Setter
public class BizDailyBalancePageParam {

    /** 当前页 */
    @Schema(description = "当前页码")
    private Integer current;

    /** 每页条数 */
    @Schema(description = "每页条数")
    private Integer size;

    /** 排序字段 */
    @Schema(description = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @Schema(description = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @Schema(description = "关键词")
    private String searchKey;

    /** remark */
    @Schema(description = "remark")
    private String remark;

    /** shop name */
    @Schema(description = "shop name")
    private String orgName;

}
