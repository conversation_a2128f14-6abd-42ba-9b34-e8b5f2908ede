
package vip.xiaonuo.biz.modular.giftcardlog.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 礼品卡记录Id参数
 *
 * <AUTHOR>
 * @date  2024/06/13 09:46
 **/
@Getter
@Setter
public class BizGiftCardLogIdParam {

    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;
}
