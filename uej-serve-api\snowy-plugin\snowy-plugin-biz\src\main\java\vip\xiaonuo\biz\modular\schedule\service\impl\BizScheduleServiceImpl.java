package vip.xiaonuo.biz.modular.schedule.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.biz.modular.schedule.mapper.BizScheduleMapper;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleAddParam;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleEditParam;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleIdParam;
import vip.xiaonuo.biz.modular.schedule.param.BizSchedulePageParam;
import vip.xiaonuo.biz.modular.schedule.service.BizScheduleService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;

import java.util.List;

/**
 * 排班Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/18 16:16
 **/
@Service
public class BizScheduleServiceImpl extends ServiceImpl<BizScheduleMapper, BizSchedule> implements BizScheduleService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizSchedule> page(BizSchedulePageParam bizSchedulePageParam) {
        QueryWrapper<BizSchedule> queryWrapper = new QueryWrapper<BizSchedule>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizSchedulePageParam.getStaffName())) {
            queryWrapper.lambda().like(BizSchedule::getStaffName, bizSchedulePageParam.getStaffName());
        }
        if(ObjectUtil.isAllNotEmpty(bizSchedulePageParam.getSortField(), bizSchedulePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizSchedulePageParam.getSortOrder());
            queryWrapper.orderBy(true, bizSchedulePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizSchedulePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizSchedule::getId);
        }
        
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizSchedule::getOrganizationId, loginUserDataScope);
        } else {
            // 如果没有数据范围权限，只能查看自己的排班记录
            queryWrapper.lambda().eq(BizSchedule::getStaffId, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizScheduleAddParam bizScheduleAddParam) {
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(ObjectUtil.isNotEmpty(bizScheduleAddParam.getOrganizationId()) && !loginUserDataScope.contains(bizScheduleAddParam.getOrganizationId())) {
                throw new CommonException("您没有权限在该机构下创建排班，机构id：{}", bizScheduleAddParam.getOrganizationId());
            }
        } else {
            // 如果没有数据范围权限，只能为自己创建排班
            if(ObjectUtil.isNotEmpty(bizScheduleAddParam.getStaffId()) && !bizScheduleAddParam.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限为该员工创建排班，员工id：{}", bizScheduleAddParam.getStaffId());
            }
        }
        
        BizSchedule bizSchedule = BeanUtil.toBean(bizScheduleAddParam, BizSchedule.class);
        this.save(bizSchedule);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizScheduleEditParam bizScheduleEditParam) {
        BizSchedule bizSchedule = this.queryEntity(bizScheduleEditParam.getId());
        
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizSchedule.getOrganizationId())) {
                throw new CommonException("您没有权限编辑该机构下的排班，机构id：{}", bizSchedule.getOrganizationId());
            }
        } else {
            // 如果没有数据范围权限，只能编辑自己的排班
            if(!bizSchedule.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该员工的排班，员工id：{}", bizSchedule.getStaffId());
            }
        }
        
        BeanUtil.copyProperties(bizScheduleEditParam, bizSchedule);
        this.updateById(bizSchedule);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizScheduleIdParam> bizScheduleIdParamList) {
        List<String> scheduleIdList = CollStreamUtil.toList(bizScheduleIdParamList, BizScheduleIdParam::getId);
        if(ObjectUtil.isNotEmpty(scheduleIdList)) {
            // 校验数据范围 - 基于organizationId进行权限控制
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            List<BizSchedule> scheduleList = this.listByIds(scheduleIdList);
            
            for(BizSchedule schedule : scheduleList) {
                if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                    if(!loginUserDataScope.contains(schedule.getOrganizationId())) {
                        throw new CommonException("您没有权限删除该机构下的排班，机构id：{}", schedule.getOrganizationId());
                    }
                } else {
                    // 如果没有数据范围权限，只能删除自己的排班
                    if(!schedule.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该员工的排班，员工id：{}", schedule.getStaffId());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(scheduleIdList);
    }

    @Override
    public BizSchedule detail(BizScheduleIdParam bizScheduleIdParam) {
        BizSchedule bizSchedule = this.queryEntity(bizScheduleIdParam.getId());
        
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizSchedule.getOrganizationId())) {
                throw new CommonException("您没有权限查看该机构下的排班，机构id：{}", bizSchedule.getOrganizationId());
            }
        } else {
            // 如果没有数据范围权限，只能查看自己的排班
            if(!bizSchedule.getStaffId().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该员工的排班，员工id：{}", bizSchedule.getStaffId());
            }
        }
        
        return bizSchedule;
    }

    @Override
    public BizSchedule queryEntity(String id) {
        BizSchedule bizSchedule = this.getById(id);
        if(ObjectUtil.isEmpty(bizSchedule)) {
            throw new CommonException("排班不存在，id值为：{}", id);
        }
        return bizSchedule;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizScheduleServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizSchedule.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}
