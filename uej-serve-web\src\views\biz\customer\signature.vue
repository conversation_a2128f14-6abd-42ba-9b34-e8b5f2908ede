<template>
	<div class="container">
		<vue-esign
			class="xn-bdr236"
			ref="esignRef"
			v-model:bgColor="bgColor"
			:width="800"
			:height="500"
			:quality="1"
			:is-crop="isCrop"
			:line-width="lineWidth"
			:line-color="lineColor"
		/>
		<a-form layout="horizontal" class="mt-[10px]">
			<a-form-item :label="$t('common.画笔粗细')">
				<a-input-number v-model:value="lineWidth" :min="1" :max="20" />
			</a-form-item>
			<a-form-item>
				<div class="xn-pr50">{{ $t('common.是否裁剪') }}：<a-checkbox v-model:checked="isCrop"></a-checkbox></div>
			</a-form-item>
		</a-form>
		<a-space>
			<a-button type="primary" :loading="loading" @click="handleOk">{{ $t('common.确定') }}</a-button>
			<a-button @click="handleReset">{{ $t('common.清屏') }}</a-button>
		</a-space>
	</div>
</template>

<script setup>
	import VueEsign from '@/components/XnSignName/vueEsign.vue'
	import { getCurrentInstance } from 'vue'
	import { message } from 'ant-design-vue'
	import { useRoute } from 'vue-router'
	import bizCustomerApi from '@/api/biz/bizCustomerApi'
	
	const route = useRoute()
	const { proxy } = getCurrentInstance()
	const esignRef = ref(false)
	const bgColor = ref('#fff')
	const isCrop = ref(false)
	const lineWidth = ref(10)
	const lineColor = ref('#000000')
	const id = ref('')
	const loading = ref(false)

	const handleReset = () => {
		esignRef.value.reset()
	}
	
	const handleOk = () => {
		loading.value = true
		esignRef.value
			.generate()
			.then((res) => {
				// 调用专门的签字保存接口
				bizCustomerApi.bizCustomerSaveSignature({
					id: id.value,
					signature: res
				})
				.then(() => {
					message.success('签字保存成功')
					setTimeout(() => {
						window.close() // 关闭当前窗口
					}, 1000)
				})
				.catch((error) => {
					console.error('保存签字失败:', error)
					message.error('保存签字失败，请重试')
				})
				.finally(() => {
					loading.value = false
				})
			})
			.catch(() => {
				loading.value = false
				message.warning(proxy.$t('common.无任何签字'))
			})
	}

	onMounted(() => {
		id.value = route.query.id
		if (!id.value) {
			message.error('缺少客户ID参数')
		}
	})
</script>

<style lang="less" scoped>
	.container {
		padding: 10px;
	}
	.xn-bdr236 {
		border: 1px solid rgb(236 236 236);
	}
</style> 