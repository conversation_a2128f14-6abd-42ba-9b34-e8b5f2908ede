
package vip.xiaonuo.biz.modular.config.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务配置添加参数
 *
 * <AUTHOR>
 * @date  2024/07/08 19:09
 **/
@Getter
@Setter
public class BizConfigAddParam {

    /** Parent Key */
    @Schema(description = "Parent Key")
    private String parentBizKey;

    /** Key */
    @Schema(description = "Key")
    private String bizKey;

    /** Value */
    @Schema(description = "Value")
    private String bizValue;

}
