
package vip.xiaonuo.biz.modular.dailybalance.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目结款记录表添加参数
 *
 * <AUTHOR>
 * @date  2024/12/02 13:50
 **/
@Getter
@Setter
public class BizDailyBalanceAddParam {

    /** item */
    @Schema(description = "item")
    private String item;

    /** receivable amount */
    @Schema(description = "receivable amount")
    private BigDecimal receivableAmount;

    /** received amount */
    @Schema(description = "received amount")
    private BigDecimal receivedAmount;

    /** balance */
    @Schema(description = "balance")
    private BigDecimal balance;

    /** remark */
    @Schema(description = "remark")
    private String remark;

    /** shop id */
    @Schema(description = "shop id")
    private String orgId;

    /** shop name */
    @Schema(description = "shop name")
    private String orgName;

}
