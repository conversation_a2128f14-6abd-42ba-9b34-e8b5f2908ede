
package vip.xiaonuo.biz.modular.scheduledate.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.scheduledate.entity.BizScheduleDate;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateAddParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateEditParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateIdParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDatePageParam;
import vip.xiaonuo.biz.modular.scheduledate.service.BizScheduleDateService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 排班表当日属性控制器
 *
 * <AUTHOR>
 * @date  2024/06/18 16:34
 */
@Tag(name = "排班表当日属性控制器")
@RestController
@Validated
public class BizScheduleDateController {

    @Resource
    private BizScheduleDateService bizScheduleDateService;

    /**
     * 获取排班表当日属性分页
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    @Operation(summary = "获取排班表当日属性分页")
    @SaCheckPermission("/biz/scheduledate/page")
    @GetMapping("/biz/scheduledate/page")
    public CommonResult<Page<BizScheduleDate>> page(BizScheduleDatePageParam bizScheduleDatePageParam) {
        return CommonResult.data(bizScheduleDateService.page(bizScheduleDatePageParam));
    }

    /**
     * 添加排班表当日属性
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    @Operation(summary = "添加排班表当日属性")
    @CommonLog("添加排班表当日属性")
    @SaCheckPermission("/biz/scheduledate/add")
    @PostMapping("/biz/scheduledate/add")
    public CommonResult<String> add(@RequestBody @Valid BizScheduleDateAddParam bizScheduleDateAddParam) {
        bizScheduleDateService.add(bizScheduleDateAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑排班表当日属性
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    @Operation(summary = "编辑排班表当日属性")
    @CommonLog("编辑排班表当日属性")
    @SaCheckPermission("/biz/scheduledate/edit")
    @PostMapping("/biz/scheduledate/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizScheduleDateEditParam bizScheduleDateEditParam) {
        bizScheduleDateService.edit(bizScheduleDateEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除排班表当日属性
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    @Operation(summary = "删除排班表当日属性")
    @CommonLog("删除排班表当日属性")
    @SaCheckPermission("/biz/scheduledate/delete")
    @PostMapping("/biz/scheduledate/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizScheduleDateIdParam> bizScheduleDateIdParamList) {
        bizScheduleDateService.delete(bizScheduleDateIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取排班表当日属性详情
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    @Operation(summary = "获取排班表当日属性详情")
    @SaCheckPermission("/biz/scheduledate/detail")
    @GetMapping("/biz/scheduledate/detail")
    public CommonResult<BizScheduleDate> detail(@Valid BizScheduleDateIdParam bizScheduleDateIdParam) {
        return CommonResult.data(bizScheduleDateService.detail(bizScheduleDateIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    @Operation(summary = "获取排班表当日属性动态字段的配置")
    @SaCheckPermission("/biz/scheduledate/dynamicFieldConfigList")
    @GetMapping("/biz/scheduledate/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizScheduleDateService.dynamicFieldConfigList(columnName));
    }

    @Operation(summary = "获取一周工作日属性详情")
    @GetMapping("/biz/scheduledate/getOneWeek")
    public CommonResult<List<BizScheduleDate>> getOneWeek(@Valid String start, @Valid String end, @Valid String organizationId) {
        return CommonResult.data(bizScheduleDateService.list(new QueryWrapper<BizScheduleDate>().lambda().between(BizScheduleDate::getScheduleDate, start, end)
                                                                                                        .eq(BizScheduleDate::getOrganizationId, organizationId)));
    }
}
