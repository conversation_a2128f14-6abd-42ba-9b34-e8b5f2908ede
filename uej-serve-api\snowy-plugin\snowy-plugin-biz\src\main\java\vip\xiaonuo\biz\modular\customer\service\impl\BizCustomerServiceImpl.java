package vip.xiaonuo.biz.modular.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.customer.entity.BizCustomer;
import vip.xiaonuo.biz.modular.customer.mapper.BizCustomerMapper;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerAddParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerEditParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerIdParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerPageParam;
import vip.xiaonuo.biz.modular.customer.service.BizCustomerService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;

import java.util.List;

/**
 * 外部会员信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 17:22
 **/
@Service
public class BizCustomerServiceImpl extends ServiceImpl<BizCustomerMapper, BizCustomer> implements BizCustomerService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizCustomer> page(BizCustomerPageParam bizCustomerPageParam) {
        QueryWrapper<BizCustomer> queryWrapper = new QueryWrapper<BizCustomer>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizCustomerPageParam.getFirstName())) {
            queryWrapper.lambda().like(BizCustomer::getFirstName, bizCustomerPageParam.getFirstName());
        }
        if(ObjectUtil.isNotEmpty(bizCustomerPageParam.getLastName())) {
            queryWrapper.lambda().like(BizCustomer::getLastName, bizCustomerPageParam.getLastName());
        }
        if(ObjectUtil.isNotEmpty(bizCustomerPageParam.getGender())) {
            queryWrapper.lambda().eq(BizCustomer::getGender, bizCustomerPageParam.getGender());
        }
        if(ObjectUtil.isNotEmpty(bizCustomerPageParam.getLevel())) {
            queryWrapper.lambda().eq(BizCustomer::getLevel, bizCustomerPageParam.getLevel());
        }
        if(ObjectUtil.isNotEmpty(bizCustomerPageParam.getPhone())) {
            queryWrapper.lambda().like(BizCustomer::getPhone, bizCustomerPageParam.getPhone());
        }
        if(ObjectUtil.isAllNotEmpty(bizCustomerPageParam.getSortField(), bizCustomerPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizCustomerPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizCustomerPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizCustomerPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizCustomer::getId);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizCustomer::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizCustomer::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizCustomer::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizCustomerAddParam bizCustomerAddParam) {
        BizCustomer bizCustomer = BeanUtil.toBean(bizCustomerAddParam, BizCustomer.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizCustomer.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizCustomer.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizCustomer);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizCustomerEditParam bizCustomerEditParam) {
        BizCustomer bizCustomer = this.queryEntity(bizCustomerEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查客户所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizCustomer.getOrgId()) && !loginUserDataScope.contains(bizCustomer.getOrgId())) {
                throw new CommonException("您没有权限编辑该客户信息，客户id：{}", bizCustomer.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizCustomer.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该客户信息，客户id：{}", bizCustomer.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该客户信息，客户id：{}", bizCustomer.getId());
            }
        }
        
        BeanUtil.copyProperties(bizCustomerEditParam, bizCustomer);
        this.updateById(bizCustomer);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizCustomerIdParam> bizCustomerIdParamList) {
        List<String> customerIdList = CollStreamUtil.toList(bizCustomerIdParamList, BizCustomerIdParam::getId);
        if(ObjectUtil.isNotEmpty(customerIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizCustomer> customerList = this.listByIds(customerIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查客户所属机构是否在权限范围内
                for(BizCustomer customer : customerList) {
                    if(ObjectUtil.isNotEmpty(customer.getOrgId()) && !loginUserDataScope.contains(customer.getOrgId())) {
                        throw new CommonException("您没有权限删除该客户信息，客户id：{}", customer.getId());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizCustomer customer : customerList) {
                        if(!customer.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该客户信息，客户id：{}", customer.getId());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除客户信息");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(customerIdList);
    }

    @Override
    public BizCustomer detail(BizCustomerIdParam bizCustomerIdParam) {
        BizCustomer bizCustomer = this.queryEntity(bizCustomerIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查客户所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizCustomer.getOrgId()) && !loginUserDataScope.contains(bizCustomer.getOrgId())) {
                throw new CommonException("您没有权限查看该客户信息，客户id：{}", bizCustomer.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizCustomer.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该客户信息，客户id：{}", bizCustomer.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该客户信息，客户id：{}", bizCustomer.getId());
            }
        }
        
        return bizCustomer;
    }

    @Override
    public BizCustomer queryEntity(String id) {
        BizCustomer bizCustomer = this.getById(id);
        if(ObjectUtil.isEmpty(bizCustomer)) {
            throw new CommonException("外部会员信息不存在，id值为：{}", id);
        }
        return bizCustomer;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizCustomerServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizCustomer.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}


