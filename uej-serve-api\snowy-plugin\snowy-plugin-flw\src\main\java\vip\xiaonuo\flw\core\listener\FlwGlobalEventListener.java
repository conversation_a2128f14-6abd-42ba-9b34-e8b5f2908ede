
package vip.xiaonuo.flw.core.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.bpm.model.bpmn.impl.BpmnModelConstants;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import vip.xiaonuo.flw.core.enums.NodeBusinessTableFlwStatusEnum;
import vip.xiaonuo.flw.core.enums.NodeExecutionListenerKeyEnum;
import vip.xiaonuo.flw.core.enums.NodeTaskListenerKeyEnum;
import vip.xiaonuo.flw.core.util.NodeRuntimeUtil;

import java.util.List;

/**
 * 全局事件监听器
 *
 * <AUTHOR>
 * @date 2022/5/26 10:01
 **/
@Configuration
public class FlwGlobalEventListener implements JavaDelegate {

    /**
     * 执行监听器
     *
     * <AUTHOR>
     * @date 2022/6/15 10:05
     **/
    @EventListener
    public void onExecutionEvent(DelegateExecution delegateExecution) {
        if(isProcessStart(delegateExecution)) {
            NodeRuntimeUtil.handleTableDataVariables(delegateExecution);
            NodeRuntimeUtil.handleTitleAndAbstract(delegateExecution);
            NodeRuntimeUtil.handleUserTaskParticipateInfo(delegateExecution);
            JSONObject flwFieldJsonObject = JSONUtil.createObj().set("FLW_STATUS", NodeBusinessTableFlwStatusEnum.IN_APPROVAL.getValue())
                    .set("FLW_INSTANCE_ID", delegateExecution.getProcessInstanceId());
            NodeRuntimeUtil.updateFlwFields(delegateExecution, flwFieldJsonObject);
        }
    }

    /**
     * 任务监听器
     *
     * <AUTHOR>
     * @date 2022/6/15 10:05
     **/
    @EventListener
    public void onTaskEvent(DelegateTask delegateTask) {
        if(isTaskCreate(delegateTask)) {
            boolean newProcessStart = ObjectUtil.isEmpty(delegateTask.getProcessEngineServices().getHistoryService()
                    .createHistoricProcessInstanceQuery().processInstanceId(delegateTask.getProcessInstanceId()).singleResult());
            if(isTaskStart(delegateTask)) {
                if(newProcessStart) {
                    // 自动完成第一个发起申请节点
                    NodeRuntimeUtil.handleUserTaskCommentAndAttachment(delegateTask.getId(),
                            delegateTask.getProcessInstanceId(), "START", "发起申请", null,
                            CollectionUtil.newArrayList());
                    delegateTask.getProcessEngineServices().getTaskService().complete(delegateTask.getId());
                }
            } else {
                NodeRuntimeUtil.handleUserTaskTypeAndDistinctType(delegateTask);
            }
        }
        if(isTaskAssignment(delegateTask)) {
            if(!isTaskStart(delegateTask)) {
                // 非申请节点则发送待办通知
                NodeRuntimeUtil.handleUserTaskTodoNotice(delegateTask);
            }
        }
    }

    /**
     * 判断是否流程启动
     *
     * <AUTHOR>
     * @date 2022/6/9 19:52
     */
    public static boolean isProcessStart(DelegateExecution delegateExecution) {
        String eventName = delegateExecution.getEventName();
        if(eventName.equals(NodeExecutionListenerKeyEnum.START.getValue().toLowerCase())) {
            String activityInstanceId = delegateExecution.getActivityInstanceId();
            return ObjectUtil.isEmpty(activityInstanceId);
        }
        return false;
    }

    /**
     * 判断任务是否为发起申请
     *
     * <AUTHOR>
     * @date 2022/6/9 19:52
     */
    public static boolean isTaskStart(DelegateTask delegateTask) {
        List<FlowNode> flowNodeList = delegateTask.getBpmnModelElementInstance().getPreviousNodes().list();
        if(ObjectUtil.isNotEmpty(flowNodeList) && flowNodeList.size() == 1) {
            return flowNodeList.get(0).getElementType().getTypeName().equals(BpmnModelConstants.BPMN_ELEMENT_START_EVENT);
        }
        return false;
    }

    /**
     * 判断是否任务创建
     *
     * <AUTHOR>
     * @date 2022/6/9 19:52
     */
    public static boolean isTaskCreate(DelegateTask delegateTask) {
        String eventName = delegateTask.getEventName();
        return eventName.equals(NodeTaskListenerKeyEnum.CREATE.getValue().toLowerCase());
    }

    /**
     * 判断是否任务分配
     *
     * <AUTHOR>
     * @date 2022/6/9 19:52
     */
    public static boolean isTaskAssignment(DelegateTask delegateTask) {
        String eventName = delegateTask.getEventName();
        return eventName.equals(NodeTaskListenerKeyEnum.ASSIGNMENT.getValue().toLowerCase());
    }

    /**
     * 结束监听
     *
     * <AUTHOR>
     * @date 2023/7/17 23:42
     */
    @Override
    public void execute(DelegateExecution delegateExecution) {
        String eventName = delegateExecution.getEventName();
        if(eventName.equals(NodeExecutionListenerKeyEnum.END.getValue().toLowerCase())) {
            // 发送完成通知
            NodeRuntimeUtil.handleProcessCompleteNotice(delegateExecution);
            JSONObject flwFieldJsonObject = JSONUtil.createObj().set("FLW_STATUS", NodeBusinessTableFlwStatusEnum.IN_APPROVAL.getValue());
            NodeRuntimeUtil.updateFlwFields(delegateExecution, flwFieldJsonObject);
        }
    }
}
