<template>
	<xn-form-container title="Import/Export" :width="700" :visible="visible" :destroy-on-close="true" @close="onClose">
		<span
			>Import data format strictly according to the system template for data entry, please click
			<a-button type="primary" size="small" @click="downloadImportUserTemplate">Download Template</a-button>
		</span>
		<a-divider dashed />
		<div>
			<a-spin :spinning="impUploadLoading">
				<a-upload-dragger :show-upload-list="false" :custom-request="customRequestLocal" :accept="uploadAccept">
					<p class="ant-upload-drag-icon">
						<inbox-outlined></inbox-outlined>
					</p>
					<p class="ant-upload-text">Click or drag the file to this area for uploading</p>
					<p class="ant-upload-hint">Only supports xls and xlsx format files</p>
				</a-upload-dragger>
			</a-spin>
		</div>
		<a-alert v-if="impAlertStatus" type="info" :show-icon="false" banner closable @close="onImpClose" class="mt-3">
			<template #description>
				<p>Total number of imports：{{ impResultData.totalCount }} strip</p>
				<p>Import successful：{{ impResultData.successCount }} strip</p>
				<div v-if="impResultData.errorCount > 0">
					<p><span class="xn-color-red">Number of failures：</span>{{ impResultData.errorCount }} strip</p>
					<a-table :dataSource="impResultErrorDataSource" :columns="impErrorColumns" size="small" />
				</div>
			</template>
		</a-alert>
	</xn-form-container>
</template>

<script setup name="userImpExp">
	import { message } from 'ant-design-vue'
	import userApi from '@/api/sys/userApi'
	import downloadUtil from '@/utils/downloadUtil'

	const impUploadLoading = ref(false)
	const impAlertStatus = ref(false)
	const impResultData = ref({})
	const impResultErrorDataSource = ref([])
	const impAccept = [
		{
			extension: '.xls',
			mimeType: 'application/vnd.ms-excel'
		},
		{
			extension: '.xlsx',
			mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		}
	]
	// 指定能选择的文件类型
	const uploadAccept = String(
		impAccept.map((item) => {
			return item.mimeType
		})
	)
	// 导入
	const customRequestLocal = (data) => {
		impUploadLoading.value = true
		const fileData = new FormData()
		// 校验上传文件扩展名和文件类型是否为.xls、.xlsx
		const extension = '.'.concat(data.file.name.split('.').slice(-1).toString().toLowerCase())
		const mimeType = data.file.type
		// 提取允许的扩展名
		const extensionArr = impAccept.map((item) => item.extension)
		// 提取允许的MIMEType
		const mimeTypeArr = impAccept.map((item) => item.mimeType)
		if (!extensionArr.includes(extension) || !mimeTypeArr.includes(mimeType)) {
			message.warning('Upload file types only support xls and xlsx format files！')
			impUploadLoading.value = false
			return false
		}
		fileData.append('file', data.file)
		return userApi
			.userImport(fileData)
			.then((res) => {
				// impAlertStatus.value = true
				// impResultData.value = res
				// impResultErrorDataSource.value = res.errorDetail
				message.info('success')
			})
			.finally(() => {
				impUploadLoading.value = false
			})
	}
	// 关闭导入提示
	const onImpClose = () => {
		impAlertStatus.value = false
	}
	const impErrorColumns = [
		{
			title: '索引',
			dataIndex: 'index',
			width: '80px'
		},
		{
			title: '原因',
			dataIndex: 'msg'
		}
	]
	// 定义emit事件
	const emit = defineEmits({ successful: null })
	// 默认是关闭状态
	const visible = ref(false)
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = () => {
		visible.value = true
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		// 关闭导入的提示
		onImpClose()
	}
	// 下载用户导入模板
	const downloadImportUserTemplate = () => {
		const a = document.createElement('a')
		a.href = 'https://oa-wanqi20-1255648699.cos.ap-shanghai.myqcloud.com/coo/202410/Userimporttemplate1.xlsx'
		a.click()
		a.remove()
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
<style scoped>
	.xn-color-red {
		color: #ff0000;
	}
</style>
