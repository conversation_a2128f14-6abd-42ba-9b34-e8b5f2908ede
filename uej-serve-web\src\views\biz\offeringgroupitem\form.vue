<template>
	<xn-form-container
		:title="formData.id ? 'edit offeringgroupitem' : 'add offeringgroupitem'"
		:width="700"
		v-model:open="open"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<!-- <a-col :span="12">
                    <a-form-item label="OFFERING_ID：" name="offeringId">
                        <a-input v-model:value="formData.offeringId" placeholder="Please enter OFFERING_ID" allow-clear />
                    </a-form-item>
                </a-col> -->
				<a-col :span="12">
					<a-form-item label="OFFERING_NAME：" name="offeringName">
						<a-select
							v-model:value="formData.offeringName"
							:options="offeringOptions"
							style="width: 100%"
							@change="offeringChange"
							placeholder="Please select OFFERING_NAME"
						>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="LIST PRICE：" name="listPrice">
						<a-input-number
							v-model:value="formData.listPrice"
							:min="0"
							placeholder="Please enter LIST PRICE"
							allow-clear
							style="width: 100%"
							@change="numChange"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="PLANNING HOURS：" name="planningHours">
						<a-input-number
							v-model:value="formData.planningHours"
							:min="0"
							placeholder="Please enter PLANNING HOURS"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="NUM：" name="num">
						<a-input-number
							v-model:value="formData.num"
							:min="0"

							placeholder="Please enter NUM"
							allow-clear
							style="width: 100%"
							@change="numChange"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="TOTAL PRICE：" name="totalPrice">
						<a-input-number
							v-model:value="formData.totalPrice"
							:min="0"
							placeholder="Please enter TOTAL PRICE"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
					<xn-form-item :fieldConfig="item" :formData="dynamicFormData" />
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="bizOfferingGroupItemForm">
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import bizOfferingGroupItemApi from '@/api/biz/bizOfferingGroupItemApi'
	import bizOfferingApi from '@/api/biz/bizOfferingApi'
	// 抽屉状态
	const open = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({	})
	const submitLoading = ref(false)
	// 动态表单
	const dynamicFormRef = ref()
	const dynamicFieldConfigList = ref([])
	const dynamicFormData = ref({})

	const offeringOptions = ref([])

	// 打开抽屉
	const onOpen = (record) => {
		open.value = true
		bizOfferingGroupItemApi.bizOfferingGroupItemDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
			dynamicFieldConfigList.value = data
		})
		bizOfferingApi.bizOfferingList().then((res) => {
			offeringOptions.value = res.map((data) => {
				return {
					label: data.name,
					value: data.id,
					listPrice: data.listPrice,
					planningHours: data.planningHours
				}
			})
		})
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			dynamicFormData.value = JSON.parse(formData.value.extJson) || {}
		}
	}


	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		dynamicFormData.value = {}
		open.value = false
	}
	// 默认要校验的
	const formRules = {}
	// 验证并提交数据
	const onSubmit = () => {
		const promiseList = []
		promiseList.push(
			new Promise((resolve, reject) => {
				formRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		promiseList.push(
			new Promise((resolve, reject) => {
				dynamicFormRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		submitLoading.value = true
		Promise.all(promiseList)
			.then(() => {
				const formDataParam = cloneDeep(formData.value)
				formDataParam.extJson = JSON.stringify(dynamicFormData.value)
				bizOfferingGroupItemApi
					.bizOfferingGroupItemSubmitForm(formDataParam, formDataParam.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			})
			.catch(() => {})
	}

	const numChange=()=>{
		formData.value.totalPrice=formData.value.num*formData.value.listPrice
	}
	const offeringChange = (value, option) => {
		formData.value.offeringName = option.label
		formData.value.offeringId = value
		formData.value.listPrice = option.listPrice
		formData.value.planningHours = option.planningHours
	}

	const setMainId = (e) => {
		formData.value.mainId = e.mainId
	}
	// 抛出函数
	defineExpose({
		onOpen,
		setMainId
	})
</script>
