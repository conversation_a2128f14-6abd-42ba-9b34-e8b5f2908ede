<template>
	<div class="menu" :style="{ left: props.x + 'px', top: props.y + 'px' }" v-if="props.visible">
		<a-menu :theme="store.theme" style="width: 200px" mode="vertical">
			<template v-for="item in props.data" :key="item.key">
				<a-menu-item @click="handleClick(item.key)" v-if="item.title=='task_delete' && hasPerm('bizTaskDelete')">
					{{ $t(item.label) }}
				</a-menu-item>
				<a-menu-item @click="handleClick(item.key)" v-else>
					{{ $t(item.label) }}
				</a-menu-item>
			</template>
		</a-menu>
	</div>
</template>

<script setup>
	import { globalStore } from '@/store'
	const store = globalStore()
	const props = defineProps({
		x: {
			type: Number,
			default: 0
		},
		y: {
			type: Number,
			default: 0
		},
		visible: {
			type: Boolean,
			default: false
		},
		data: {
			type: Array,
			default: () => []
		}
	})

	const emits = defineEmits(['menu-item'])

	const handleClick = (action) => {
		emits('menu-item', action)
	}
</script>

<style lang="less" scoped>
	.menu {
		position: fixed;
		transition: all 0.6s ease;
		z-index: 9999;
	}
</style>
