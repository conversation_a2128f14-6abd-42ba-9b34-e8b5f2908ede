package vip.xiaonuo.biz.modular.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.biz.modular.customer.entity.BizCustomer;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerAddParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerEditParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerIdParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerPageParam;
import vip.xiaonuo.biz.modular.customer.service.BizCustomerService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 外部会员信息控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 17:22
 */
@Tag(name = "外部会员信息控制器")
@RestController
@Validated
public class BizCustomerController {

    @Resource
    private BizCustomerService bizCustomerService;

    /**
     * 获取外部会员信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    @Operation(summary = "获取外部会员信息分页")
    @SaCheckPermission("/biz/customer/page")
    @GetMapping("/biz/customer/page")
    public CommonResult<Page<BizCustomer>> page(BizCustomerPageParam bizCustomerPageParam) {
        return CommonResult.data(bizCustomerService.page(bizCustomerPageParam));
    }

    /**
     * 添加外部会员信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    @Operation(summary = "添加外部会员信息")
    @CommonLog("添加外部会员信息")
    @SaCheckPermission("/biz/customer/add")
    @PostMapping("/biz/customer/add")
    public CommonResult<String> add(@RequestBody @Valid BizCustomerAddParam bizCustomerAddParam) {
        bizCustomerService.add(bizCustomerAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑外部会员信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    @Operation(summary = "编辑外部会员信息")
    @CommonLog("编辑外部会员信息")
    @SaCheckPermission("/biz/customer/edit")
    @PostMapping("/biz/customer/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizCustomerEditParam bizCustomerEditParam) {
        bizCustomerService.edit(bizCustomerEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除外部会员信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    @Operation(summary = "删除外部会员信息")
    @CommonLog("删除外部会员信息")
    @SaCheckPermission("/biz/customer/delete")
    @PostMapping("/biz/customer/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizCustomerIdParam> bizCustomerIdParamList) {
        bizCustomerService.delete(bizCustomerIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取外部会员信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    @Operation(summary = "获取外部会员信息详情")
    @SaCheckPermission("/biz/customer/detail")
    @GetMapping("/biz/customer/detail")
    public CommonResult<BizCustomer> detail(@Valid BizCustomerIdParam bizCustomerIdParam) {
        return CommonResult.data(bizCustomerService.detail(bizCustomerIdParam));
    }

    @Operation(summary = "获取外部会员集合")
    @PostMapping("/biz/customer/getCustomersByIds")
    public CommonResult<List<BizCustomer>> getCustomersByIds(@RequestBody BizCustomerIdParam ids) {
        List<BizCustomer> result = bizCustomerService.lambdaQuery().in(BizCustomer::getId, ids.getIdList()).list();
        return CommonResult.data(result);
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    @Operation(summary = "获取外部会员信息动态字段的配置")
    @SaCheckPermission("/biz/customer/dynamicFieldConfigList")
    @GetMapping("/biz/customer/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizCustomerService.dynamicFieldConfigList(columnName));
    }

    /**
     * 保存客户签字
     *
     * <AUTHOR>
     * @date  2024/12/24
     */
    @Operation(summary = "保存客户签字")
    @CommonLog("保存客户签字")
    @PostMapping("/biz/customer/saveSignature")
    public CommonResult<String> saveSignature(@RequestBody @Valid BizCustomerEditParam bizCustomerEditParam) {
        // 只更新签字字段，其他字段保持不变
        BizCustomer customer = bizCustomerService.getById(bizCustomerEditParam.getId());
        if (customer == null) {
            throw new CommonException("客户不存在");
        }
        customer.setSignature(bizCustomerEditParam.getSignature());
        bizCustomerService.updateById(customer);
        return CommonResult.ok();
    }
}
