package vip.xiaonuo.dev.modular.dm.service;

import vip.xiaonuo.dev.modular.dm.param.*;
import vip.xiaonuo.dev.modular.dm.result.DevDmColumnsResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmConnectResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmInfoListResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmTablesResult;

import java.sql.SQLException;
import java.util.List;

/**
 * 获取数据库连接
 * <AUTHOR>
 */
public interface DevDmService {

    /**
     * 获取数据库连接
     *
     * @param dbsId 数据源
     * @return JSONObject
     */
    DevDmConnectResult dbConnect(String dbsId);

    /**
     * 获取所有数据源信息
     *
     * <AUTHOR>
     * @date 2023/2/1 10:43
     **/
    List<DevDmInfoListResult> dbInfoList();

    /**
     * 获取所有表信息
     *
     * <AUTHOR>
     * @date 2022/10/25 22:33
     **/
    List<DevDmTablesResult> tables(DevDmTablesParam devDmTablesParam);

    /**
     * 查询指定数据源中的所有表
     *
     * <AUTHOR>
     * @date 2023/2/1 10:31
     **/
    List<DevDmTablesResult> queryTables(String url, String userName, String password);

    /**
     * 添加数据库表
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    void addTable(DevDmAddTableParam devDmAddTableParam);

    /**
     * 添加数据库表
     * @param url
     * @param userName
     * @param password
     * @param tableName
     * @param tableRemark
     */
    void addTableData(String url, String userName, String password, String tableName, String tableRemark);

    /**
     * 编辑数据库表
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    void editTable(DevDmEditTableParam devDmEditTableParam);

    /**
     * 编辑数据库表
     * @param url
     * @param userName
     * @param password
     * @param tableName
     * @param newTableName
     * @param tableRemark
     */
    void editTableData(String url, String userName, String password, String tableName, String newTableName, String tableRemark);


    /**
     * 删除数据库表
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    void deleteTable(DevDmDeleteTableParam devDmDeleteTableParam);

    /**
     * 删除数据库表
     * @param url
     * @param username
     * @param password
     * @param tableName
     */
    void deleteTableData(String url, String username, String password, String tableName);

    /**
     * 获取表内所有字段信息
     *
     * <AUTHOR>
     * @date 2022/10/25 22:33
     **/
    List<DevDmColumnsResult> columns(DevDmColumnsParam devDmColumnsParam);

    /**
     * 查询指定数据源中指定表的所有字段
     *
     * <AUTHOR>
     * @date 2023/2/1 11:09
     **/
    List<DevDmColumnsResult> queryTableColumns(String url, String userName, String password, String tableName);

    /**
     * 添加数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    void addColumn(DevDmAddColumnParam devDmAddColumnParam);

    /**
     * 添加数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     **/
    void addColumnData(String url, String userName, String password, DevDmAddColumnParam devDmAddColumnParam);

    /**
     * 编辑数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    void editColumn(DevDmEditColumnParam devDmEditColumnParam);

    /**
     * 编辑数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     **/
    void editColumnData(String url, String username, String password, DevDmEditColumnParam devDmEditColumnParam);

    /**
     * 删除数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    void deleteColumn(DevDmDeleteColumnParam devDmDeleteColumnParam);

    /**
     * 删除数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    void deleteColumnData(String url, String username, String password, String tableName, String columnName);
}
