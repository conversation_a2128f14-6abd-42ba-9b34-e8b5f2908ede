
package vip.xiaonuo.biz.modular.schedule.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.auth.core.pojo.SaBaseLoginUser;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.biz.modular.config.service.BizConfigService;
import vip.xiaonuo.biz.modular.devConfig.config.entity.BizDevConfig;
import vip.xiaonuo.biz.modular.devConfig.config.service.BizDevConfigService;
import vip.xiaonuo.biz.modular.org.entity.BizOrg;
import vip.xiaonuo.biz.modular.org.service.BizOrgService;
import vip.xiaonuo.biz.modular.position.entity.BizPosition;
import vip.xiaonuo.biz.modular.position.service.BizPositionService;
import vip.xiaonuo.biz.modular.scheduledate.entity.BizScheduleDate;
import vip.xiaonuo.biz.modular.scheduledate.service.BizScheduleDateService;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.service.BizTaskService;
import vip.xiaonuo.biz.modular.taskdatastatistics.entity.BizTaskDataStatistics;
import vip.xiaonuo.biz.modular.taskdatastatistics.service.BizTaskDataStatisticsService;
import vip.xiaonuo.biz.modular.user.entity.BizUser;
import vip.xiaonuo.biz.modular.user.service.BizUserService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleAddParam;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleEditParam;
import vip.xiaonuo.biz.modular.schedule.param.BizScheduleIdParam;
import vip.xiaonuo.biz.modular.schedule.param.BizSchedulePageParam;
import vip.xiaonuo.biz.modular.schedule.service.BizScheduleService;
import cn.hutool.json.JSONObject;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 排班控制器
 *
 * <AUTHOR>
 * @date  2024/06/18 16:16
 */
@Tag(name = "排班控制器")
@RestController
@Validated
public class BizScheduleController {

    @Resource
    private BizScheduleService bizScheduleService;

    @Resource
    private BizScheduleDateService bizScheduleDateService;

    @Resource
    private BizOrgService bizOrgService;

    @Resource
    private BizTaskDataStatisticsService bizTaskDataStatisticsService;

    @Resource
    private BizConfigService bizConfigService;

    @Resource
    private BizPositionService bizPositionService;

    @Resource
    private BizTaskService bizTaskService;

    @Resource
    private BizDevConfigService devConfigService;

    @Resource
    private BizUserService bizUserService;

    /**
     * 获取排班分页
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    @Operation(summary = "获取排班分页")
    @SaCheckPermission("/biz/schedule/page")
    @GetMapping("/biz/schedule/page")
    public CommonResult<Page<BizSchedule>> page(BizSchedulePageParam bizSchedulePageParam) {
        return CommonResult.data(bizScheduleService.page(bizSchedulePageParam));
    }

    /**
     * 添加排班
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    @Operation(summary = "添加排班")
    @CommonLog("添加排班")
    @SaCheckPermission("/biz/schedule/add")
    @PostMapping("/biz/schedule/add")
    public CommonResult<String> add(@RequestBody @Valid BizScheduleAddParam bizScheduleAddParam) {
        bizScheduleService.add(bizScheduleAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑排班
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    @Operation(summary = "编辑排班")
    @CommonLog("编辑排班")
    @SaCheckPermission("/biz/schedule/edit")
    @PostMapping("/biz/schedule/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizScheduleEditParam bizScheduleEditParam) {
        bizScheduleService.edit(bizScheduleEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除排班
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    @Operation(summary = "删除排班")
    @CommonLog("删除排班")
    @SaCheckPermission("/biz/schedule/delete")
    @PostMapping("/biz/schedule/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizScheduleIdParam> bizScheduleIdParamList) {
        List<BizSchedule> list = bizScheduleService.lambdaQuery()
                .in(BizSchedule::getId, bizScheduleIdParamList.stream().map(BizScheduleIdParam::getId).toList()).list();
        List<String> assignDates = list.stream().map(o -> o.getAssignDate()).toList();
        // 如果有未完成的任务，放到未指派任务中
        for (String assignDate : assignDates) {
            List<BizTask> taskNoFinish = bizTaskService.lambdaQuery()
                    .like(BizTask::getStartTime, assignDate)
                    .ne(BizTask::getState, "TASK_STATE_FINISH")
                    .ne(BizTask::getStaff, "Unregistered")
                    .list();
            if (!taskNoFinish.isEmpty()) {
                // 更新任务表
                for (BizTask noFinish : taskNoFinish) {
                    noFinish.setAssign("Unregistered");
                    noFinish.setStaff("Unregistered");
                    noFinish.setOrgId("Unregistered");
                }
                bizTaskService.updateBatchById(taskNoFinish);

                // 更新任务数据表
                List<BizTaskDataStatistics> tasks = bizTaskDataStatisticsService.lambdaQuery()
                        .in(BizTaskDataStatistics::getMainId, taskNoFinish.stream().map(BizTask::getId).toList()).list();
                for (BizTaskDataStatistics task : tasks) {
                    task.setStaffId("Unregistered");
                    task.setStaff("Unregistered");
                    task.setOrgId("Unregistered");
                }
                bizTaskDataStatisticsService.updateBatchById(tasks);
            } else {
                bizScheduleService.delete(bizScheduleIdParamList);
            }
        }
        return CommonResult.ok();
    }

    /**
     * 获取排班详情
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    @Operation(summary = "获取排班详情")
    @SaCheckPermission("/biz/schedule/detail")
    @GetMapping("/biz/schedule/detail")
    public CommonResult<BizSchedule> detail(@Valid BizScheduleIdParam bizScheduleIdParam) {
        return CommonResult.data(bizScheduleService.detail(bizScheduleIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/18 16:16
     */
    @Operation(summary = "获取排班动态字段的配置")
    @SaCheckPermission("/biz/schedule/dynamicFieldConfigList")
    @GetMapping("/biz/schedule/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizScheduleService.dynamicFieldConfigList(columnName));
    }

    @Operation(summary = "获取一周排班详情")
    @GetMapping("/biz/schedule/getOneWeek")
    public CommonResult<Map<String, List<BizSchedule>>> getOneWeek(@Valid String start, @Valid String end, @Valid String organizationId) {
        SaBaseLoginUser loginUser = StpLoginUserUtil.getLoginUser();
        Map<String, List<BizSchedule>> collect = bizScheduleService.list(new QueryWrapper<BizSchedule>().lambda()
                                                                    .between(BizSchedule::getAssignDate, start, end)
                                                                    .isNotNull(BizSchedule::getStaffName)
                                                                    .eq(BizSchedule::getOrganizationId, organizationId)).stream().collect(Collectors.groupingBy(BizSchedule::getStaffName));
        return CommonResult.data(collect);
    }

    @Operation(summary = "获取机构, 营业时间, 员工姓名")
    @GetMapping("/biz/schedule/getAllOrganization")
    public CommonResult<Map<String, List<BizSchedule>>> getAllOrganization(String start, String end) {
        SaBaseLoginUser loginUser = StpLoginUserUtil.getLoginUser();
        
        // 获取所有机构列表
        List<BizOrg> allOrgList = bizOrgService.getAllOrgList();
        
        // 获取当前用户所属组织及其所有子组织
        List<BizOrg> userOrgAndChildren = bizOrgService.getChildListById(allOrgList, loginUser.getOrgId(), true);
        
        // 过滤出非根节点的组织（排除parentId为"0"的根节点）
        List<BizOrg> orgs = userOrgAndChildren.stream()
                .filter(org -> !"0".equals(org.getParentId()))
                .collect(Collectors.toList());
        Map<String, List<BizSchedule>> result = new HashMap<>();
        LocalDate firstDay = LocalDate.parse(start, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        orgs.forEach(org -> {
            List<BizSchedule> schedules = new ArrayList<>();
            for (int i = 0; i < 7; i++) {
                BizSchedule schedule = new BizSchedule();
                String day = firstDay.plus(i, ChronoUnit.DAYS).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                List<String> staffs = bizScheduleService.lambdaQuery().eq(BizSchedule::getAssignDate, day).eq(BizSchedule::getOrganizationId, org.getId()).isNotNull(BizSchedule::getWorkTime).list().stream().map(BizSchedule::getStaffName).distinct().toList();
                BizScheduleDate scheduleDate = bizScheduleDateService.getOne(new QueryWrapper<BizScheduleDate>().lambda().eq(BizScheduleDate::getScheduleDate, day).eq(BizScheduleDate::getOrganizationId, org.getId()));
                schedule.setOrganizationId(org.getId());
                schedule.setOpenTime(scheduleDate == null ? null : scheduleDate.getOpenTime());
                schedule.setStaffs(staffs);
                schedule.setStaffNum(staffs.size());
                schedules.add(schedule);
            }
            result.put(org.getName(), schedules);
        });
        return CommonResult.data(result);
    }

    @Operation(summary = "新增一周的人员安排")
    @PostMapping("/biz/schedule/addOneWeek")
    public CommonResult<String> addOneWeek(@RequestBody BizScheduleAddParam bizScheduleAddParam) {
        List<BizSchedule> schedules = new ArrayList<>();
        LocalDate firstDay = LocalDate.parse(bizScheduleAddParam.getAssignDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        for (int i = 0; i < 7; i++) {
            BizSchedule schedule = new BizSchedule();
            schedule.setStaffId(bizScheduleAddParam.getStaffId());
            schedule.setStaffName(bizScheduleAddParam.getStaffName());
            schedule.setOrganizationId(bizScheduleAddParam.getOrganizationId());
            schedule.setOrganizationName(bizScheduleAddParam.getOrganizationName());
            schedule.setAssignDate(firstDay.plus(i, ChronoUnit.DAYS).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            schedules.add(schedule);
        }
        bizScheduleService.saveBatch(schedules);
        return CommonResult.ok();
    }

    @Operation(summary = "开启或关闭自动排班")
    @GetMapping("/biz/schedule/copyLastWeek")
    public CommonResult<String> copyLastWeek(String orgId, String copy) {
        BizDevConfig copyConfig = devConfigService.lambdaQuery().eq(BizDevConfig::getConfigKey, "copySchedule").one();
        String autoCopyOrg = ObjectUtil.isEmpty(copyConfig.getConfigValue()) ? "" : copyConfig.getConfigValue();
        // 开启自动排班
        if (copy.equals("1")) {
            if (autoCopyOrg.contains(orgId)) {
                return CommonResult.ok();
            } else {
                // 首次添加自动排班
                if (autoCopyOrg.length() == 0) {
                    copyConfig.setConfigValue("[" + orgId + "]");
                // 非首次添加自动排班
                } else {
                    List<String> list = JSONUtil.toList(autoCopyOrg, String.class);
                    list.add(orgId);
                    copyConfig.setConfigValue(list.toString());
                }
                devConfigService.updateById(copyConfig);
            }
        // 关闭自动排班
        } else {
            if (!autoCopyOrg.contains(orgId)) {
                return CommonResult.ok();
            } else {
                List<String> list = JSONUtil.toList(autoCopyOrg, String.class);
                list.remove(orgId);
                copyConfig.setConfigValue(list.toString());
                devConfigService.updateById(copyConfig);
            }
        }
        return CommonResult.ok();
    }

    @Operation(summary = "复制上一周的排班计划")
    @GetMapping("/biz/schedule/copyLastWeekAuto")
    public CommonResult<String> copyLastWeekAuto() {
        if (!timeToCopy()) {
            return null;
        }
        BizDevConfig copyConfig = devConfigService.lambdaQuery().eq(BizDevConfig::getConfigKey, "copySchedule").one();
        if (ObjectUtil.isEmpty(copyConfig)) {
            return CommonResult.ok("No staff has been scheduled automatically");
        }
        List<String> orgIds = JSONUtil.toList(copyConfig.getConfigValue(), String.class);
        if (orgIds.isEmpty()) {
            return CommonResult.ok("No staff has been scheduled automatically");
        }

        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        List<BizSchedule> lastWeekSchedules = bizScheduleService.lambdaQuery()
                .in(BizSchedule::getOrganizationId, orgIds)
                .between(BizSchedule::getAssignDate, LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd")).minusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), date)
                .list();
        // 查询到的上周排班，对每天的排班日期加7
        lastWeekSchedules.stream().map(
                obj -> {obj.setId(null);
                        obj.setAssignDate(LocalDate.parse(obj.getAssignDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        return obj;})
                .collect(Collectors.toList());
        bizScheduleService.saveBatch(lastWeekSchedules);
        return CommonResult.ok();
    }

    @Operation(summary = "获取本日排班人员状态")
    @GetMapping("/biz/schedule/getScheduledStaff")
    public CommonResult<List<BizSchedule>> getScheduledStaff(String organizationId, String time) {
        List<BizPosition> positionAll = bizPositionService.list();

        Map<String, BizSchedule> collect = bizScheduleService.lambdaQuery().eq(BizSchedule::getOrganizationId, organizationId).eq(BizSchedule::getAssignDate, time).orderByDesc(BizSchedule::getAssignDate).isNotNull(BizSchedule::getWorkTime)
                .list().stream().collect(Collectors.toMap(BizSchedule::getStaffId, o -> o, (o1, o2) -> o1));
        List<BizSchedule> todaySchedule = new ArrayList<>(collect.values());

        if (todaySchedule.isEmpty()) {
            return CommonResult.ok("No staff members have been scheduled to work today");
        }
        List<BizTaskDataStatistics> todayStaff = bizTaskDataStatisticsService.lambdaQuery()
                .eq(BizTaskDataStatistics::getOrgId, organizationId)
                .like(BizTaskDataStatistics::getStartTime, DateUtil.format(new Date(), "yyyy-MM-dd")).list();
        BigDecimal totalMoney = BigDecimal.ZERO;
        BigDecimal totalTip = BigDecimal.ZERO;
        for (BizSchedule bizSchedule : todaySchedule) {
            // 计算员工今日的销售金额
            BigDecimal money = BigDecimal.ZERO;
            BigDecimal tip = BigDecimal.ZERO;
            List<BizTask> tasks = bizTaskService.lambdaQuery()
                    .eq(BizTask::getAssign, bizSchedule.getStaffId())
                    .like(BizTask::getStartTime, DateUtil.format(new Date(), "yyyy-MM-dd")).list();
            for (BizTask task : tasks) {
                if (task.getState().equals("TASK_STATE_FINISH")) {
                    money = money.add(Convert.toBigDecimal(task.getTotalPrice()));
                }
                tip = tip.add(ObjectUtil.isEmpty(task.getTip()) ? BigDecimal.ZERO : Convert.toBigDecimal(task.getTip()));
            }
            List<BizTask> preparedTasks = tasks.stream()
                    .filter(o -> o.getState().equals("TASK_STATE_NEW"))
                    .sorted(Comparator.comparing(BizTask::getStartTime)).toList();
            if (!preparedTasks.isEmpty()) {
                BizTask preparedTasksEarliest = preparedTasks.get(0);
                Map<String, Object> result = calculateTime(DateUtil.now(), preparedTasksEarliest.getStartTime());
                bizSchedule.setNext(result.get("time").toString());
            } else {
                bizSchedule.setNext("-");
            }
            bizSchedule.setMoney(money);
            bizSchedule.setTip(tip);
            totalMoney = totalMoney.add(money);
            totalTip = totalTip.add(tip);
            String positionId = bizUserService.getById(bizSchedule.getStaffId()).getPositionId();
            if (ObjectUtil.isNotEmpty(positionId)) {
                List<BizPosition> position = positionAll.stream().filter(obj -> obj.getId().equals(positionId)).toList();
                if (ObjectUtil.isNotEmpty(position)) {
                    bizSchedule.setColor(position.get(0).getColor());
                }
            }
            List<BizTaskDataStatistics> staffSchedule = todayStaff.stream()
                    .filter(o -> o.getStaffId().equals(bizSchedule.getStaffId()) && !o.getStatus().equals("TASK_STATE_NEW"))
                    .sorted(Comparator.comparing(BizTaskDataStatistics::getStartTime)).toList();
            if (staffSchedule.isEmpty()) {
                String openTime = DateUtil.format(new Date(), "yyyy-MM-dd ") + JSONUtil.toList(bizSchedule.getWorkTime(), String.class).get(0);
                Map<String, Object> result = calculateTime(openTime, DateUtil.now());
                bizSchedule.setFree(result.get("time").toString());
                bizSchedule.setWork("-");
                bizSchedule.setTaskStatus("free");
                bizSchedule.setSort(Convert.toLong(result.get("sort")));
            }
            else {
                BizTaskDataStatistics taskLast = staffSchedule.get(staffSchedule.size() - 1);
                if (staffSchedule.stream().allMatch(o -> o.getStatus().equals("TASK_STATE_FINISH"))) {
                    Map<String, Object> result = calculateTime(taskLast.getPlanningEndTime(), DateUtil.now());
                    bizSchedule.setFree(result.get("time").toString());
                    bizSchedule.setWork("-");
                    bizSchedule.setTaskStatus("free");
                    bizSchedule.setSort(Convert.toLong(result.get("sort")));
                } else {
//                    List<BizTaskDataStatistics> goingTask = staffSchedule.stream().filter(obj -> !obj.getStatus().equals("TASK_STATE_FINISH") && ObjectUtil.isEmpty(obj.getParentId())).toList();
//                    if (goingTask.isEmpty()) {
//                        Map<String, Object> result = calculateTime(DateUtil.now(), taskLast.getPlanningEndTime());
//                        bizSchedule.setFree(result.get("time").toString());
//                        bizSchedule.setWork("-");
//                        bizSchedule.setTaskStatus("work");
//                        bizSchedule.setSort(99);
//                    } else {
                        Map<String, Object> result = calculateTime(DateUtil.now(), taskLast.getPlanningEndTime());
                        bizSchedule.setFree("-");
                        bizSchedule.setWork(result.get("time").toString());
                        bizSchedule.setTaskStatus("work");
                        bizSchedule.setSort(99);
//                    }
                }
            }
        }
        todaySchedule.get(0).setTotalTip(totalTip);
        todaySchedule.get(0).setTotalMoney(totalMoney);
        todaySchedule = todaySchedule.stream().sorted(Comparator.comparing(BizSchedule::getSort)).toList();
        return CommonResult.data(todaySchedule);
    }


    /**
     * 判断当前时间是否是周五23~24
     */
    private static boolean timeToCopy() {
        Calendar now = Calendar.getInstance();
        // 是否为周五
        boolean isFriday = now.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY;
        if (!isFriday) {
            return false;
        }
        // 是否在23:00至23:59之间
        boolean isBetween2200And2359 = now.get(Calendar.HOUR_OF_DAY) >= 23 && now.get(Calendar.HOUR_OF_DAY) < 24
                && now.get(Calendar.MINUTE) >= 0 && now.get(Calendar.MINUTE) <= 59;
        return isBetween2200And2359;
    }

    public static Map<String, Object> calculateTime(String startTimeStr, String endTimeStr) {
        Map<String, Object> result = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, formatter);
        Duration duration = Duration.between(startTime, endTime);
        //String time = Convert.toStr(duration.toMinutes()).replace("-", "") + " min";
        long minutes = duration.toMinutes();
        String time = minutes / 60 + "h : " + minutes % 60 + "min";
        result.put("time", time);
        result.put("sort", duration.toMinutes());
        return result;
    }

    @Operation(summary = "获取自动排班信息")
    @GetMapping("/biz/schedule/getCopyStatus")
    public CommonResult<String> getCopyStatus(String orgId) {
        BizDevConfig copyConfig = devConfigService.lambdaQuery().eq(BizDevConfig::getConfigKey, "copySchedule").one();
        if (ObjectUtil.isEmpty(copyConfig) || ObjectUtil.isEmpty(copyConfig.getConfigValue())) {
            return CommonResult.data("0");
        }
        List<String> orgIds = JSONUtil.toList(copyConfig.getConfigValue(), String.class);
        return CommonResult.data(orgIds.contains(orgId) ? "1" : "0");
    }
}
