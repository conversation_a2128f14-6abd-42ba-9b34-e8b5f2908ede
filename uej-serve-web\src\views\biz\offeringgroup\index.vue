<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
            <a-row :gutter="24">
                <a-col :xs="24" :sm="12" :md="8" :xl="6">
                    <a-form-item label="Type" name="category">
                        <a-input v-model:value="searchFormState.category" placeholder="Please enter Type" />
                    </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :xl="6">
                    <a-form-item label="Name" name="name">
                        <a-input v-model:value="searchFormState.name" placeholder="Please enter Name" @pressEnter="tableRef.refresh(true)"/>
                    </a-form-item>
                </a-col>
                <a-col :xs="24" :sm="12" :md="8" :xl="6">
                    <a-button type="primary" @click="tableRef.refresh(true)">{{ $t('common.searchButton') }}</a-button>
                    <a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="tableRef"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.id"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection"
        >
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('bizOfferingGroupAdd')">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.addButton') }}
                    </a-button>
                    <xn-batch-delete
                        v-if="hasPerm('bizOfferingGroupBatchDelete')"
                        :selectedRowKeys="selectedRowKeys"
                        @batchDelete="deleteBatchBizOfferingGroup"
						:buttonName="$t('common.batchRemoveButton')"
                    />
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
						<a @click="offeringFormRef.onOpen(record)" v-if="hasPerm('bizOfferingEdit')">{{ $t('common.more') }}</a>
						<a-divider type="vertical" v-if="hasPerm(['bizOfferingGroupEdit'])" />
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('bizOfferingGroupEdit')">{{ $t('common.editButton') }}</a>
                        <a-divider type="vertical" v-if="hasPerm(['bizOfferingGroupEdit', 'bizOfferingGroupDelete'], 'and')" />
                        <a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizOfferingGroup(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('bizOfferingGroupDelete')">{{ $t('common.removeButton') }}</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="tableRef.refresh(true)" />
	<offeringForm ref="offeringFormRef"></offeringForm>
</template>

<script setup name="offeringgroup">
    import { cloneDeep } from 'lodash-es'
    import Form from './form.vue'
	import offeringForm from './offeringForm.vue'
    import bizOfferingGroupApi from '@/api/biz/bizOfferingGroupApi'
    const searchFormState = ref({})
    const searchFormRef = ref()
    const tableRef = ref()
    const formRef = ref()
	const offeringFormRef=ref()
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    const columns = [
        {
            title: 'Type',
            dataIndex: 'category'
        },
        {
            title: 'Name',
            dataIndex: 'name'
        },
        {
            title: 'Planning Hours',
            dataIndex: 'planningHours'
        },
        {
            title: 'List Price',
            dataIndex: 'listPrice'
        },
        {
            title: 'Cost Price',
            dataIndex: 'costPrice'
        }
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['bizOfferingGroupEdit', 'bizOfferingGroupDelete'])) {
        columns.push({
            title: 'action',
            dataIndex: 'action',
            align: 'center',
            width: '190px'
        })
    }
    const selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        // columns数字类型字段加入 needTotal: true 可以勾选自动算账
        alert: {
            show: true,
            clear: () => {
                selectedRowKeys.value = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
        const searchFormParam = cloneDeep(searchFormState.value)
        return bizOfferingGroupApi.bizOfferingGroupPage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    // 重置
    const reset = () => {
        searchFormRef.value.resetFields()
        tableRef.value.refresh(true)
    }
    // 删除
    const deleteBizOfferingGroup = (record) => {
        let params = [
            {
                id: record.id
            }
        ]
        bizOfferingGroupApi.bizOfferingGroupDelete(params).then(() => {
            tableRef.value.refresh(true)
        })
    }
    // 批量删除
    const deleteBatchBizOfferingGroup = (params) => {
        bizOfferingGroupApi.bizOfferingGroupDelete(params).then(() => {
            tableRef.value.clearRefreshSelected()
        })
    }
</script>
