
package vip.xiaonuo.dev.modular.dfc.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 代码生成模块所需要用到的数据源选择器的结果
 *
 * <AUTHOR>
 * @date 2022/7/19 18:55
 **/
@Getter
@Setter
@Accessors(chain = true)
public class DevDfcDbsSelectorResult {

    /** id */
    @Schema(description = "id")
    private String id;

    /** 名称 */
    @Schema(description = "名称")
    private String poolName;
}
