
package vip.xiaonuo.biz.modular.customer.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外部会员信息查询参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:22
 **/
@Getter
@Setter
public class BizCustomerPageParam {

    /** 当前页 */
    @Schema(description = "当前页码")
    private Integer current;

    /** 每页条数 */
    @Schema(description = "每页条数")
    private Integer size;

    /** 排序字段 */
    @Schema(description = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @Schema(description = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @Schema(description = "关键词")
    private String searchKey;

    /** First Name */
    @Schema(description = "First Name")
    private String firstName;

    /** Last Name */
    @Schema(description = "Last Name")
    private String lastName;

    /** Gender */
    @Schema(description = "Gender")
    private String gender;

    /** Level */
    @Schema(description = "Level")
    private String level;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "会员号")
    private String memberId;

    @Schema(description = "保险公司")
    private String InsuranceCompany;

    @Schema(description = "Phone")
    private String phone;
}
