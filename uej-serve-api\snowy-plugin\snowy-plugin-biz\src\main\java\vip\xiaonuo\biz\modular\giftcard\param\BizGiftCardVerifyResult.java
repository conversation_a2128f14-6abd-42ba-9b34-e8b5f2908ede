package vip.xiaonuo.biz.modular.giftcard.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 礼品卡验证结果
 *
 * <AUTHOR>
 * @date  2024/12/19
 **/
@Getter
@Setter
public class BizGiftCardVerifyResult {

    /** 验证是否成功 */
    @Schema(description = "验证是否成功")
    private Boolean success;

    /** 消息 */
    @Schema(description = "消息")
    private String message;

    /** 礼品卡总价值 */
    @Schema(description = "礼品卡总价值")
    private BigDecimal balance;

    /** 礼品卡可用余额 */
    @Schema(description = "礼品卡可用余额")
    private BigDecimal availableBalance;

    /** 礼品卡ID */
    @Schema(description = "礼品卡ID")
    private String giftCardId;

    /** 是否已激活 */
    @Schema(description = "是否已激活")
    private Boolean activated;

    /** 是否已过期 */
    @Schema(description = "是否已过期")
    private Boolean expired;
} 