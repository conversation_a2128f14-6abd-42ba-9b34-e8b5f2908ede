package vip.xiaonuo.biz.modular.productcategory.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.productcategory.entity.BizProductCategory;
import vip.xiaonuo.biz.modular.productcategory.param.*;

import java.util.List;

/**
 * 产品类目Service接口
 *
 * <AUTHOR>
 * @date 2024/12/19
 **/
public interface BizProductCategoryService extends IService<BizProductCategory> {

    /**
     * 获取产品类目分页
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    Page<BizProductCategory> page(BizProductCategoryPageParam bizProductCategoryPageParam);

    /**
     * 获取产品类目树
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    List<Tree<String>> tree();

    /**
     * 添加产品类目
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    void add(BizProductCategoryAddParam bizProductCategoryAddParam);

    /**
     * 编辑产品类目
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    void edit(BizProductCategoryEditParam bizProductCategoryEditParam);

    /**
     * 删除产品类目
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    void delete(List<BizProductCategoryIdParam> bizProductCategoryIdParamList);

    /**
     * 获取产品类目详情
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    BizProductCategory detail(BizProductCategoryIdParam bizProductCategoryIdParam);

    /**
     * 获取产品类目详情
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    BizProductCategory queryEntity(String id);
} 