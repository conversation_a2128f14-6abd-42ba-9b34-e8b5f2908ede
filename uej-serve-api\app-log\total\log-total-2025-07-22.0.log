2025-07-22T16:00:44.951+08:00  INFO 2840 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 2840 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-07-22T16:00:44.956+08:00  INFO 2840 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-07-22T16:00:48.637+08:00  INFO 2840 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-22T16:00:48.642+08:00  INFO 2840 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-22T16:00:48.892+08:00  INFO 2840 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 229 ms. Found 0 Redis repository interfaces.
2025-07-22T16:00:49.436+08:00  WARN 2840 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-07-22T16:00:49.852+08:00  WARN 2840 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-07-22T16:00:50.657+08:00  WARN 2840 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-22T16:00:51.143+08:00  INFO 2840 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-07-22T16:00:51.160+08:00  INFO 2840 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T16:00:51.161+08:00  INFO 2840 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-22T16:00:51.254+08:00  INFO 2840 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T16:00:51.254+08:00  INFO 2840 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6232 ms
2025-07-22T16:00:51.894+08:00  INFO 2840 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-07-22T16:00:52.534+08:00  INFO 2840 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-07-22T16:00:52.536+08:00  INFO 2840 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-07-22T16:00:52.537+08:00  INFO 2840 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-07-22T16:00:52.537+08:00  INFO 2840 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-22T16:00:52.883+08:00  INFO 2840 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-07-22T16:01:00.002+08:00  INFO 2840 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-07-22T16:01:02.189+08:00  INFO 2840 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-07-22T16:01:05.922+08:00  INFO 2840 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@6bf2ecbb, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-07-22T16:01:05.975+08:00  INFO 2840 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-07-22T16:01:05.975+08:00  INFO 2840 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-07-22T16:01:05.975+08:00  INFO 2840 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-07-22T16:01:05.976+08:00  INFO 2840 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-07-22T16:01:05.998+08:00  INFO 2840 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-07-22T16:01:06.481+08:00  INFO 2840 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@74d4f542)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@35fddea2, clock: SystemClock, configuration: Configuration(false)]
2025-07-22T16:01:09.426+08:00  INFO 2840 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-07-22T16:01:09.432+08:00  INFO 2840 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-07-22T16:01:09.798+08:00  INFO 2840 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-07-22T16:01:14.290+08:00  INFO 2840 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-07-22T16:01:14.422+08:00  INFO 2840 --- [main] vip.xiaonuo.Application                  : Started Application in 31.601 seconds (process running for 33.425)
2025-07-22T16:01:14.542+08:00  INFO 2840 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-07-22T16:02:05.724+08:00  INFO 2840 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22T16:02:05.725+08:00  INFO 2840 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-22T16:02:05.727+08:00  INFO 2840 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-22T16:02:05.964+08:00  WARN 2840 --- [http-nio-10082-exec-5] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [105] milliseconds.
2025-07-22T16:02:39.924+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:02:39.937+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:05:18.200+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.200+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.202+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.204+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.205+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.206+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.207+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.207+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.213+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.215+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:05:18.221+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:06:47.199+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.200+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.201+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.202+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.203+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.204+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.204+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.205+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.213+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.214+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:06:47.223+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:07:16.296+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.296+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.297+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.299+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.300+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.302+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.302+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.303+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.310+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.311+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:16.317+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:07:19.016+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.017+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.018+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.019+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.020+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.020+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.021+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.021+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.032+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.033+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:19.036+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:07:48.203+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.204+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.206+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.208+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.209+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.209+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.209+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.210+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.210+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:48.227+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:07:59.107+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.108+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.109+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.110+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.111+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.111+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.111+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.112+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:07:59.132+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:08:44.575+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.575+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.576+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.577+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.577+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.577+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.578+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.578+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.586+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.586+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:44.591+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:08:54.521+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.521+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.522+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.523+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.524+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.524+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.525+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.526+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.529+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:08:54.546+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:08:54.547+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:22.614+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.614+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.615+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.616+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.616+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.617+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.617+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.618+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.618+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:22.628+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:15:22.635+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:24.300+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.301+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.301+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.303+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.304+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.304+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.304+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.304+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:24.320+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:25.113+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.114+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.115+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.115+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.117+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.117+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.117+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.117+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:25.132+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:26.190+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.190+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.191+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.192+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.192+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.192+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.193+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.193+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.208+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:26.883+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.883+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.884+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.885+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.886+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.887+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.887+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.887+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:26.902+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:27.406+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.407+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.408+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.408+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.409+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.409+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.409+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.409+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:27.422+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:28.188+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.188+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.189+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.189+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.189+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.189+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.190+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.190+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.204+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:28.936+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.936+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.937+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.938+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.939+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.939+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.939+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.939+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:28.956+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:30.126+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.127+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.127+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.128+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.128+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.128+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.129+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.129+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:30.144+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:31.920+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.921+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.922+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.922+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.923+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.923+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.923+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.924+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:31.938+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:32.742+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.744+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.744+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.745+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.745+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.745+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.745+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.745+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:32.760+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:33.411+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.412+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.412+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.413+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.415+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.415+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.415+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.416+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:33.430+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:34.131+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.132+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.133+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.134+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.134+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.144+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.145+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.145+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:34.159+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:35.428+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.429+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.429+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.430+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.431+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.431+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.431+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.431+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.436+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:35.445+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:35.446+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:15:40.397+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.398+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.401+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.402+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.402+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.403+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.403+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.403+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.406+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.406+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:40.418+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:15:40.420+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:42.134+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.135+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.135+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.136+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.136+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.136+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.137+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.137+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.148+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:42.149+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:42.149+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.152+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.152+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.152+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.154+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.154+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.154+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.155+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.155+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.166+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.167+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:44.170+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:44.179+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:15:45.972+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.973+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.973+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.974+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.974+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.974+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.975+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.975+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.989+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.990+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:45.995+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:47.284+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.284+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.285+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.286+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.288+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.288+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.289+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.289+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.294+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.294+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:47.302+08:00  WARN 2840 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:47.305+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:15:48.122+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.123+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.124+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.125+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.125+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.125+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.126+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.126+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.137+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.138+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:48.142+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:55.255+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.255+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.255+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.256+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.256+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.257+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.257+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.257+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.275+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:55.277+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:55.278+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.486+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.487+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.487+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.488+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.488+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.488+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.488+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.488+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.496+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.496+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:15:57.505+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:15:57.509+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:16:01.074+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.075+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.075+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.076+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.077+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.077+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.077+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.077+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.088+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.089+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:01.093+08:00  WARN 2840 --- [http-nio-10082-exec-2] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:06.292+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.293+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.293+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.294+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.294+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.295+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.295+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.295+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.301+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:06.307+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:14.081+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.082+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.082+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.083+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.083+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.083+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.083+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.084+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:14.099+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:15.804+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.804+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.805+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.805+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.805+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.806+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.806+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.806+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.812+08:00  WARN 2840 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:15.819+08:00  WARN 2840 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:17.560+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.560+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.561+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.562+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.562+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.563+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.563+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.563+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:17.577+08:00  WARN 2840 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:18.389+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.390+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.390+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.391+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.392+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.392+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.392+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.392+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:18.406+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:19.760+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.761+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.761+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.762+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.762+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.764+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.764+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.764+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:19.777+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:21.445+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.446+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.446+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.447+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.447+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.447+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.447+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.448+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.452+08:00  WARN 2840 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:21.461+08:00  WARN 2840 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:24.072+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.073+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.073+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.074+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.074+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.075+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.075+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.075+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.082+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.083+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:16:24.089+08:00  WARN 2840 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.biz.modular.position.entity.BizPosition_1543899639134019591
2025-07-22T16:16:24.093+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:23:07.327+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:23:07.337+08:00  WARN 2840 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:23:15.546+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:23:15.553+08:00  WARN 2840 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:26:31.830+08:00  INFO 2840 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-07-22T16:26:31.863+08:00  INFO 2840 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-07-22T16:26:31.873+08:00  INFO 2840 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-07-22T16:26:31.888+08:00  INFO 2840 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-07-22T16:26:31.889+08:00  INFO 2840 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-07-22T16:26:31.889+08:00  INFO 2840 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-07-22T16:26:31.895+08:00  INFO 2840 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-07-22T16:26:31.895+08:00  INFO 2840 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-07-22T16:26:31.895+08:00  INFO 2840 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-07-22T16:33:35.383+08:00  INFO 28304 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 28304 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-07-22T16:33:35.387+08:00  INFO 28304 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-07-22T16:33:39.913+08:00  INFO 28304 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-22T16:33:39.933+08:00  INFO 28304 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-22T16:33:40.458+08:00  INFO 28304 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 468 ms. Found 0 Redis repository interfaces.
2025-07-22T16:33:41.353+08:00  WARN 28304 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-07-22T16:33:42.169+08:00  WARN 28304 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-07-22T16:33:43.362+08:00  WARN 28304 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-07-22T16:33:44.285+08:00  INFO 28304 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-07-22T16:33:44.342+08:00  INFO 28304 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-22T16:33:44.343+08:00  INFO 28304 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-07-22T16:33:44.634+08:00  INFO 28304 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-22T16:33:44.635+08:00  INFO 28304 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 9150 ms
2025-07-22T16:33:45.558+08:00  INFO 28304 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-07-22T16:33:46.606+08:00  INFO 28304 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-07-22T16:33:46.612+08:00  INFO 28304 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-07-22T16:33:46.616+08:00  INFO 28304 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-07-22T16:33:46.617+08:00  INFO 28304 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-22T16:33:48.357+08:00  INFO 28304 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-07-22T16:34:00.080+08:00  INFO 28304 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-07-22T16:34:01.189+08:00  INFO 28304 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-07-22T16:34:05.059+08:00  INFO 28304 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@********, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-07-22T16:34:05.105+08:00  INFO 28304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-07-22T16:34:05.105+08:00  INFO 28304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-07-22T16:34:05.106+08:00  INFO 28304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-07-22T16:34:05.106+08:00  INFO 28304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-07-22T16:34:05.123+08:00  INFO 28304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-07-22T16:34:05.778+08:00  INFO 28304 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@4b024fb2)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@4cdb7fd9, clock: SystemClock, configuration: Configuration(false)]
2025-07-22T16:34:09.336+08:00  INFO 28304 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-07-22T16:34:09.343+08:00  INFO 28304 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-07-22T16:34:09.588+08:00  INFO 28304 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-07-22T16:34:13.820+08:00  INFO 28304 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-07-22T16:34:13.948+08:00  INFO 28304 --- [main] vip.xiaonuo.Application                  : Started Application in 40.844 seconds (process running for 42.939)
2025-07-22T16:34:14.029+08:00  INFO 28304 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-07-22T16:34:34.383+08:00  INFO 28304 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22T16:34:34.383+08:00  INFO 28304 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-22T16:34:34.385+08:00  INFO 28304 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-22T16:34:34.650+08:00  WARN 28304 --- [http-nio-10082-exec-2] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [112] milliseconds.
2025-07-22T16:36:10.598+08:00  WARN 28304 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:36:10.607+08:00  WARN 28304 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T16:45:06.224+08:00 ERROR 28304 --- [http-nio-10082-exec-3] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/schedule/getCopyStatus，具体信息：

org.springframework.web.servlet.resource.NoResourceFoundException: No static resource biz/schedule/getCopyStatus.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-07-22T16:45:06.242+08:00  WARN 28304 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:46:08.644+08:00 ERROR 28304 --- [http-nio-10082-exec-2] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/schedule/getCopyStatus，具体信息：

org.springframework.web.servlet.resource.NoResourceFoundException: No static resource biz/schedule/getCopyStatus.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-07-22T16:46:08.668+08:00  WARN 28304 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T16:47:11.780+08:00 ERROR 28304 --- [http-nio-10082-exec-3] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/schedule/getCopyStatus，具体信息：

org.springframework.web.servlet.resource.NoResourceFoundException: No static resource biz/schedule/getCopyStatus.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-07-22T16:47:11.809+08:00  WARN 28304 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T17:42:45.096+08:00  INFO 28304 --- [http-nio-10082-exec-10] o.apache.coyote.http11.Http11Processor   : Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.

java.lang.IllegalArgumentException: Invalid character found in the request target [/biz/schedule/getAllOrganization?start=2025-07-20&end=2025-07-26&orgIds[]=1543842934270394368&orgIds[]=1846013013578170370&orgIds[]=1853311584481075202&orgIds[]=1852153035578613762&orgIds[]=1852153036459417602&orgIds[]=1852153035893186562&orgIds[]=1864493542733000705&orgIds[]=1864477928568139778&orgIds[]=1853311895371276290&orgIds[]=1846013014303784961&orgIds[]=1939489912900894721&orgIds[]=1939489853643767809&orgIds[]=1853255622852370434&date=Sat,+19+Jul+2025+16:00:00+GMT&_=1753177365081 ]. The valid characters are defined in RFC 7230 and RFC 3986
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:482)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:264)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-07-22T17:49:18.815+08:00  WARN 28304 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T17:49:18.826+08:00  WARN 28304 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T17:49:21.490+08:00  WARN 28304 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-07-22T17:49:21.498+08:00  WARN 28304 --- [http-nio-10082-exec-8] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-07-22T17:56:09.311+08:00  INFO 28304 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-07-22T17:56:09.346+08:00  INFO 28304 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-07-22T17:56:09.357+08:00  INFO 28304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-07-22T17:56:09.375+08:00  INFO 28304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-07-22T17:56:09.375+08:00  INFO 28304 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-07-22T17:56:09.375+08:00  INFO 28304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-07-22T17:56:09.379+08:00  INFO 28304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-07-22T17:56:09.379+08:00  INFO 28304 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-07-22T17:56:09.379+08:00  INFO 28304 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
