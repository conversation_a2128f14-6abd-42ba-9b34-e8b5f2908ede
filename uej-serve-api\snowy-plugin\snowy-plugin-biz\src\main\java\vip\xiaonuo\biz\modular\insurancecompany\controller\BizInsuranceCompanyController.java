
package vip.xiaonuo.biz.modular.insurancecompany.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.insurancecompany.entity.BizInsuranceCompany;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyAddParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyEditParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyIdParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyPageParam;
import vip.xiaonuo.biz.modular.insurancecompany.service.BizInsuranceCompanyService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 保险公司控制器
 *
 * <AUTHOR>
 * @date  2024/08/09 09:42
 */
@Tag(name = "保险公司控制器")
@RestController
@Validated
public class BizInsuranceCompanyController {

    @Resource
    private BizInsuranceCompanyService bizInsuranceCompanyService;

    /**
     * 获取保险公司分页
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    @Operation(summary = "获取保险公司分页")
    @SaCheckPermission("/biz/insurancecompany/page")
    @GetMapping("/biz/insurancecompany/page")
    public CommonResult<Page<BizInsuranceCompany>> page(BizInsuranceCompanyPageParam bizInsuranceCompanyPageParam) {
        return CommonResult.data(bizInsuranceCompanyService.page(bizInsuranceCompanyPageParam));
    }

    @Operation(summary = "获取保险公司分页")
    @GetMapping("/biz/insurancecompany/list")
    public CommonResult<List<BizInsuranceCompany>> list() {
        return CommonResult.data(bizInsuranceCompanyService.list());
    }

    /**
     * 添加保险公司
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    @Operation(summary = "添加保险公司")
    @CommonLog("添加保险公司")
    @SaCheckPermission("/biz/insurancecompany/add")
    @PostMapping("/biz/insurancecompany/add")
    public CommonResult<String> add(@RequestBody @Valid BizInsuranceCompanyAddParam bizInsuranceCompanyAddParam) {
        bizInsuranceCompanyService.add(bizInsuranceCompanyAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑保险公司
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    @Operation(summary = "编辑保险公司")
    @CommonLog("编辑保险公司")
    @SaCheckPermission("/biz/insurancecompany/edit")
    @PostMapping("/biz/insurancecompany/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizInsuranceCompanyEditParam bizInsuranceCompanyEditParam) {
        bizInsuranceCompanyService.edit(bizInsuranceCompanyEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除保险公司
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    @Operation(summary = "删除保险公司")
    @CommonLog("删除保险公司")
    @SaCheckPermission("/biz/insurancecompany/delete")
    @PostMapping("/biz/insurancecompany/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizInsuranceCompanyIdParam> bizInsuranceCompanyIdParamList) {
        bizInsuranceCompanyService.delete(bizInsuranceCompanyIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取保险公司详情
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    @Operation(summary = "获取保险公司详情")
    @SaCheckPermission("/biz/insurancecompany/detail")
    @GetMapping("/biz/insurancecompany/detail")
    public CommonResult<BizInsuranceCompany> detail(@Valid BizInsuranceCompanyIdParam bizInsuranceCompanyIdParam) {
        return CommonResult.data(bizInsuranceCompanyService.detail(bizInsuranceCompanyIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    @Operation(summary = "获取保险公司动态字段的配置")
    @SaCheckPermission("/biz/insurancecompany/dynamicFieldConfigList")
    @GetMapping("/biz/insurancecompany/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizInsuranceCompanyService.dynamicFieldConfigList(columnName));
    }
}
