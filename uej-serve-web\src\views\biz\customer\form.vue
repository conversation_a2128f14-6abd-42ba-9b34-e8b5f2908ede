<template>
	<xn-form-container
		:title="formData.id ? 'edit customer' : 'add customer'"
		:width="700"
		v-model:open="open"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="First Name：" name="firstName">
						<a-input v-model:value="formData.firstName" placeholder="Please enter First Name" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Sur Name：" name="lastName">
						<a-input v-model:value="formData.lastName" placeholder="Please enter Sur Name" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Gender：" name="gender">
						<a-select
							v-model:value="formData.gender"
							:options="genderOption"
							style="width: 100%"
							placeholder="Please enter Gender"
						>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Level：" name="level">
						<a-input v-model:value="formData.level" placeholder="Please enter Level" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Reward Points：" name="rewardPoints">
						<a-input v-model:value="formData.rewardPoints" placeholder="Please enter Reward Points" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Attend Time：" name="attendTime">
						<a-date-picker
							v-model:value="formData.attendTime"
							value-format="YYYY-MM-DD HH:mm:ss"
							show-time
							placeholder="Please enter Attend Time"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Email：" name="email">
						<a-input v-model:value="formData.email" placeholder="Please enter Email" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Phone：" name="phone">
						<a-input v-model:value="formData.phone" placeholder="Please enter Phone" allow-clear />
					</a-form-item>
				</a-col>

			</a-row>
			<a-row :gutter="16">
						<a-col :span="8">
							<a-form-item label="Signature：" name="signature">
								<a-button @click="xnSignNameRef.show()">Open signature board</a-button>
							</a-form-item>
							<div class="mb-2 xn-wd" v-if="formData.signature">
								<a-image :src="formData.signature" width="100%" />
							</div>
						</a-col>
						<a-col :span="8">
							<a-form-item label="InsuranceCompany：" name="insuranceCompany">
								<a-select
									v-model:value="formData.insuranceCompany"
									:options="insuranceCompanyList"
									style="width: 100%"
									placeholder="Please select insurance company"
								></a-select>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="MemberId：" name="memberId">
								<a-input v-model:value="formData.memberId" placeholder="Please enter your memberId" allow-clear />
							</a-form-item>
						</a-col>
						<XnSignName
							ref="xnSignNameRef"
							:image="formData.signature"
							@successful="
								(e) => {
									formData.signature = e
								}
							"
						/>
					</a-row>
		</a-form>
		<a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
					<xn-form-item :fieldConfig="item" :formData="dynamicFormData" />
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="bizCustomerForm">
	import { cloneDeep } from 'lodash-es'
	import { required, rules } from '@/utils/formRules'
	import bizCustomerApi from '@/api/biz/bizCustomerApi'
	import bizInsuranceCompanyApi from '@/api/biz/bizInsuranceCompanyApi'
	import tool from '@/utils/tool'
	// 抽屉状态
	const open = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)
	// 动态表单
	const dynamicFormRef = ref()
	const dynamicFieldConfigList = ref([])
	const dynamicFormData = ref({})


	const xnSignNameRef=ref()
	const insuranceCompanyList=ref([])

	const genderOption = tool.dictList('GENDER')

	// 打开抽屉
	const onOpen = (record) => {
		open.value = true
		bizCustomerApi.bizCustomerDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
			dynamicFieldConfigList.value = data
		})
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			dynamicFormData.value = JSON.parse(formData.value.extJson || null) || {}
			nextTick(()=>{
				bizInsuranceCompanyApi.bizInsuranceCompanyList().then((data) => {
				insuranceCompanyList.value = data.map((item) => {
					return {
						value: item.id,
						label: item.name
					}
				})
			})
			})
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		dynamicFormData.value = {}
		open.value = false
	}
	// 默认要校验的
	const formRules = {
		phone: [required('Please enter Phone'), rules.phone],
		email: [rules.email]
	}
	// 验证并提交数据
	const onSubmit = () => {
		const promiseList = []
		promiseList.push(
			new Promise((resolve, reject) => {
				formRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		promiseList.push(
			new Promise((resolve, reject) => {
				dynamicFormRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		submitLoading.value = true
		Promise.all(promiseList)
			.then(() => {
				const formDataParam = cloneDeep(formData.value)
				formDataParam.extJson = JSON.stringify(dynamicFormData.value)
				bizCustomerApi
					.bizCustomerSubmitForm(formDataParam, formDataParam.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			})
			.catch(() => {})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
