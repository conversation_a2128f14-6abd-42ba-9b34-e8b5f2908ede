package vip.xiaonuo.biz.modular.offeringgroupitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 套餐明细实体
 *
 * <AUTHOR>
 * @date  2024/06/13 09:58
 **/
@Getter
@Setter
@TableName("biz_offering_group_item")
public class BizOfferingGroupItem {

    /** 主键 */
    @TableId
    @Schema(description = "主键")
    private String id;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 组织id */
    @Schema(description = "组织id")
    private String orgId;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 更新用户 */
    @Schema(description = "更新用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    @Schema(description = "Offering Group ID")
    private String mainId;

    /** OFFERING_ID */
    @Schema(description = "OFFERING_ID")
    private String offeringId;

    /** OFFERING_NAME */
    @Schema(description = "OFFERING_NAME")
    private String offeringName;

    /** LIST PRICE */
    @Schema(description = "LIST PRICE")
    private String listPrice;

    /** PLANNING HOURS */
    @Schema(description = "PLANNING HOURS")
    private String planningHours;

    /** NUM */
    @Schema(description = "NUM")
    private String num;

    /** TOTAL PRICE */
    @Schema(description = "TOTAL PRICE")
    private String totalPrice;
}
