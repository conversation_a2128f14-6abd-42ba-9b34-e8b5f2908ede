
package vip.xiaonuo.biz.modular.task.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 任务信息查询参数
 *
 * <AUTHOR>
 * @date  2024/06/12 15:42
 **/
@Getter
@Setter
public class BizTaskPageParam {

    /** 当前页 */
    @Schema(description = "当前页码")
    private Integer current;

    /** 每页条数 */
    @Schema(description = "每页条数")
    private Integer size;

    /** 排序字段 */
    @Schema(description = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @Schema(description = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @Schema(description = "关键词")
    private String searchKey;

    /** Item Name */
    @Schema(description = "Item Name")
    private String itemName;

    /** Create by */
    @Schema(description = "Create by")
    private String creator;

    private String orgId;
}
