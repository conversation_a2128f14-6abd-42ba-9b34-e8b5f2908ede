---
description:
globs:
alwaysApply: false
---
# 项目结构概览

本项目是服务大师系统，包含前端和后端两部分：

## 前端项目 (uej-serve-web)

前端项目使用Vue框架开发，主要文件结构如下：

- [package.json](mdc:uej-serve-web/package.json) - 项目依赖配置
- [vite.config.mjs](mdc:uej-serve-web/vite.config.mjs) - Vite构建工具配置
- [src/main.js](mdc:uej-serve-web/src/main.js) - 应用入口文件
- [src/App.vue](mdc:uej-serve-web/src/App.vue) - 根组件
- [src/api/](mdc:uej-serve-web/src/api/) - API接口定义
- [src/views/](mdc:uej-serve-web/src/views/) - 页面视图组件
- [src/components/](mdc:uej-serve-web/src/components/) - 通用组件
- [src/router/](mdc:uej-serve-web/src/router/) - 路由配置
- [src/store/](mdc:uej-serve-web/src/store/) - 状态管理
- [src/utils/](mdc:uej-serve-web/src/utils/) - 工具函数

## 后端项目 (uej-serve-api)

后端项目基于Java开发，主要结构：

- [pom.xml](mdc:uej-serve-api/pom.xml) - Maven项目配置
- [snowy-web-app/](mdc:uej-serve-api/snowy-web-app/) - Web应用主模块
- [snowy-plugin/](mdc:uej-serve-api/snowy-plugin/) - 插件模块
- [snowy-common/](mdc:uej-serve-api/snowy-common/) - 通用组件模块

## 数据库

- [uej_serve_foreign.sql](mdc:uej_serve_foreign.sql) - 数据库SQL文件
