
package vip.xiaonuo.biz.modular.giftcard.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 礼品卡信息Id参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:07
 **/
@Getter
@Setter
public class BizGiftCardIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;
}
