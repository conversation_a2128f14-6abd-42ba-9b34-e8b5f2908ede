package vip.xiaonuo.biz.modular.giftcardlog.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.giftcardlog.entity.BizGiftCardLog;
import vip.xiaonuo.biz.modular.giftcardlog.mapper.BizGiftCardLogMapper;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogAddParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogEditParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogIdParam;
import vip.xiaonuo.biz.modular.giftcardlog.param.BizGiftCardLogPageParam;
import vip.xiaonuo.biz.modular.giftcardlog.service.BizGiftCardLogService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 礼品卡记录Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/13 09:46
 **/
@Service
public class BizGiftCardLogServiceImpl extends ServiceImpl<BizGiftCardLogMapper, BizGiftCardLog> implements BizGiftCardLogService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizGiftCardLog> page(BizGiftCardLogPageParam bizGiftCardLogPageParam) {
        QueryWrapper<BizGiftCardLog> queryWrapper = new QueryWrapper<BizGiftCardLog>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizGiftCardLogPageParam.getTotalValue())) {
            queryWrapper.lambda().eq(BizGiftCardLog::getTotalValue, bizGiftCardLogPageParam.getTotalValue());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardLogPageParam.getCardNo())) {
            queryWrapper.lambda().eq(BizGiftCardLog::getCardNo, bizGiftCardLogPageParam.getCardNo());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardLogPageParam.getTaskId())) {
            queryWrapper.lambda().eq(BizGiftCardLog::getTaskId, bizGiftCardLogPageParam.getTaskId());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardLogPageParam.getType())) {
            queryWrapper.lambda().eq(BizGiftCardLog::getType, bizGiftCardLogPageParam.getType());
        }
        if(ObjectUtil.isAllNotEmpty(bizGiftCardLogPageParam.getSortField(), bizGiftCardLogPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizGiftCardLogPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizGiftCardLogPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizGiftCardLogPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BizGiftCardLog::getCreateTime);
        }
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizGiftCardLog::getOrgId, loginUserDataScope);
        } else {
            // 如果没有数据范围权限，只能查看自己创建的礼品卡日志
            queryWrapper.lambda().eq(BizGiftCardLog::getCreateUser, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizGiftCardLogAddParam bizGiftCardLogAddParam) {
        // 礼品卡日志添加不需要特殊的权限校验，因为任何用户都可以添加礼品卡日志
        BizGiftCardLog bizGiftCardLog = BeanUtil.toBean(bizGiftCardLogAddParam, BizGiftCardLog.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizGiftCardLog.getOrgId())) {
            String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
            if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                bizGiftCardLog.setOrgId(currentUserOrgId);
            }
        }
        
        this.save(bizGiftCardLog);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizGiftCardLogEditParam bizGiftCardLogEditParam) {
        BizGiftCardLog bizGiftCardLog = this.queryEntity(bizGiftCardLogEditParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizGiftCardLog.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的礼品卡日志，机构id：{}", bizGiftCardLog.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能编辑自己创建的礼品卡日志
            if(!bizGiftCardLog.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该礼品卡日志，卡号：{}", bizGiftCardLog.getCardNo());
            }
        }
        
        BeanUtil.copyProperties(bizGiftCardLogEditParam, bizGiftCardLog);
        this.updateById(bizGiftCardLog);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizGiftCardLogIdParam> bizGiftCardLogIdParamList) {
        List<String> giftCardLogIdList = CollStreamUtil.toList(bizGiftCardLogIdParamList, BizGiftCardLogIdParam::getId);
        if(ObjectUtil.isNotEmpty(giftCardLogIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                List<BizGiftCardLog> giftCardLogList = this.listByIds(giftCardLogIdList);
                for(BizGiftCardLog giftCardLog : giftCardLogList) {
                    if(!loginUserDataScope.contains(giftCardLog.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的礼品卡日志，机构id：{}", giftCardLog.getOrgId());
                    }
                }
            } else {
                List<BizGiftCardLog> giftCardLogList = this.listByIds(giftCardLogIdList);
                for(BizGiftCardLog giftCardLog : giftCardLogList) {
                    // 如果没有数据范围权限，只能删除自己创建的礼品卡日志
                    if(!giftCardLog.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该礼品卡日志，卡号：{}", giftCardLog.getCardNo());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(giftCardLogIdList);
    }

    @Override
    public BizGiftCardLog detail(BizGiftCardLogIdParam bizGiftCardLogIdParam) {
        BizGiftCardLog bizGiftCardLog = this.queryEntity(bizGiftCardLogIdParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizGiftCardLog.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的礼品卡日志，机构id：{}", bizGiftCardLog.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能查看自己创建的礼品卡日志
            if(!bizGiftCardLog.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该礼品卡日志，卡号：{}", bizGiftCardLog.getCardNo());
            }
        }
        
        return bizGiftCardLog;
    }

    @Override
    public BizGiftCardLog queryEntity(String id) {
        BizGiftCardLog bizGiftCardLog = this.getById(id);
        if(ObjectUtil.isEmpty(bizGiftCardLog)) {
            throw new CommonException("礼品卡记录不存在，id值为：{}", id);
        }
        return bizGiftCardLog;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizGiftCardLogServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizGiftCardLog.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}
