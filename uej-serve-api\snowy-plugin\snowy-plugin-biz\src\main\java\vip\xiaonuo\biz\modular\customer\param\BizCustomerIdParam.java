
package vip.xiaonuo.biz.modular.customer.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * 外部会员信息Id参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:22
 **/
@Getter
@Setter
public class BizCustomerIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;

    private List<String> idList;
}
