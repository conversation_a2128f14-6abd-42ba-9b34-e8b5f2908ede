
package vip.xiaonuo.biz.modular.offeringgroupitem.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 套餐明细Id参数
 *
 * <AUTHOR>
 * @date  2024/06/13 09:58
 **/
@Getter
@Setter
public class BizOfferingGroupItemIdParam {

    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;
}
