export default {
	common: {
		searchButton: 'Search',
		search: 'Search',
		resetButton: 'Reset',
		addButton: 'Add',
		editButton: 'Edit',
		removeButton: 'Delete',
		batchRemoveButton: 'Bulk Delete',
		detailButton: 'Details',
		searchKey: 'Key',
		imports: 'Import',
		more: 'More',
		export: 'Export',
		cancel: 'Cancel',
		save: 'Save',
		submit: 'Submit',
		send: 'Send',
		copy: 'Copy',
		clockButton: 'ClockIn',
		columnVisible: 'ColumnVisible',
		packUp: 'Fold',
		unfold: 'Unfold',
		refresh: 'Refresh',
		density: 'density',
		default: 'default',
		middle: 'middle',
		small: 'small',
		tips: 'Tips',
		log: 'log',
		clearing: 'Clearing...',
		styleSetting: 'Style',
		layoutSetting: 'Layout',
		themeColor: 'Themecolor',
		topBarThemeColor: 'TopBar Theme Color',
		topBarThemeColorBar: 'Top bar theme color bar',
		moduleDock: 'Module',
		crumbs: 'crumbs',
		multiLabel: 'multi-label',
		collapsibleMenu: 'Collapsible menu',
		fixedWidth: 'Fixed width',
		menuExpandsExclusively: 'The menu expands exclusively',
		loginWatermark: 'Login watermark',
		footerCopyrightInformation: 'Footer copyright information',
		roundedCornerStyle: 'Rounded corner style',
		formStyle: 'Form style',
		maximize: 'maximize',
		restore: 'restore',
		clear: 'clear',
		selected: 'Selected',
		total: 'total',
		addCurrentData: 'Add current data',
		toBeSelectedList: 'To be selected list',
		record: 'Record',
		pause: 'Pause',
		continue: 'continue',
		complete: 'complete',
		queue: 'Queue',
		progress: 'Progress',
		legend: 'Legend',
		close: 'close',
		closeOtherTabs: 'Close other tabs',
		newWindowOpens: 'Open to New Window',
		addPersonnel: 'New Person',
		searchPage: 'Search',
		openSearchPanel: 'Open the search panel',
		select: 'Select',
		verify: 'Confirm',
		revocation: 'revocation',
		Terminate: 'Terminate',
		Activate: 'Active',
		hangUp: 'Hang up',
		variable: 'variable',
		turnTo: 'Turn to',
		skip: 'Skip',
		Revive: 'Revive',
		transfer: 'Transfer',
		DictionaryName: 'Dictionary name',
		sublevel: 'Sublevel',
		processVariable: 'Process variable',
		StationMessage: 'Internal message',
		opLog: 'Operation log',
		addSchedule: 'Add schedule',
		date: 'Time',
		dateRange: 'Date Range',
		shortcut: 'Shortcut',
		visLog: 'Visit log',
		操作成功: 'Successfully!',
		autoCopySchedule: 'Automatically copy the schedule',
		autoCopyScheduleMessage: 'The schedule is automatically copied to the following week every Friday  morning',
		404: '404 not found',
		403: 'Sorry, you do not have permission to access this page',
		服务器异常: 'Server exception',
		accredit: 'accredit',
		AuthorizedResource: 'Allocate Resource',
		AuthorizeMobileResources: 'Allocate Mobile Resources',
		authorization: 'Grant Permissions',
		AuthorizedUser: 'Grant User',
		switching: 'Transfer to',
		loading: 'loading...',
		noScript: 'The browser does not support messaging',
		connectError: 'An error occurred. The message got in real time. The connection to the server was disconnected',
		rightClickTip: 'Right-click the left Gantt chart directory for quick operations',
		batchButton: 'batchButton',
		电子签名: 'Electronic signature',
		画笔粗细: 'Brush thickness',
		是否裁剪: 'Cut or not',
		预览: 'Preview',
		清屏: 'Clear screen',
		确定: 'Confirm',
		无任何签字: 'No signature',
		categoryName: 'Category Name',
		categoryCode: 'Category Code',
		parentCategory: 'Parent Category',
		sortOrder: 'Sort Order',
		level: 'Level',
		parentLevel: 'Parent',
		childLevel: 'Child',
		topLevel: 'Top Level',
		editProductCategory: 'Edit Product Category',
		addProductCategory: 'Add Product Category',
		deleteCategoryConfirm: 'Delete this category and its subcategories?',
		pleaseInputCategoryName: 'Please enter category name',
		pleaseInputCategoryCode: 'Please enter category code',
		pleaseSelectParentCategory: 'Please select parent category',
		operation: 'Operation',
		'Product Category Management Test Page': 'Product Category Management Test Page',
		'Test API': 'Test API'
	},
	model: {
		user: 'user',
		org: 'org',
		pos: 'pos',
		role: 'role',
		bizUser: 'bizUser'
	},
	login: {
		signInTitle: 'Login',
		forgetPassword: 'Forget password',
		signIn: 'Login',
		signInOther: 'Login with',
		accountPlaceholder: 'Please input User name or Email',
		accountError: 'Please input User name or Email',
		PWPlaceholder: 'Please input password',
		PWError: 'Please input password',
		validLaceholder: 'Please input valid',
		validError: 'Please input valid',
		accountPassword: 'Account Password',
		phoneSms: 'Phone SMS',
		phonePlaceholder: 'Please input a phone',
		smsCodePlaceholder: 'Please input a SMS code',
		getSmsCode: 'SMS code',
		machineValidation: 'Machine Validation',
		sendingSmsMessage: 'Sending SMS Message',
		newPwdPlaceholder: 'Please input a new password',
		backLogin: 'Back Login',
		restPassword: 'Rest Password',
		emailPlaceholder: 'Please input a email',
		emailCodePlaceholder: 'Please input a Email code',
		restPhoneType: 'For phone rest',
		restEmailType: 'For email rest',
		loginOut: 'Logout',
		popconfirmLoginOut: 'Logout current user？',
		popconfirmClearCache: 'Clear all caches？',
		quitting: 'Exiting...',
		loginSuccessful: 'Login Successful',
		backLogin: 'Back Login',
		backGoOne: 'Previous page'
	},
	user: {
		workSpace: 'My Account',
		userStatus: 'User Status',
		resetPassword: 'Reset Password',
		role: 'Role',
		batchExportButton: 'Bulk Export',
		grantRole: 'Grant Role',
		grantResource: 'Grant Resource',
		grantPermission: 'Grant Permission',
		exportUserInfo: 'Export UserInfo',
		placeholderNameAndSearchKey: 'Please input your name or keyword',
		placeholderUserStatus: 'Please select status',
		popconfirmDeleteUser: 'Confirm to delete it？',
		popconfirmResatUserPwd: 'Confirm to reset？',
		popconfirmDeleteOrg: 'Confirm to delete it？',
		popconfirmDeleteJob: 'Confirm to delete it？',
		popconfirmDeleteRole: 'Confirm to delete it？',
		clearCache: 'Clear cache',
		batchProcessButton: 'Bulk process this information?',
		batchRemoveButton: 'Delete this information?',
		userSelection: 'User selection',
		popconfirmDeleteProcess: 'Confirm to delete this process?',
		transferMessage: 'After you migrate to a node, you will automatically migrate to the latest version of this model',
		processVariableMessage:
			'The variable function is only supported in the approval state, and non-professionals are prohibited to modify it'
	},
	menu: {
		流程计时: 'Workspace',
		排班表: 'Roster',
		任务信息: 'Task information',
		服务或产品: 'Service or Products',
		服务或产品组合: 'package products',
		礼品卡信息: 'Gift card',
		代金券信息: 'Voucher',
		外部会员: 'Membership',
		工时: 'Timesheet',
		任务子表: 'Task subtable',
		使用记录管理: 'Use record detail',
		套餐明细管理: 'Package products detail',
		任务数据统计管理: 'Task data analytics',
		排班管理: 'Scheduling management',
		排班表当日属性管理: 'Scheduling schedule day attribute management',
		机构管理: 'Organization',
		人员管理: 'Employee',
		岗位管理: 'Position',
		发起申请: 'New application',
		已发申请: 'Issued application',
		待办事宜: 'To-do ',
		已办事宜: 'Done ',
		抄送事宜: 'Cc ',
		流程监控: 'Process monitoring',
		业务字典: 'Service dictionary',
		系统首页: 'Home',
		组织架构: 'Org Structure',
		组织管理: 'Org Management',
		用户管理: 'User Management',
		职位管理: 'Position Management',
		角色管理: 'Role Management',
		模块管理: 'Module Management',
		菜单管理: 'Menu management',
		基础工具: 'Basic tool',
		文件管理: 'File management',
		邮件推送: 'Email push',
		短信发送: 'SMS sending',
		站内信息: 'Site information',
		系统运维: 'System operation and maintenance',
		三方用户: 'Tripartite user',
		数据字典: 'Data dictionary',
		系统配置: 'System config',
		会话管理: 'Session management',
		系统监控: 'System monitoring',
		工作流程: 'Work flow',
		日志审计: 'Log audit',
		访问日志: 'Access log',
		操作日志: 'Operation log',
		流水序号: 'Flow sequence number',
		打印模板: 'Print template',
		流程设计: 'Process design',
		SAAS管理: 'SAAS management',
		多数据源: 'Multiple data sources',
		动态租户: 'Dynamic tenant',
		公司架构: 'Corporate structure',
		在线办公: 'Online office',
		支付体验: 'Payment experience',
		支付示例: 'Payment example',
		订单管理: 'Order management',
		在线开发: 'Online development',
		代码生成: 'Code generation',
		数据建模: 'Data modeling',
		动态字段: 'Dynamic field',
		开发示例: 'Development example',
		常见示例: 'Common example',
		图标选择: 'Icon selection',
		地图取点: 'Map fetching',
		数据导入: 'Data import',
		二维码生成: 'QR code generation',
		条码生成: 'Barcode generation',
		页面打印: 'Page printing',
		模板导出: 'Template export',
		文本编辑: 'Text editing',
		跳转路由: 'Skip route',
		高德地图: 'amap',
		百度地图: 'Baidu map',
		统计图表: 'Statistical chart',
		ECK线图: 'ECK diagram',
		EC仪表图: 'EC instrument diagram',
		EC散点图: 'EC scatter plot',
		EC柱状图: 'EC bar chart',
		EC树形图: 'EC tree diagram',
		EC漏斗图: 'EC funnel diagram',
		EC线形图: 'EC line diagram',
		EC饼状图: 'EC pie chart',
		G2进度图: 'G2 Progress Chart',
		G2子弹图: 'G2 Bullet diagram',
		G2散点图: 'G2 Scatter plot',
		G2柱状图: 'G2 bar chart',
		G2漏斗图: 'G2 funnel plot',
		G2折线图: 'G2 Line chart',
		G2词云图: 'G2 word cloud map',
		G2面积图: 'G2 area map',
		G2饼状图: 'G2 pie chart',
		G2条形图: 'G2 bar chart',
		移动端管理: 'Mobile terminal management',
		模块管理: 'Module management',
		菜单管理: 'Menu management',
		报表插件: 'Report plug-in',
		报表管理: 'Report management',
		系统: 'System',
		权限管控: 'Authority control',
		连接监控: 'Connection monitoring',
		接口文档: 'Interface document',
		任务调度: 'Task scheduling',
		运营: 'Service',
		业务: 'Admin',
		系统: 'System',
		个人中心: 'My profile',
		排班表编辑: 'Roster edit',
		更多: 'more',
		找回密码: 'Forget password',
		登录: 'login',
		礼品卡记录管理: 'Gift card record',
		业务配置管理: 'Service configuration',
		设置: 'Setting',
		产品: 'Products',
		考勤记录管理: 'Attendance record',
		打卡: 'Clock in',
		保险公司管理: 'Insurance company',
		电子签名: 'Electronic signature',
		报表: 'Report',
		运营: 'Service',
		业务: 'Admin',
		设置: 'Setting',
		系统: 'System',
		排班编辑: 'Shift editing',
		日扎帐表:'Daily account statement',
		每日报表:'Daily report',
		项目结款记录表管理:'Payment Record Form',
		配置:'Setting',
		管理:'Management',
		产品类目维护: 'Product Category Management',
		邮箱配置管理: 'Email Configuration Management'
	},
	table: {
		头像: 'My Image',
		账号: 'Account',
		姓名: 'Name',
		性别: 'Gender',
		手机: 'Phone',
		机构: 'orgName',
		职位: 'positionName',
		状态: 'userStatus',
		action: 'action',
		操作: 'action',
		机构名称: 'Organization name',
		分类: 'category',
		排序: 'sort',
		岗位名称: 'Job title',
		流程名称: 'Process name',
		创建时间: 'Creation time',
		标题: 'title',
		流水号: 'Serial number',
		定义名称: 'Define name',
		定义版本: 'Version',
		发起组织: 'Initiating organization',
		发起职位: 'Initiating position',
		耗时: 'time-consuming',
		当前节点: 'Current node',
		状态: 'status',
		发起时间: 'Start time',
		结束时间: 'End time',
		摘要: 'Description',
		办理人: 'transactor',
		发起人: 'initiator',
		发起人组织: 'Sponsor organization',
		发起人职位: 'Sponsor position',
		办理节点: 'Handling node',
		办理时间: 'Handling time',
		待阅流程: 'Pending process',
		已阅流程: 'Read process',
		当前办理人: 'Current agent',
		字典名称: 'Dictionary name',
		字典值: 'Dictionary value',
		TherapistQueue: 'Therapist Queue',
		Finish: 'Finish',
		Free: 'Free',
		Next: 'Next',
		组织名称: 'Organization name',
		职位名称: 'Job title',
		角色名称: 'role name',
		颜色: 'color',
		签名: 'signature'
	},
	flw: {
		newProcessTask: 'New process',
		myDraft: 'My draft'
	},
	searchFormData: {
		process: 'Process name',
		processHolder: 'Please enter Process name',
		pleaseEnter: 'Please enter'
	},
	setting: {
		uej: 'service master',
		modal: 'dialog',
		drawer: 'drawer',
		暗色主题风格: 'Dark theme',
		亮色主题风格: 'Bright theme',
		暗黑模式: 'Dark mode',
		经典: 'Classic',
		双排菜单: 'Double row menu',
		顶部菜单: 'Top menu',
		alert:
			'The above configuration can be previewed in real time, the developer can configure the default value in config/index.js, it is not recommended to open the layout Settings in the production environment'
	},
	dict: {
		new: 'new',
		stop: 'stop',
		progress: 'progress',
		finish: 'finish',
		paid: 'paid',
		cancelled: 'cancelled',
		remind: 'remind'
	},
	'营收趋势':'Revenue Trends',
	'周':'week',
	'双周': 'two weeks',
	'月':'month',
	'年':'year',
	'季度':'quarter',
	'今日排班':"Today's schedule",
	'今年排班':"This year's schedule",
	'全部排班':'All scheduling',
	'今日预约':'Todays appointment',
	'今年预约':"This year's appointment",
	'预约总数':'Total number of appointments',
	'今日营业额':"Today's revenue",
	'今年营业额':"This year's revenue",
	'全部营业额':'Total revenue',
	'今日礼品卡金额':"Today's gift card amount",
	'今年礼品卡金额':"This year's gift card amount",
	'全部礼品卡金额':'All gift card amounts',
	'工资表': 'Payroll statement',
	'工时效率报表': 'Work efficiency report'
}
