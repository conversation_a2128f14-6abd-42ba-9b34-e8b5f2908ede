@import './realdark';
@import './default';

.body, html {
	width: 100%;
	height: 100%;
	background-color: #f6f8f9;
}

a, button, input, textarea {
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	box-sizing: border-box;
	outline: none !important;
	-webkit-appearance: none;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	outline: none;
}

/* 大布局样式 */
.admin-ui {
	overflow: hidden;
	height: 100%;
	display: flex;
	flex-flow: column;
}

.admin-ui-wrapper {
	display: flex;
	flex: 1;
	overflow: auto;
}

.admin-ui-main {
	display: flex;
	flex-direction: column;
	height: 100%;
	flex: 1;
}
.main-content-wrapper{
	padding: 11px 11px 0px;
	overflow-y: auto;
	overflow-x: hidden;
	flex: auto;
}

.main-bottom-wrapper {
	height: 60px;
	margin-top: 25px;
	text-align: center;
	flex: auto;
	display: flex;
	align-items: flex-end;
	justify-content: center;
	padding-bottom: 25px;
}

/* 双排菜单布局 */
.snowy-doublerow-layout-menu {
	padding-right: 5px;
	line-height: 0;
	align-items: center;
}

.snowy-doublerow-layout-menu-item-fort-div {
	overflow: hidden;
	text-overflow: ellipsis;
	opacity: 1;
	display: block;
	flex: auto;
}

.snowy-doublerow-layout-menu-item-fort-div-span {
	font-size: 13px;
	text-overflow: ellipsis;
}

.snowy-title{
	color: var(--text-color);
}
.ant-layout-sider-collapsed{
	.logo-bar>span{
		display: none;
	}
}
.ant-layout-sider-dark{
	.snowy-header-logo{
		color: #fff;
	}
}

/* 设置抽屉样式 */
.layout-setting {
	position: fixed;
	width: 40px;
	height: 40px;
	border-radius: 3px 0 0 3px;
	bottom: 50%;
	right: 0px;
	z-index: 100;
	background: @primary-color;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.layout-setting i {
	color: #fff;
}

/* 头部 */
.snowy-header {
	height: 50px;
	display: flex;
	justify-content: space-between;
	border-bottom: 1px solid var(--header-bottom);
	box-shadow: 0 0.4px 0.5px rgb(0 21 41 / 12%);
	.ant-menu-item{
		height: 48px;
		line-height: 48px;
	}
	background: var(--snowy-background-color);
}
// 应用主题色
.snowy-header-primary-color {
	color: white;
	background-color: var(--primary-color);
	.ant-badge{
		color: white;
	}
	.ant-breadcrumb-link {
		color: white;
	}
	.ant-breadcrumb-separator {
		color: white;
	}
	.ant-menu-light .ant-menu-item:hover{
		color: #ccc;
		background-color: var(--primary-7);
	}
}

.ant-layout-sider-dark {
	.snowy-title{
		color: #fff;
	}
}

.snowy-header-left {
	display: flex;
	align-items: center;
	padding-left: 20px;
}

.snowy-header-left .menu-unfold-outlined {
	padding: 0 12px
}

.snowy-header-right {
	display: flex;
	align-items: center;
}

.snowy-header-logo {
	height: 49px;
	display: flex;
	justify-content: space-between;
	border-bottom: 1px solid rgba(255, 255, 255, 0.04);
}

.snowy-header-logo-primary-color {
	color: white;
	background-color: var(--primary-color);
}

.snowy-header-logo .logo-bar {
	font-weight: bold;
	display: flex;
	align-items: center;
	font-size: 20px;
}

.snowy-header-logo .logo-bar .logo {
	margin-right: 10px;
	width: 35px;
	height: 35px;
}

.top-snowy-header {
	background: #001529;
	color: white;
}
.top-snowy-header-light {
	background: #ffffff;
	color: #000000;
}
.top-snowy-header-layout {
	background: var(--primary-color);
	color: #ffffff;
}
.panel-item {
	padding: 0 10px;
	cursor: pointer;
	height: 100%;
	display: flex;
	align-items: center;
	/*color: var(--font-color);*/
}
.panel-item:hover {
	background: var(--header-color-split);
}
.contextmenu {
	position: fixed;
	width: 200px;
	margin:0;
	border-radius: 0px;
	background: @body-background;
	border: 1px solid var(--border-color-split);
	box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
	z-index: 3000;
	list-style-type: none;
	padding: 10px 0;
}
.contextmenu hr {
	margin:5px 0;
	border: none;
	height: 1px;
	font-size: 0px;
	background-color: var(--border-color-split)
}
.contextmenu li {
	display: flex;
	align-items: center;
	margin:0;
	cursor: pointer;
	line-height: 30px;
	padding: 0 17px;
	color: @text-color;
}
.contextmenu li i {
	font-size: 14px;
	margin-right: 10px;
}
.contextmenu li:hover {
	background-color: @component-background;
	color: #66b1ff;
}
.contextmenu li.disabled {
	cursor: not-allowed;
	color: #bbb;
	background: transparent;
}

/*页面最大化*/
.admin-ui.main-maximize {
	.main-maximize-exit {
		display: block;
	}
	.ant-layout-sider, .ant-layout-sider-dark, .layout-setting, .snowy-header, .admin-ui-breadcrumb, .snowy-tags {
		display: none;
	}
	.main-content-wrapper {
		padding: 0;
	}
}

/* 最大化后的退出按钮 */
.main-maximize-exit {
	display: none;
	position: fixed;
	z-index: 3000;
	top: -20px;
	padding-top: 18px;
	left: 50%;
	margin-left: -20px;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	cursor: pointer;
	background: rgba(0, 0, 0, 0.2);
	text-align: center;
}

.main-maximize-exit:hover {
	background: rgba(0, 0, 0, 0.4);
}

.ant-layout-sider{
	overflow: auto;
}

/* 重写antdv的一些样式，定义到全局 */
.ant-card-head-title {
	padding: 12px 0!important;
}
.ant-tabs-large > .ant-tabs-nav .ant-tabs-tab {
	padding: 12px 0!important;
}
.ant-card-extra {
	padding: 12px 0!important;
}
.ant-card-head {
	border-bottom: 0px !important;
	min-height: 50px !important;
}

/* 重写antdv的表格滚动条 */
.ant-table-body, .ant-table-content{
	&::-webkit-scrollbar {
		height: 10px;
		width: 10px;
	}
	&::-webkit-scrollbar-thumb {
		border-radius: 10px;
		-webkit-box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
		background: @border-color-split;
	}
	&::-webkit-scrollbar-track {
		-webkit-box-shadow: 0;
		border-radius: 10px;
		background: @background-color-base;
	}
}

.left-span-label {
	border-left: 4px solid var(--primary-color);
	font-size: 15px;
	color: var(--font-color);
	font-weight: 600;
	padding-left: 8px;
}

// 滚动条，需要哪里，加哪个class
body,
.ant-scrolling-effect,
.ant-drawer-wrapper-body,
.ant-drawer-body,
.admin-ui,
.ant-modal-wrap,
.ant-transfer-list-content,
.ant-card,
.ant-layout-sider,
.CodeMirror-scroll,
.main-content-wrapper,
.xn-icon-select-list,
.form-user-table,
.scopeDefineOrgTreeDiv,
.user-table,
.role-table,
.org-table,
.pos-table,
.poi-list,
.snowy-orgpos-vis,
.index-message-list,
.ant-picker-time-panel-column,
.timeline-div,
.gen-preview-content,
.ant-menu,
.ant-tabs-dropdown-menu,

.admin-ui-main{
	&::-webkit-scrollbar {
		/*滚动条整体样式*/
		width : 0;  /*高宽分别对应横竖滚动条的尺寸*/
		height: 0;
	}
	&::-webkit-scrollbar-thumb {
		/*滚动条里面小方块*/
		border-radius   : 10px;
		background-color: @component-background; // skyblue
		background-image: -webkit-linear-gradient(
			45deg,
			rgba(255, 255, 255, 0.2) 25%,
			transparent 25%,
			transparent 50%,
			rgba(255, 255, 255, 0.2) 50%,
			rgba(255, 255, 255, 0.2) 75%,
			transparent 75%,
			transparent
		);
	}
	&::-webkit-scrollbar-track {
		/*滚动条里面轨道*/
		box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
		background   : @component-background;
		border-radius: 5px;
		opacity: 0;
		display: none;
	}
}

.json-box-9136076486841527{
	overflow: hidden!important;;
	.CodeMirror-scrollbar-filler{
		display: none!important;
	}
}
.ant-modal-close-x .anticon {
	padding: 2px !important;
}
.xn-mb10 {
	margin-bottom: 10px;
}
.xn-mt4 {
	margin-top: 4px;
}
.xn-mg08 {
	margin: 0 8px;
}
.xn-fdr {
	float: right;
}
.xn-wd {
	width: 100%;
}
.xn-wd90 {
	width: 90px;
}
.xn-wdcalc-70 {
	width: calc(100% - 70px);
}
.xn-mr8 {
	margin-right: 8px;
}
.xn-ht400 {
	height: 400px;
}
.xn-wh25 {
	height: 25px;
	width: 25px;
}
.xn-ml10 {
	margin-left: 10px;
}
.xn-pl0 {
	padding-left: 0px;
}
.xn-pd8 {
	padding: 8px;
}
.xn-pb10 {
	padding-bottom: 10px;
}
.xn-color-a0a0a0 {
	color: #a0a0a0;
}
.xn-color-d9d9d9 {
	color: #d9d9d9;
}
.xn-color-ff4d4f {
	color: #ff4d4f;
}
.xn-color-00025 {
	color:  rgba(0, 0, 0, 0.25);
}
.xn-jk-line {
	width: 188px;
	margin-bottom: 8px;
	display: block;
}
.xn-findform-line {
	border: 1px solid var(--border-color-split);
	cursor: pointer;
	width: 100%;
	height: 40px;
}
.odd {
	background-color: var(--table-row-hover-bg);
}
.snowy-theme-dark .odd {
	background-color: #1d1d1d
}
// 以下是重写表单设计器的样式
.list-main {
	background: var(--auto-judge-before-color) !important;
}
.drag-move-box:before {
	background: var(--primary-color) !important;
}
.drag-move-box>.delete {
	background: var(--primary-color) !important;
}
.drag-move-box>.copy {
	background: var(--primary-color) !important;
}
.drag-move-box .show-key-box {
	color: var(--primary-color) !important;
}
.left-ul-item:hover {
	color: var(--primary-color) !important;
	border: 1px solid var(--primary-color) !important;
	-webkit-box-shadow: 0 2px 6px var(--primary-color) !important;
	box-shadow: 0 2px 6px var(--primary-color) !important;
}
.list-main>.moving:before {
	background: var(--primary-color) !important;
}
.operating-area a:hover {
	color: var(--primary-color) !important;
}
.batch-box>.delete {
	background: var(--primary-color) !important;
}
.batch-box>.copy {
	background: var(--primary-color) !important;
}
.batch-box.active:before {
	background: var(--primary-color) !important;
}
.ant-advanced-search-form{
	margin-bottom: 10px;
}
