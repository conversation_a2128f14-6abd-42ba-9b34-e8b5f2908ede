package vip.xiaonuo.biz.modular.webhook.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.param.BizTaskIdParam;
import vip.xiaonuo.biz.modular.task.service.BizTaskService;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;
import vip.xiaonuo.biz.modular.taskitem.service.BizTaskItemService;
import vip.xiaonuo.biz.modular.webhook.param.WordPressWebhookParam;
import vip.xiaonuo.biz.modular.webhook.param.AmeliaWebhookParam;
import vip.xiaonuo.biz.modular.webhook.adapter.AmeliaWebhookAdapter;
import vip.xiaonuo.biz.modular.webhook.service.WebhookService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import vip.xiaonuo.sys.modular.user.result.SysLoginUser;
import vip.xiaonuo.sys.modular.user.service.SysUserService;

import java.util.List;
import java.util.ArrayList;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.Map;
import java.util.HashMap;

/**
 * WordPress Webhook接收控制器
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Tag(name = "WordPress Webhook控制器")
@RestController
@Validated
@Slf4j
public class WebhookController {

    @Resource
    private WebhookService webhookService;

    @Resource
    private BizTaskService bizTaskService;

    @Resource
    private BizTaskItemService bizTaskItemService;

    @Resource
    private AmeliaWebhookAdapter ameliaWebhookAdapter;

    @Resource
    private SysUserService sysUserService;

    /**
     * 接收WordPress的任务创建webhook
     *
     * @param requestBody WordPress传来的数据
     * @return 处理结果
     */
    @Operation(summary = "接收WordPress任务创建webhook")
    @CommonLog("接收WordPress任务创建webhook")
    @PostMapping("/webhook/wordpress/task/create")
    public CommonResult<String> createTask(@RequestBody String requestBody, HttpServletRequest request) {
        try {
            log.info("接收到WordPress任务创建webhook: {}", requestBody);

            // 验证API Key（URL参数）
            if (!webhookService.verifyWebhookApiKey(request)) {
                log.warn("WordPress webhook API Key验证失败");
                return CommonResult.error("API Key验证失败");
            }

            WordPressWebhookParam webhookParam = new WordPressWebhookParam();

            // 解析JSON数据
            JSONObject jsonObject = JSONUtil.parseObj(requestBody);

            // 检查是否是特殊的Amelia格式（直接包含appointment字段）
            if (jsonObject.containsKey("appointment")) {
                log.info("检测到特殊Amelia格式的webhook，进行解析");

                JSONObject appointment = jsonObject.getJSONObject("appointment");
                JSONObject bookings = jsonObject.getJSONObject("bookings");

                // 设置基本信息
                webhookParam.setAction("create");
                webhookParam.setAppointmentId(appointment.getStr("id"));
                webhookParam.setStatus(appointment.getStr("status"));
                webhookParam.setTimestamp(System.currentTimeMillis() / 1000);

                // 提取location信息
                String locationName = null;
                String orgId = null;
                if (appointment.containsKey("location")) {
                    JSONObject location = appointment.getJSONObject("location");
                    locationName = location.getStr("name");
                    if (StrUtil.isNotEmpty(locationName)) {
                        orgId = webhookService.mapAmeliaLocationToSystemOrg(locationName);
                        log.info("Location映射: {} -> orgId: {}", locationName, orgId);
                    }
                }

                // 提取customer信息
                String customerEmail = null;
                String customerId = null;
                String customerName = null;
                if (bookings != null && bookings.containsKey("0")) {
                    JSONObject firstBooking = bookings.getJSONObject("0");
                    if (firstBooking.containsKey("customer")) {
                        JSONObject customer = firstBooking.getJSONObject("customer");
                        customerEmail = customer.getStr("email");
                        String firstName = customer.getStr("firstName");
                        String lastName = customer.getStr("lastName");
                        customerName = (firstName != null ? firstName : "") + (lastName != null ? " " + lastName : "");
                        
                        if (StrUtil.isNotEmpty(customerEmail)) {
                            // 通过email查找用户
                            try {
                                SysLoginUser user = sysUserService.getUserByEmail(customerEmail);
                                if (user != null) {
                                    customerId = user.getId();
                                    log.info("Customer映射: {} -> userId: {}", customerEmail, customerId);
                                } else {
                                    log.info("未找到邮箱 {} 对应的系统用户，将作为访客处理", customerEmail);
                                }
                            } catch (Exception e) {
                                log.warn("查找用户失败: {}", e.getMessage());
                            }
                        }
                        
                        // 设置customer信息
                        WordPressWebhookParam.CustomerInfo customerInfo = new WordPressWebhookParam.CustomerInfo();
                        customerInfo.setId(customerId); // 如果找到用户则为用户ID，否则为null
                        customerInfo.setName(customerName);
                        customerInfo.setEmail(customerEmail);
                        customerInfo.setPhone(customer.getStr("phone"));
                        webhookParam.setCustomer(customerInfo);
                    }
                }
                
                // 提取provider信息
                String providerEmail = null;
                String providerId = null;
                String providerName = null;
                String providerOrgId = null;
                if (appointment.containsKey("provider")) {
                    JSONObject provider = appointment.getJSONObject("provider");
                    providerEmail = provider.getStr("email");
                    String firstName = provider.getStr("firstName");
                    String lastName = provider.getStr("lastName");
                    providerName = (firstName != null ? firstName : "") + (lastName != null ? " " + lastName : "");
                    
                    if (StrUtil.isNotEmpty(providerEmail)) {
                        // 通过email查找系统用户
                        try {
                            SysLoginUser user = sysUserService.getUserByEmail(providerEmail);
                            if (user != null) {
                                providerId = user.getId();
                                providerOrgId = user.getOrgId();
                                log.info("Provider映射: {} -> userId: {}, orgId: {}", providerEmail, providerId, providerOrgId);
                            } else {
                                log.warn("未找到邮箱 {} 对应的系统用户", providerEmail);
                            }
                        } catch (Exception e) {
                            log.warn("查找Provider用户失败: {}", e.getMessage());
                        }
                    }
                    
                    WordPressWebhookParam.StaffInfo staffInfo = new WordPressWebhookParam.StaffInfo();
                    staffInfo.setId(providerId); // 使用系统用户ID而不是provider.id
                    staffInfo.setName(providerName);
                    staffInfo.setEmail(providerEmail);
                    if (StrUtil.isNotEmpty(providerOrgId)) {
                        staffInfo.setDepartmentId(providerOrgId);
                    }
                    webhookParam.setStaff(staffInfo);
                }
                
                // 如果没有通过provider找到orgId，则使用location映射的orgId
                if (StrUtil.isEmpty(providerOrgId) && StrUtil.isNotEmpty(orgId)) {
                    if (webhookParam.getStaff() != null) {
                        webhookParam.getStaff().setDepartmentId(orgId);
                    }
                }

                // 提取service信息
                if (appointment.containsKey("service")) {
                    JSONObject service = appointment.getJSONObject("service");
                    WordPressWebhookParam.ServiceInfo serviceInfo = new WordPressWebhookParam.ServiceInfo();
                    serviceInfo.setId(service.getStr("id"));
                    serviceInfo.setName(service.getStr("name"));
                    serviceInfo.setPrice(service.getDouble("price"));
                    
                    // 智能处理duration：如果大于100则认为是秒，否则认为是小时
                    Double duration = service.getDouble("duration");
                    if (duration != null) {
                        if (duration > 100) {
                            // 大于100，认为是秒数，转换为小时
                            serviceInfo.setDuration(duration / 3600.0);
                            log.info("Duration解析: {}秒 -> {}小时", duration, duration / 3600.0);
                        } else {
                            // 小于等于100，认为已经是小时
                            serviceInfo.setDuration(duration);
                            log.info("Duration解析: {}小时（无需转换）", duration);
                        }
                    }
                    serviceInfo.setQuantity(1);

                    List<WordPressWebhookParam.ServiceInfo> services = new ArrayList<>();
                    services.add(serviceInfo);
                    webhookParam.setServices(services);
                }

                // 提取时间信息
                if (appointment.containsKey("bookingStart") && appointment.containsKey("bookingEnd")) {
                    WordPressWebhookParam.AppointmentTime appointmentTime = new WordPressWebhookParam.AppointmentTime();
                    appointmentTime.setStartTime(appointment.getStr("bookingStart"));
                    appointmentTime.setEndTime(appointment.getStr("bookingEnd"));
                    appointmentTime.setTimezone("Asia/Shanghai");
                    webhookParam.setAppointmentTime(appointmentTime);
                }

                // 提取payment信息
                if (bookings != null && bookings.containsKey("0")) {
                    JSONObject firstBooking = bookings.getJSONObject("0");
                    WordPressWebhookParam.PaymentInfo paymentInfo = new WordPressWebhookParam.PaymentInfo();
                    paymentInfo.setTotalAmount(firstBooking.getDouble("price"));
                    paymentInfo.setCash(0.0);
                    paymentInfo.setCard(0.0);
                    paymentInfo.setPaymentStatus("pending");
                    webhookParam.setPayment(paymentInfo);
                }

                // 设置metadata，包含orgId
                Map<String, Object> metadata = new HashMap<>();
                if (StrUtil.isNotEmpty(orgId)) {
                    metadata.put("orgId", orgId);
                }
                metadata.put("locationName", locationName);
                metadata.put("originalFormat", "amelia-special");
                webhookParam.setMetadata(metadata);

                log.info("特殊Amelia格式解析完成，orgId: {}, userId: {}", orgId, customerId);

            } else if (AmeliaWebhookAdapter.isAmeliaFormat(requestBody)) {
                // 标准Amelia格式
                log.info("检测到标准Amelia格式的webhook，进行格式转换");
                AmeliaWebhookParam ameliaParam = JSONUtil.toBean(requestBody, AmeliaWebhookParam.class);
                webhookParam = AmeliaWebhookAdapter.convertToStandard(ameliaParam);

            } else {
                // 通用格式
                log.info("使用通用格式解析webhook");
                webhookParam = JSONUtil.toBean(jsonObject, WordPressWebhookParam.class);
            }

            // 处理任务创建
            String taskId = webhookService.createTaskFromWordPress(webhookParam);

            log.info("WordPress任务创建成功，任务ID: {}", taskId);
            return CommonResult.data(taskId).setMsg("任务创建成功");

        } catch (Exception e) {
            log.error("处理WordPress任务创建webhook失败", e);
            return CommonResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 接收WordPress的任务修改webhook
     *
     * @param webhookParam WordPress传来的数据
     * @return 处理结果
     */
    @Operation(summary = "接收WordPress任务修改webhook")
    @CommonLog("接收WordPress任务修改webhook")
    @PostMapping("/webhook/wordpress/task/update")
    public CommonResult<String> updateTask(@RequestBody WordPressWebhookParam webhookParam, HttpServletRequest request) {
        try {
            log.info("接收到WordPress任务修改webhook: {}", JSONUtil.toJsonStr(webhookParam));

            // 验证API Key（URL参数）
            if (!webhookService.verifyWebhookApiKey(request)) {
                log.warn("WordPress webhook API Key验证失败");
                return CommonResult.error("API Key验证失败");
            }

            // 验证webhook签名
            if (!webhookService.verifyWebhookSignature(request, webhookParam)) {
                log.warn("WordPress webhook签名验证失败");
                return CommonResult.error("Webhook签名验证失败");
            }

            // 处理任务修改
            String taskId = webhookService.updateTaskFromWordPress(webhookParam);

            log.info("WordPress任务修改成功，任务ID: {}", taskId);
            return CommonResult.data(taskId).setMsg("任务修改成功");

        } catch (Exception e) {
            log.error("处理WordPress任务修改webhook失败", e);
            return CommonResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 接收WordPress的任务取消webhook
     *
     * @param webhookParam WordPress传来的数据
     * @return 处理结果
     */
    @Operation(summary = "接收WordPress任务取消webhook")
    @CommonLog("接收WordPress任务取消webhook")
    @PostMapping("/webhook/wordpress/task/cancel")
    public CommonResult<String> cancelTask(@RequestBody WordPressWebhookParam webhookParam, HttpServletRequest request) {
        try {
            log.info("接收到WordPress任务取消webhook: {}", JSONUtil.toJsonStr(webhookParam));

            // 验证API Key（URL参数）
            if (!webhookService.verifyWebhookApiKey(request)) {
                log.warn("WordPress webhook API Key验证失败");
                return CommonResult.error("API Key验证失败");
            }

            // 验证webhook签名
            if (!webhookService.verifyWebhookSignature(request, webhookParam)) {
                log.warn("WordPress webhook签名验证失败");
                return CommonResult.error("Webhook签名验证失败");
            }

            // 处理任务取消
            String result = webhookService.cancelTaskFromWordPress(webhookParam);

            log.info("WordPress任务取消成功: {}", result);
            return CommonResult.ok("任务取消成功");

        } catch (Exception e) {
            log.error("处理WordPress任务取消webhook失败", e);
            return CommonResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 通用WordPress webhook接收接口
     * 根据action参数自动路由到对应的处理方法
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    @Operation(summary = "通用WordPress webhook接收接口")
    @CommonLog("接收WordPress通用webhook")
    @PostMapping("/webhook/wordpress/task")
    public CommonResult<String> handleWordPressWebhook(@RequestBody String requestBody, HttpServletRequest request) {
        try {
            log.info("接收到WordPress通用webhook: {}", requestBody);

            // 验证API Key（URL参数）
            if (!webhookService.verifyWebhookApiKey(request)) {
                log.warn("WordPress webhook API Key验证失败");
                return CommonResult.error("API Key验证失败");
            }

            WordPressWebhookParam webhookParam;

            // 检测是否为Amelia格式的webhook
            if (AmeliaWebhookAdapter.isAmeliaFormat(requestBody)) {
                log.info("检测到Amelia格式的webhook，进行格式转换");

                // 解析Amelia格式
                AmeliaWebhookParam ameliaParam = JSONUtil.toBean(requestBody, AmeliaWebhookParam.class);

                // 转换为通用格式
                webhookParam = AmeliaWebhookAdapter.convertToStandard(ameliaParam);

                log.info("Amelia格式转换完成，转换后的参数: {}", JSONUtil.toJsonStr(webhookParam));
            } else {
                log.info("使用通用格式解析webhook");

                // 解析通用格式
                JSONObject jsonObject = JSONUtil.parseObj(requestBody);
                webhookParam = JSONUtil.toBean(jsonObject, WordPressWebhookParam.class);
            }

            // 验证webhook签名（可选，因为已有API Key验证）
            // if (!webhookService.verifyWebhookSignature(request, webhookParam)) {
            //     log.warn("WordPress webhook签名验证失败");
            //     return CommonResult.error("Webhook签名验证失败");
            // }

            // 根据action参数路由到对应的处理方法
            String action = webhookParam.getAction();
            if (StrUtil.isEmpty(action)) {
                return CommonResult.error("缺少action参数");
            }

            switch (action.toLowerCase()) {
                case "create":
                case "book":
                case "appointment_created":
                    String taskId = webhookService.createTaskFromWordPress(webhookParam);
                    return CommonResult.data(taskId).setMsg("任务创建成功");

                case "update":
                case "modify":
                case "appointment_updated":
                    String updatedTaskId = webhookService.updateTaskFromWordPress(webhookParam);
                    return CommonResult.data(updatedTaskId).setMsg("任务修改成功");

                case "cancel":
                case "delete":
                case "appointment_cancelled":
                    String result = webhookService.cancelTaskFromWordPress(webhookParam);
                    return CommonResult.ok("任务取消成功");

                default:
                    log.warn("未知的webhook action: {}", action);
                    return CommonResult.error("未知的action: " + action);
            }

        } catch (Exception e) {
            log.error("处理WordPress通用webhook失败", e);
            return CommonResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口 - 验证webhook连接
     *
     * @return 测试结果
     */
    @Operation(summary = "测试WordPress webhook连接")
    @GetMapping("/webhook/wordpress/test")
    public CommonResult<String> testWebhook(HttpServletRequest request) {
        log.info("收到WordPress webhook测试请求");

        // 验证API Key（URL参数）
        if (!webhookService.verifyWebhookApiKey(request)) {
            log.warn("WordPress webhook API Key验证失败");
            return CommonResult.error("API Key验证失败");
        }

        return CommonResult.ok("WordPress webhook连接正常");
    }

    /**
     * 获取任务状态 - 提供给WordPress查询
     *
     * @param taskId 任务ID或WordPress预约ID
     * @return 任务状态
     */
    @Operation(summary = "获取任务状态")
    @GetMapping("/webhook/wordpress/task/status")
    public CommonResult<JSONObject> getTaskStatus(@RequestParam String taskId, HttpServletRequest request) {
        try {
            // 验证API Key（URL参数）
            if (!webhookService.verifyWebhookApiKey(request)) {
                log.warn("WordPress webhook API Key验证失败");
                return CommonResult.error("API Key验证失败");
            }

            // 尝试通过任务ID查找
            BizTask task = null;
            try {
                task = bizTaskService.getById(taskId);
            } catch (Exception e) {
                // 如果直接查找失败，尝试通过WordPress预约ID查找
                task = webhookService.findTaskByWordPressId(taskId);
            }

            if (task == null) {
                return CommonResult.error("任务不存在");
            }

            JSONObject result = new JSONObject();
            result.set("taskId", task.getId());
            result.set("status", task.getState());
            result.set("itemName", task.getItemName());
            result.set("startTime", task.getStartTime());
            result.set("assign", task.getAssign());
            result.set("staff", task.getStaff());

            return CommonResult.data(result);

        } catch (Exception e) {
            log.error("获取任务状态失败", e);
            return CommonResult.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * WordPress Webhook测试接口 - 打印原始数据
     * 用于调试和查看实际传入的数据格式
     */
    @PostMapping("/test/raw")
    public CommonResult<Map<String, Object>> testWebhookRaw(HttpServletRequest request, @RequestBody String rawBody) {
        try {
            // API Key验证
            if (!webhookService.verifyWebhookApiKey(request)) {
                return CommonResult.error("API Key验证失败");
            }

            log.info("=== WordPress Webhook 原始数据测试 ===");
            log.info("请求时间: {}", LocalDateTime.now());
            log.info("请求方法: {}", request.getMethod());
            log.info("请求URL: {}", request.getRequestURL());
            log.info("Content-Type: {}", request.getContentType());

            // 打印所有请求头
            log.info("=== 请求头信息 ===");
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                log.info("Header: {} = {}", headerName, headerValue);
            }

            // 打印原始请求体
            log.info("=== 原始请求体 ===");
            log.info("Raw Body: {}", rawBody);

                         // 尝试解析为JSON并美化输出
             try {
                 Object jsonObject = JSONUtil.parseObj(rawBody);
                 String prettyJson = JSONUtil.toJsonPrettyStr(jsonObject);
                 log.info("=== 美化的JSON ===");
                 log.info("{}", prettyJson);
             } catch (Exception e) {
                 log.warn("无法解析为JSON: {}", e.getMessage());
             }

            // 返回接收到的数据
            Map<String, Object> response = new HashMap<>();
            response.put("timestamp", LocalDateTime.now());
            response.put("received", true);
            response.put("rawBody", rawBody);
            response.put("contentType", request.getContentType());

                         return CommonResult.data(response).setMsg("测试数据已接收并打印到日志");

        } catch (Exception e) {
            log.error("测试接口处理失败", e);
            return CommonResult.error("测试接口处理失败: " + e.getMessage());
        }
    }

    /**
     * Amelia专用测试接口
     */
    @PostMapping("/test/amelia")
    public CommonResult<Map<String, Object>> testAmeliaWebhook(HttpServletRequest request, @RequestBody String rawBody) {
        try {
            // API Key验证
            if (!webhookService.verifyWebhookApiKey(request)) {
                return CommonResult.error("API Key验证失败");
            }

            log.info("=== Amelia Webhook 专用测试 ===");
            log.info("请求时间: {}", LocalDateTime.now());

                         // 尝试解析为Amelia格式
             try {
                 AmeliaWebhookParam ameliaData = JSONUtil.toBean(rawBody, AmeliaWebhookParam.class);
                 log.info("=== Amelia数据解析成功 ===");
                 log.info("Message: {}", ameliaData.getMessage());
                 if (ameliaData.getData() != null) {
                     log.info("Type: {}", ameliaData.getData().getType());
                     if (ameliaData.getData().getAppointment() != null) {
                         log.info("Appointment ID: {}", ameliaData.getData().getAppointment().getId());
                         log.info("Service ID: {}", ameliaData.getData().getAppointment().getServiceId());
                         log.info("Provider ID: {}", ameliaData.getData().getAppointment().getProviderId());
                         log.info("Booking Start: {}", ameliaData.getData().getAppointment().getBookingStart());
                         log.info("Booking End: {}", ameliaData.getData().getAppointment().getBookingEnd());
                     }
                 }

                 // 测试适配器转换
                 if (AmeliaWebhookAdapter.isAmeliaFormat(rawBody)) {
                     WordPressWebhookParam converted = AmeliaWebhookAdapter.convertToStandard(ameliaData);
                     log.info("=== 适配器转换结果 ===");
                     log.info("转换后的操作类型: {}", converted.getAction());
                     if (converted.getCustomer() != null) {
                         log.info("转换后的客户姓名: {}", converted.getCustomer().getName());
                     }
                 } else {
                     log.warn("数据格式不是标准Amelia格式");
                 }

             } catch (Exception e) {
                 log.error("Amelia数据解析失败: {}", e.getMessage());
                 log.info("原始数据: {}", rawBody);
             }

            Map<String, Object> response = new HashMap<>();
            response.put("timestamp", LocalDateTime.now());
            response.put("received", true);
            response.put("plugin", "Amelia");
            response.put("rawBody", rawBody);

                         return CommonResult.data(response).setMsg("Amelia测试数据已处理");

        } catch (Exception e) {
            log.error("Amelia测试接口处理失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }

    /**
     * 请求头测试接口
     */
    @PostMapping("/test/headers")
    public CommonResult<Map<String, Object>> testWebhookHeaders(HttpServletRequest request) {
        try {
            // API Key验证
            if (!webhookService.verifyWebhookApiKey(request)) {
                return CommonResult.error("API Key验证失败");
            }

            log.info("=== Webhook 请求头测试 ===");

            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                headers.put(headerName, headerValue);
                log.info("Header: {} = {}", headerName, headerValue);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("timestamp", LocalDateTime.now());
            response.put("headers", headers);
            response.put("method", request.getMethod());
            response.put("url", request.getRequestURL().toString());
            response.put("remoteAddr", request.getRemoteAddr());

                         return CommonResult.data(response).setMsg("请求头信息已记录");

        } catch (Exception e) {
            log.error("请求头测试失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }
}
