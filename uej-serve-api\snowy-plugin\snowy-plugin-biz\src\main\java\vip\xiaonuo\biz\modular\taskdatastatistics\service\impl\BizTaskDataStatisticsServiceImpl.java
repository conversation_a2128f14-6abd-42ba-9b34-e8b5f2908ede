package vip.xiaonuo.biz.modular.taskdatastatistics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.taskdatastatistics.entity.BizTaskDataStatistics;
import vip.xiaonuo.biz.modular.taskdatastatistics.mapper.BizTaskDataStatisticsMapper;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsAddParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsEditParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsIdParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsPageParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.service.BizTaskDataStatisticsService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 任务数据统计Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/13 15:48
 **/
@Service
public class BizTaskDataStatisticsServiceImpl extends ServiceImpl<BizTaskDataStatisticsMapper, BizTaskDataStatistics> implements BizTaskDataStatisticsService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizTaskDataStatistics> page(BizTaskDataStatisticsPageParam bizTaskDataStatisticsPageParam) {
        QueryWrapper<BizTaskDataStatistics> queryWrapper = new QueryWrapper<BizTaskDataStatistics>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizTaskDataStatisticsPageParam.getStatus())) {
            queryWrapper.lambda().eq(BizTaskDataStatistics::getStatus, bizTaskDataStatisticsPageParam.getStatus());
        }
        if(ObjectUtil.isAllNotEmpty(bizTaskDataStatisticsPageParam.getSortField(), bizTaskDataStatisticsPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizTaskDataStatisticsPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizTaskDataStatisticsPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizTaskDataStatisticsPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizTaskDataStatistics::getId);
        }
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizTaskDataStatistics::getOrgId, loginUserDataScope);
        } else {
            // 无权限时只能查看自己创建的任务统计
            queryWrapper.lambda().eq(BizTaskDataStatistics::getCreateUser, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizTaskDataStatisticsAddParam bizTaskDataStatisticsAddParam) {
        // 任务统计添加不需要特殊的权限校验，因为任何用户都可以添加任务统计
        BizTaskDataStatistics bizTaskDataStatistics = BeanUtil.toBean(bizTaskDataStatisticsAddParam, BizTaskDataStatistics.class);
        this.save(bizTaskDataStatistics);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizTaskDataStatisticsEditParam bizTaskDataStatisticsEditParam) {
        BizTaskDataStatistics bizTaskDataStatistics = this.queryEntity(bizTaskDataStatisticsEditParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizTaskDataStatistics.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的任务统计，机构id：{}", bizTaskDataStatistics.getOrgId());
            }
        } else {
            if(!bizTaskDataStatistics.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该任务统计，任务名称：{}", bizTaskDataStatistics.getTaskName());
            }
        }
        
        BeanUtil.copyProperties(bizTaskDataStatisticsEditParam, bizTaskDataStatistics);
        this.updateById(bizTaskDataStatistics);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizTaskDataStatisticsIdParam> bizTaskDataStatisticsIdParamList) {
        List<String> taskDataStatisticsIdList = CollStreamUtil.toList(bizTaskDataStatisticsIdParamList, BizTaskDataStatisticsIdParam::getId);
        if(ObjectUtil.isNotEmpty(taskDataStatisticsIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            List<BizTaskDataStatistics> taskDataStatisticsList = this.listByIds(taskDataStatisticsIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 检查机构权限
                for(BizTaskDataStatistics taskDataStatistics : taskDataStatisticsList) {
                    if(!loginUserDataScope.contains(taskDataStatistics.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的任务统计，机构id：{}", taskDataStatistics.getOrgId());
                    }
                }
            } else {
                // 检查创建者权限
                for(BizTaskDataStatistics taskDataStatistics : taskDataStatisticsList) {
                    if(!taskDataStatistics.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该任务统计，任务名称：{}", taskDataStatistics.getTaskName());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(taskDataStatisticsIdList);
    }

    @Override
    public BizTaskDataStatistics detail(BizTaskDataStatisticsIdParam bizTaskDataStatisticsIdParam) {
        BizTaskDataStatistics bizTaskDataStatistics = this.queryEntity(bizTaskDataStatisticsIdParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizTaskDataStatistics.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的任务统计，机构id：{}", bizTaskDataStatistics.getOrgId());
            }
        } else {
            if(!bizTaskDataStatistics.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该任务统计，任务名称：{}", bizTaskDataStatistics.getTaskName());
            }
        }
        
        return bizTaskDataStatistics;
    }

    @Override
    public BizTaskDataStatistics queryEntity(String id) {
        BizTaskDataStatistics bizTaskDataStatistics = this.getById(id);
        if(ObjectUtil.isEmpty(bizTaskDataStatistics)) {
            throw new CommonException("任务数据统计不存在，id值为：{}", id);
        }
        return bizTaskDataStatistics;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizTaskDataStatisticsServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizTaskDataStatistics.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}
