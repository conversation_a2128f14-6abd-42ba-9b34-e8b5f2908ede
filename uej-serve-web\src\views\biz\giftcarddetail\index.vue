<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :span="6">
					<a-form-item label="Card Number" name="cardNumber">
						<a-input v-model:value="searchFormState.cardNumber" placeholder="Please enter Card Number" @pressEnter="tableRef.refresh(true)"/>
					</a-form-item>
				</a-col>
				<a-col :span="6">
					<a-form-item label="If Avtived" name="actived">
						<a-select v-model:value="searchFormState.actived" placeholder="Please select Avtived">
							<a-select-option value="0">Not Actived</a-select-option>
							<a-select-option value="1">Actived</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="6">
					<a-button type="primary" @click="tableRef.refresh(true)">{{ $t('common.searchButton') }}</a-button>
					<a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
				</a-col>
			</a-row>
		</a-form>
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
			:scroll="{x:'max-content'}"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-button
						type="primary"
						@click="()=>{
							formRef.onOpen(),
							formRef.setMainId({ mainId: props.mainId })
						}"
						v-if="hasPerm('bizGiftCardDetailAdd')"
					>
						<template #icon><plus-outlined /></template>
						{{ $t('common.addButton') }}
					</a-button>
					<xn-batch-delete
						v-if="hasPerm('bizGiftCardDetailBatchDelete')"
						:selectedRowKeys="selectedRowKeys"
						@batchDelete="deleteBatchBizGiftCardDetail"
						:buttonName="$t('common.batchRemoveButton')"
					/>
					<a-button @click="batchCallBack">
						{{ $t('common.batchButton') }}
					</a-button>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'actived'">
					<span>{{ record.actived=='1' ? 'Actived' : 'Not Actived' }}</span>
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a @click="formRef.onOpen(record)" v-if="hasPerm('bizGiftCardDetailEdit')">{{ $t('common.editButton') }}</a>
						<a-divider type="vertical" v-if="hasPerm(['bizGiftCardDetailEdit', 'bizGiftCardDetailChangeStatus'], 'or')" />
						<a-dropdown v-if="hasPerm('bizGiftCardDetailChangeStatus')">
							<template #overlay>
								<a-menu>
									<a-menu-item v-if="record.actived !== '0'" @click="changeActivationStatus(record, '0')">
										<span style="color: #ff4d4f;">Deactivate</span>
									</a-menu-item>
								</a-menu>
							</template>
							<a-button type="link" size="small">
								Status <DownOutlined />
							</a-button>
						</a-dropdown>
						<a-divider type="vertical" v-if="hasPerm(['bizGiftCardDetailEdit', 'bizGiftCardDetailDelete'], 'and')" />
						<a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizGiftCardDetail(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('bizGiftCardDetailDelete')">{{
								$t('common.removeButton')
							}}</a-button>
						</a-popconfirm>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh(true)" />
</template>

<script setup name="giftcarddetail">
	import { cloneDeep } from 'lodash-es'
	import { DownOutlined } from '@ant-design/icons-vue'
	import { message } from 'ant-design-vue'
	import Form from './form.vue'
	import bizGiftCardDetailApi from '@/api/biz/bizGiftCardDetailApi'
	const props = defineProps({
		mainId: {
			type: String,
			default: null
		}
	})
	const searchFormState = ref({})
	const searchFormRef = ref()
	const tableRef = ref()
	const formRef = ref()
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	const columns = [
		// {
		// 	title: 'gift_card_id',
		// 	dataIndex: 'mainId'
		// },
		{
			title: 'Card Number',
			dataIndex: 'cardNumber'
		},
		{
			title: 'Pin',
			dataIndex: 'pin'
		},
		{
			title: 'Create Date',
			dataIndex: 'createDate'
		},
		{
			title: 'Active Date',
			dataIndex: 'activeDate'
		},
		{
			title: 'Exp Date',
			dataIndex: 'expDate'
		},
		{
			title: 'Used Date',
			dataIndex: 'usedDate'
		},
		{
			title: 'Value',
			dataIndex: 'value'
		},
		{
			title: 'Rest Value',
			dataIndex: 'restValue'
		},
		{
			title:'Shop Name',
			dataIndex:'orgName'
		},
		{
			title: 'If Avtived',
			dataIndex: 'actived'
		}
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['bizGiftCardDetailEdit', 'bizGiftCardDetailDelete', 'bizGiftCardDetailChangeStatus'])) {
		columns.push({
			title: 'action',
			dataIndex: 'action',
			align: 'center',
			
			fixed: 'right'
		})
	}

	watch(
		props,
		(newVal) => {
			if (newVal) {
				searchFormState.value.mainId = newVal.mainId
			}
		},
		{
			immediate: true,
			deep: true
		}
	)

	const selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		// columns数字类型字段加入 needTotal: true 可以勾选自动算账
		alert: {
			show: true,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		const searchFormParam = cloneDeep(searchFormState.value)
		return bizGiftCardDetailApi.bizGiftCardDetailPage(Object.assign(parameter, searchFormParam)).then((data) => {
			return data
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 删除
	const deleteBizGiftCardDetail = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		bizGiftCardDetailApi.bizGiftCardDetailDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchBizGiftCardDetail = (params) => {
		bizGiftCardDetailApi.bizGiftCardDetailDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}

	const batchCallBack=()=>{
		formRef.value.onOpen()
		formRef.value.setMainId({mainId: props.mainId })
		formRef.value.batch()
	}

	// 修改激活状态
	const changeActivationStatus = (record, status) => {
		const statusText = status === '1' ? 'Activate' : 'Deactivate'
		const params = {
			id: record.id,
			actived: status
		}
		bizGiftCardDetailApi.bizGiftCardDetailChangeStatus(params).then(() => {
			message.success(`${statusText} successfully`)
			tableRef.value.refresh(true)
		}).catch((error) => {
			message.error(`${statusText} failed: ${error.message || error}`)
		})
	}

const refresh = () => {
	tableRef.value.refresh(true)
}

	defineExpose({
		refresh
	})
</script>
