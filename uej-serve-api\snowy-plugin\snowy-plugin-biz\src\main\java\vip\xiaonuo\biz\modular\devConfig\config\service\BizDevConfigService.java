
package vip.xiaonuo.biz.modular.devConfig.config.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.devConfig.config.entity.BizDevConfig;
import vip.xiaonuo.biz.modular.devConfig.config.param.*;

import java.util.List;

/**
 * 配置Service接口
 *
 * <AUTHOR>
 * @date 2022/4/22 10:41
 **/
public interface BizDevConfigService extends IService<BizDevConfig> {

    /**
     * 根据键获取值
     *
     * <AUTHOR>
     * @date 2022/4/22 14:52
     **/
    String getValueByKey(String key);

    /**
     * 获取配置分页
     *
     * <AUTHOR>
     * @date 2022/4/24 20:08
     */
    Page<BizDevConfig> page(DevConfigPageParam devConfigPageParam);

    /**
     * 获取基础配置列表
     *
     * <AUTHOR>
     * @date 2022/4/24 20:08
     */
    List<BizDevConfig> sysBaseList();

    /**
     * 获取配置列表
     *
     * <AUTHOR>
     * @date 2022/4/24 20:08
     */
    List<BizDevConfig> list(DevConfigListParam devConfigListParam);

    /**
     * 添加配置
     *
     * <AUTHOR>
     * @date 2022/4/24 20:48
     */
    void add(DevConfigAddParam devConfigAddParam);

    /**
     * 编辑配置
     *
     * <AUTHOR>
     * @date 2022/4/24 21:13
     */
    void edit(DevConfigEditParam devConfigEditParam);

    /**
     * 删除配置
     *
     * <AUTHOR>
     * @date 2022/4/24 21:18
     */
    void delete(List<DevConfigIdParam> devConfigIdParamList);

    /**
     * 获取配置详情
     *
     * <AUTHOR>
     * @date 2022/4/24 21:18
     */
    BizDevConfig detail(DevConfigIdParam devConfigIdParam);

    /**
     * 获取配置详情
     *
     * <AUTHOR>
     * @date 2022/4/24 21:18
     */
    BizDevConfig queryEntity(String id);

    /**
     * 配置批量更新
     *
     * <AUTHOR>
     * @date 2022/6/28 11:09
     **/
    void editBatch(List<DevConfigBatchParam> devConfigBatchParamList);
}
