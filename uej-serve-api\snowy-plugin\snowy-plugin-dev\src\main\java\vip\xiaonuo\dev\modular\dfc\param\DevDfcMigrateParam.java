package vip.xiaonuo.dev.modular.dfc.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 迁移数据参数
 *
 * <AUTHOR>
 * @date 2023/8/7 22:59
 */
@Getter
@Setter
public class DevDfcMigrateParam {
    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;

    /** 数据源Id */
    @Schema(description = "数据源Id")
    @NotBlank(message = "dbsId不能为空")
    private String dbsId;

    /** 表名称 */
    @Schema(description = "表名称")
    @NotBlank(message = "tableName不能为空")
    private String tableName;

    /** 动态字段存储数据的字段 */
    @Schema(description = "动态字段存储数据的字段")
    @NotBlank(message = "dfDataStoreColumnName不能为空")
    private String dfDataStoreColumnName;

    /** 字段名称 */
    @Schema(description = "请选择迁移目标字段")
    @NotBlank(message = "migrateTargetColumnName不能为空")
    private String migrateTargetColumnName;

}
