
package vip.xiaonuo.biz.modular.config.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.config.entity.BizConfig;
import vip.xiaonuo.biz.modular.config.param.BizConfigAddParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigEditParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigIdParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 业务配置Service接口
 *
 * <AUTHOR>
 * @date  2024/07/08 19:09
 **/
public interface BizConfigService extends IService<BizConfig> {

    /**
     * 获取业务配置分页
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    Page<BizConfig> page(BizConfigPageParam bizConfigPageParam);

    /**
     * 添加业务配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    void add(BizConfigAddParam bizConfigAddParam);

    /**
     * 编辑业务配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    void edit(BizConfigEditParam bizConfigEditParam);

    /**
     * 删除业务配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    void delete(List<BizConfigIdParam> bizConfigIdParamList);

    /**
     * 获取业务配置详情
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    BizConfig detail(BizConfigIdParam bizConfigIdParam);

    /**
     * 获取业务配置详情
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     **/
    BizConfig queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
