package vip.xiaonuo.biz.modular.config.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.config.entity.BizConfig;
import vip.xiaonuo.biz.modular.config.mapper.BizConfigMapper;
import vip.xiaonuo.biz.modular.config.param.BizConfigAddParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigEditParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigIdParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigPageParam;
import vip.xiaonuo.biz.modular.config.service.BizConfigService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 业务配置Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/07/08 19:09
 **/
@Service
public class BizConfigServiceImpl extends ServiceImpl<BizConfigMapper, BizConfig> implements BizConfigService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizConfig> page(BizConfigPageParam bizConfigPageParam) {
        QueryWrapper<BizConfig> queryWrapper = new QueryWrapper<BizConfig>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizConfigPageParam.getParentBizKey())) {
            queryWrapper.lambda().eq(BizConfig::getParentBizKey, bizConfigPageParam.getParentBizKey());
        }
        if(ObjectUtil.isNotEmpty(bizConfigPageParam.getBizKey())) {
            queryWrapper.lambda().eq(BizConfig::getBizKey, bizConfigPageParam.getBizKey());
        }
        if(ObjectUtil.isAllNotEmpty(bizConfigPageParam.getSortField(), bizConfigPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizConfigPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizConfigPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizConfigPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizConfig::getId);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizConfig::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizConfig::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizConfig::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizConfigAddParam bizConfigAddParam) {
        BizConfig bizConfig = BeanUtil.toBean(bizConfigAddParam, BizConfig.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizConfig.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizConfig.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizConfigEditParam bizConfigEditParam) {
        BizConfig bizConfig = this.queryEntity(bizConfigEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查配置所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizConfig.getOrgId()) && !loginUserDataScope.contains(bizConfig.getOrgId())) {
                throw new CommonException("您没有权限编辑该配置，配置id：{}", bizConfig.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizConfig.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该配置，配置id：{}", bizConfig.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该配置，配置id：{}", bizConfig.getId());
            }
        }
        
        BeanUtil.copyProperties(bizConfigEditParam, bizConfig);
        this.updateById(bizConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizConfigIdParam> bizConfigIdParamList) {
        List<String> configIdList = CollStreamUtil.toList(bizConfigIdParamList, BizConfigIdParam::getId);
        if(ObjectUtil.isNotEmpty(configIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizConfig> configList = this.listByIds(configIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查配置所属机构是否在权限范围内
                for(BizConfig config : configList) {
                    if(ObjectUtil.isNotEmpty(config.getOrgId()) && !loginUserDataScope.contains(config.getOrgId())) {
                        throw new CommonException("您没有权限删除该配置，配置id：{}", config.getId());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizConfig config : configList) {
                        if(!config.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该配置，配置id：{}", config.getId());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除配置");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(configIdList);
    }

    @Override
    public BizConfig detail(BizConfigIdParam bizConfigIdParam) {
        BizConfig bizConfig = this.queryEntity(bizConfigIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查配置所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizConfig.getOrgId()) && !loginUserDataScope.contains(bizConfig.getOrgId())) {
                throw new CommonException("您没有权限查看该配置，配置id：{}", bizConfig.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizConfig.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该配置，配置id：{}", bizConfig.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该配置，配置id：{}", bizConfig.getId());
            }
        }
        
        return bizConfig;
    }

    @Override
    public BizConfig queryEntity(String id) {
        BizConfig bizConfig = this.getById(id);
        if(ObjectUtil.isEmpty(bizConfig)) {
            throw new CommonException("业务配置不存在，id值为：{}", id);
        }
        return bizConfig;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizConfigServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizConfig.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}

