
package vip.xiaonuo.flw.modular.process.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 草稿Id参数
 *
 * <AUTHOR>
 * @date 2022/8/1 10:32
 */
@Getter
@Setter
public class FlwProcessDraftIdParam {

    /** 草稿Id */
    @Schema(description = "草稿Id")
    @NotBlank(message = "id不能为空")
    private String id;
}
