package vip.xiaonuo.biz.modular.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.mapper.BizTaskMapper;
import vip.xiaonuo.biz.modular.task.param.BizTaskAddParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskEditParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskIdParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskPageParam;
import vip.xiaonuo.biz.modular.task.service.BizTaskService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;

import java.util.List;

/**
 * 任务信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 15:42
 **/
@Service
public class BizTaskServiceImpl extends ServiceImpl<BizTaskMapper, BizTask> implements BizTaskService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizTask> page(BizTaskPageParam bizTaskPageParam) {
        QueryWrapper<BizTask> queryWrapper = new QueryWrapper<BizTask>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizTaskPageParam.getItemName())) {
            queryWrapper.lambda().like(BizTask::getItemName, bizTaskPageParam.getItemName());
        }
        if(ObjectUtil.isNotEmpty(bizTaskPageParam.getCreator())) {
            queryWrapper.lambda().like(BizTask::getCreator, bizTaskPageParam.getCreator());
        }
        if(ObjectUtil.isAllNotEmpty(bizTaskPageParam.getSortField(), bizTaskPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizTaskPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizTaskPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizTaskPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BizTask::getCreateTime);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizTask::getOrgId, loginUserDataScope);
        } else {
            // 如果没有数据范围权限，只能查看自己创建或分配给自己的任务
            queryWrapper.lambda().and(wrapper -> wrapper
                .eq(BizTask::getCreator, StpLoginUserUtil.getLoginUser().getId())
                .or()
                .eq(BizTask::getAssign, StpLoginUserUtil.getLoginUser().getId())
            );
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizTaskAddParam bizTaskAddParam) {
        // 校验数据范围 - 由于AddParam没有orgId字段，主要基于assign字段进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isEmpty(loginUserDataScope)) {
            // 如果没有数据范围权限，只能创建分配给自己的任务
            if(ObjectUtil.isNotEmpty(bizTaskAddParam.getAssign()) && !bizTaskAddParam.getAssign().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限创建分配给其他人的任务，分配人员id：{}", bizTaskAddParam.getAssign());
            }
        }
        // 注意：如果有数据范围权限，在创建任务时会自动设置orgId为当前用户所属机构
        
        BizTask bizTask = BeanUtil.toBean(bizTaskAddParam, BizTask.class);
        this.save(bizTask);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizTaskEditParam bizTaskEditParam) {
        BizTask bizTask = this.queryEntity(bizTaskEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizTask.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的任务，机构id：{}", bizTask.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能编辑自己创建或分配给自己的任务
            if(!bizTask.getCreator().equals(StpLoginUserUtil.getLoginUser().getId()) && 
               !bizTask.getAssign().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该任务，任务id：{}", bizTask.getId());
            }
        }
        
        BeanUtil.copyProperties(bizTaskEditParam, bizTask);
        this.updateById(bizTask);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizTaskIdParam> bizTaskIdParamList) {
        List<String> taskIdList = CollStreamUtil.toList(bizTaskIdParamList, BizTaskIdParam::getId);
        if(ObjectUtil.isNotEmpty(taskIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            List<BizTask> taskList = this.listByIds(taskIdList);
            
            for(BizTask task : taskList) {
                if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                    if(!loginUserDataScope.contains(task.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的任务，机构id：{}", task.getOrgId());
                    }
                } else {
                    // 如果没有数据范围权限，只能删除自己创建的任务
                    if(!task.getCreator().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该任务，任务id：{}", task.getId());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(taskIdList);
    }

    @Override
    public BizTask detail(BizTaskIdParam bizTaskIdParam) {
        BizTask bizTask = this.queryEntity(bizTaskIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizTask.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的任务，机构id：{}", bizTask.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能查看自己创建或分配给自己的任务
            if(!bizTask.getCreator().equals(StpLoginUserUtil.getLoginUser().getId()) && 
               !bizTask.getAssign().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该任务，任务id：{}", bizTask.getId());
            }
        }
        
        return bizTask;
    }

    @Override
    public BizTask queryEntity(String id) {
        BizTask bizTask = this.getById(id);
        if(ObjectUtil.isEmpty(bizTask)) {
            throw new CommonException("任务信息不存在，id值为：{}", id);
        }
        return bizTask;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizTaskServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizTask.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}
