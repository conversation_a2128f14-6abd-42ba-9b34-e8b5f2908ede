
package vip.xiaonuo.biz.modular.taskitem.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 任务子表Id参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:34
 **/
@Getter
@Setter
public class BizTaskItemIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;
}
