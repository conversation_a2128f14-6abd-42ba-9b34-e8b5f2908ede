
package vip.xiaonuo.biz.modular.scheduledate.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 排班表当日属性添加参数
 *
 * <AUTHOR>
 * @date  2024/06/18 16:34
 **/
@Getter
@Setter
public class BizScheduleDateAddParam {

    /** Schedule Date */
    @Schema(description = "Schedule Date")
    private String scheduleDate;

    private String openTime;

    private String organizationId;

    /** Holiday */
    @Schema(description = "Holiday")
    private String holiday;

    /** Num */
    @Schema(description = "Num")
    private String num;

    /** Remark */
    @Schema(description = "Remark")
    private String remark;

    private String extJson;
}
