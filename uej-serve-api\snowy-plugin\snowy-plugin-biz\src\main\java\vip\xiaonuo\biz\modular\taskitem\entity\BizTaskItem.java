
package vip.xiaonuo.biz.modular.taskitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 任务子表实体
 *
 * <AUTHOR>
 * @date  2024/06/12 17:34
 **/
@Getter
@Setter
@TableName("biz_task_item")
public class BizTaskItem {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** 主表id */
    @Schema(description = "主表id")
    private String mainId;

    /** Product Id */
    @Schema(description = "Product Id")
    private String productId;

    /** Product Name */
    @Schema(description = "Product Name")
    private String productName;

    private String type;

    /** Planning Hours */
    @Schema(description = "Planning Hours")
    private String planningHours;

    /** List Price */
    @Schema(description = "List Price")
    private String listPrice;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String extJson;

    private String staffId;
    private String staffName;
    private String orgId;
}
