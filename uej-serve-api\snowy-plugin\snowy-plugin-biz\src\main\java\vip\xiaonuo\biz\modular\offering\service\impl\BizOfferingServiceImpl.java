package vip.xiaonuo.biz.modular.offering.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.offering.entity.BizOffering;
import vip.xiaonuo.biz.modular.offering.mapper.BizOfferingMapper;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingAddParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingEditParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingIdParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingPageParam;
import vip.xiaonuo.biz.modular.offering.service.BizOfferingService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;

import java.util.List;

/**
 * 服务或产品清单Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 16:09
 **/
@Service
public class BizOfferingServiceImpl extends ServiceImpl<BizOfferingMapper, BizOffering> implements BizOfferingService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizOffering> page(BizOfferingPageParam bizOfferingPageParam) {
        QueryWrapper<BizOffering> queryWrapper = new QueryWrapper<BizOffering>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizOfferingPageParam.getCategory())) {
            queryWrapper.lambda().like(BizOffering::getCategory, bizOfferingPageParam.getCategory());
        }
        if(ObjectUtil.isNotEmpty(bizOfferingPageParam.getName())) {
            queryWrapper.lambda().like(BizOffering::getName, bizOfferingPageParam.getName());
        }
        if(ObjectUtil.isAllNotEmpty(bizOfferingPageParam.getSortField(), bizOfferingPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizOfferingPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizOfferingPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizOfferingPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizOffering::getId);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizOffering::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizOffering::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizOffering::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizOfferingAddParam bizOfferingAddParam) {
        BizOffering bizOffering = BeanUtil.toBean(bizOfferingAddParam, BizOffering.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizOffering.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizOffering.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizOffering);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizOfferingEditParam bizOfferingEditParam) {
        BizOffering bizOffering = this.queryEntity(bizOfferingEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查服务产品所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizOffering.getOrgId()) && !loginUserDataScope.contains(bizOffering.getOrgId())) {
                throw new CommonException("您没有权限编辑该服务产品，产品id：{}", bizOffering.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizOffering.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该服务产品，产品id：{}", bizOffering.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该服务产品，产品id：{}", bizOffering.getId());
            }
        }
        
        BeanUtil.copyProperties(bizOfferingEditParam, bizOffering);
        this.updateById(bizOffering);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizOfferingIdParam> bizOfferingIdParamList) {
        List<String> offeringIdList = CollStreamUtil.toList(bizOfferingIdParamList, BizOfferingIdParam::getId);
        if(ObjectUtil.isNotEmpty(offeringIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizOffering> offeringList = this.listByIds(offeringIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查服务产品所属机构是否在权限范围内
                for(BizOffering offering : offeringList) {
                    if(ObjectUtil.isNotEmpty(offering.getOrgId()) && !loginUserDataScope.contains(offering.getOrgId())) {
                        throw new CommonException("您没有权限删除该服务产品，产品id：{}", offering.getId());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizOffering offering : offeringList) {
                        if(!offering.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该服务产品，产品id：{}", offering.getId());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除服务产品");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(offeringIdList);
    }

    @Override
    public BizOffering detail(BizOfferingIdParam bizOfferingIdParam) {
        BizOffering bizOffering = this.queryEntity(bizOfferingIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查服务产品所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizOffering.getOrgId()) && !loginUserDataScope.contains(bizOffering.getOrgId())) {
                throw new CommonException("您没有权限查看该服务产品，产品id：{}", bizOffering.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizOffering.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该服务产品，产品id：{}", bizOffering.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该服务产品，产品id：{}", bizOffering.getId());
            }
        }
        
        return bizOffering;
    }

    @Override
    public BizOffering queryEntity(String id) {
        BizOffering bizOffering = this.getById(id);
        if(ObjectUtil.isEmpty(bizOffering)) {
            throw new CommonException("服务或产品清单不存在，id值为：{}", id);
        }
        return bizOffering;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizOfferingServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizOffering.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}

