
package vip.xiaonuo.dev.modular.dfc.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据源库表参数
 *
 * <AUTHOR>
 * @date 2022/7/29 9:59
 */
@Getter
@Setter
public class DevDfcDbTableSelectorParam {

    /** 数据源Id */
    @Schema(description = "数据源Id")
    @NotBlank(message = "dbsId不能为空")
    private String dbsId;
}
