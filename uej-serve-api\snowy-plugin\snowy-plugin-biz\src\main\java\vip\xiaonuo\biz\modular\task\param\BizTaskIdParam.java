
package vip.xiaonuo.biz.modular.task.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 任务信息Id参数
 *
 * <AUTHOR>
 * @date  2024/06/12 15:42
 **/
@Getter
@Setter
public class BizTaskIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;
}
