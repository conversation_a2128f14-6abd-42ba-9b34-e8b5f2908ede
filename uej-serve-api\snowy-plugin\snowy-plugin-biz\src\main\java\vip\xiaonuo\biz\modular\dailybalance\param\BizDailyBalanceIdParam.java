
package vip.xiaonuo.biz.modular.dailybalance.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 项目结款记录表Id参数
 *
 * <AUTHOR>
 * @date  2024/12/02 13:50
 **/
@Getter
@Setter
public class BizDailyBalanceIdParam {

    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;
}
