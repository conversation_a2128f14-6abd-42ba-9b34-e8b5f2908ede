package vip.xiaonuo.biz.modular.webhook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.modular.customer.entity.BizCustomer;
import vip.xiaonuo.biz.modular.customer.service.BizCustomerService;
import vip.xiaonuo.biz.modular.giftcarddetail.service.BizGiftCardDetailService;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.biz.modular.schedule.service.BizScheduleService;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.entity.BizTaskGiftCard;
import vip.xiaonuo.biz.modular.task.service.BizTaskGiftCardService;
import vip.xiaonuo.biz.modular.task.service.BizTaskService;
import vip.xiaonuo.biz.modular.taskdatastatistics.entity.BizTaskDataStatistics;
import vip.xiaonuo.biz.modular.taskdatastatistics.service.BizTaskDataStatisticsService;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;
import vip.xiaonuo.biz.modular.taskitem.service.BizTaskItemService;
import vip.xiaonuo.biz.modular.voucher.service.BizVoucherService;
import vip.xiaonuo.biz.modular.webhook.param.WordPressWebhookParam;
import vip.xiaonuo.biz.modular.webhook.service.WebhookService;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.sys.modular.user.entity.SysUser;
import vip.xiaonuo.sys.modular.user.service.SysUserService;
import vip.xiaonuo.sys.modular.org.entity.SysOrg;
import vip.xiaonuo.sys.modular.org.service.SysOrgService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Webhook服务实现类
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@Slf4j
public class WebhookServiceImpl implements WebhookService {

    @Resource
    private BizTaskService bizTaskService;

    @Resource
    private BizTaskItemService bizTaskItemService;

    @Resource
    private BizTaskDataStatisticsService bizTaskDataStatisticsService;

    @Resource
    private BizTaskGiftCardService bizTaskGiftCardService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private BizCustomerService bizCustomerService;

    @Resource
    private BizVoucherService bizVoucherService;

    @Resource
    private BizGiftCardDetailService bizGiftCardDetailService;

    @Resource
    private BizScheduleService bizScheduleService;

    @Resource
    private SysOrgService sysOrgService;

    /** webhook密钥，用于验证签名 */
    @Value("${webhook.wordpress.secret:default_webhook_secret}")
    private String webhookSecret;

    /** API Key，用于URL参数验证 */
    @Value("${webhook.wordpress.api-key:wp_api_2024_secure_key}")
    private String webhookApiKey;

    /** 是否启用API Key验证 */
    @Value("${webhook.wordpress.enable-api-key-verify:true}")
    private boolean enableApiKeyVerify;

    @Override
    public boolean verifyWebhookSignature(HttpServletRequest request, WordPressWebhookParam webhookParam) {
        try {
            // 获取请求头中的签名
            String signature = request.getHeader("X-WordPress-Signature");
            if (StrUtil.isEmpty(signature)) {
                signature = request.getHeader("X-Hub-Signature-256");
            }

            if (StrUtil.isEmpty(signature)) {
                log.warn("缺少webhook签名头");
                return false;
            }

            // 计算预期的签名
            String payload = JSONUtil.toJsonStr(webhookParam);
            HMac hmac = new HMac(HmacAlgorithm.HmacSHA256, webhookSecret.getBytes());
            String expectedSignature = "sha256=" + hmac.digestHex(payload);

            // 验证签名
            boolean isValid = expectedSignature.equals(signature);
            if (!isValid) {
                log.warn("Webhook签名验证失败，期望: {}, 实际: {}", expectedSignature, signature);
            }

            return isValid;

        } catch (Exception e) {
            log.error("验证webhook签名时发生错误", e);
            return false;
        }
    }

    @Override
    public boolean verifyWebhookApiKey(HttpServletRequest request) {
        try {
            // 如果未启用API Key验证，则跳过
            if (!enableApiKeyVerify) {
                log.debug("API Key验证已禁用，跳过验证");
                return true;
            }

            // 从URL参数中获取key
            String requestApiKey = request.getParameter("key");
            if (StrUtil.isEmpty(requestApiKey)) {
                log.warn("缺少API Key参数");
                return false;
            }

            // 验证API Key
            boolean isValid = webhookApiKey.equals(requestApiKey);
            if (!isValid) {
                log.warn("API Key验证失败，期望: {}, 实际: {}", webhookApiKey, requestApiKey);
            } else {
                log.debug("API Key验证成功");
            }

            return isValid;

        } catch (Exception e) {
            log.error("验证API Key时发生错误", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTaskFromWordPress(WordPressWebhookParam webhookParam) {
        try {
            log.info("开始创建WordPress任务: {}", JSONUtil.toJsonStr(webhookParam));

            // 1. 创建主任务
            BizTask bizTask = new BizTask();
            mapBasicInfoToTask(bizTask, webhookParam);
            mapCustomerInfoToTask(bizTask, webhookParam);
            mapStaffInfoToTask(bizTask, webhookParam);
            mapPaymentInfoToTask(bizTask, webhookParam);
            mapTimeInfoToTask(bizTask, webhookParam);

            // 保存主任务
            bizTaskService.save(bizTask);
            log.info("主任务创建成功，任务ID: {}", bizTask.getId());

            // 2. 创建任务数据统计记录（用于流程计时）
            createTaskStatistics(bizTask, webhookParam);

            // 3. 创建任务子项记录
            createTaskItems(bizTask, webhookParam);

            // 4. 检查并创建排班记录
            checkAndCreateSchedule(bizTask);

            log.info("WordPress任务创建完成: {}", bizTask.getId());
            return bizTask.getId();

        } catch (Exception e) {
            log.error("创建WordPress任务失败: {}", e.getMessage(), e);
            throw new CommonException("创建WordPress任务失败: {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateTaskFromWordPress(WordPressWebhookParam webhookParam) {
        try {
            log.info("开始从WordPress更新任务，预约ID: {}", webhookParam.getAppointmentId());

            // 查找现有任务
            BizTask existingTask = null;
            if (StrUtil.isNotEmpty(webhookParam.getTaskId())) {
                existingTask = bizTaskService.getById(webhookParam.getTaskId());
            } else if (StrUtil.isNotEmpty(webhookParam.getAppointmentId())) {
                existingTask = findTaskByWordPressId(webhookParam.getAppointmentId());
            }

            if (existingTask == null) {
                log.warn("未找到要更新的任务，创建新任务");
                return createTaskFromWordPress(webhookParam);
            }

            // 备份原有信息（用于回滚）
            BizTask originalTask = BeanUtil.copyProperties(existingTask, BizTask.class);

            // 更新任务信息
            mapBasicInfoToTask(existingTask, webhookParam);
            mapStaffInfoToTask(existingTask, webhookParam);
            mapCustomerInfoToTask(existingTask, webhookParam);
            mapTimeInfoToTask(existingTask, webhookParam);
            mapPaymentInfoToTask(existingTask, webhookParam);

            // 更新任务
            bizTaskService.updateById(existingTask);

            // 更新任务子项
            updateTaskItems(existingTask, webhookParam);

            // 更新任务统计数据
            updateTaskStatistics(existingTask, webhookParam);

            log.info("WordPress任务更新成功，任务ID: {}, 预约ID: {}", existingTask.getId(), webhookParam.getAppointmentId());
            return existingTask.getId();

        } catch (Exception e) {
            log.error("从WordPress更新任务失败", e);
            throw new CommonException("更新任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String cancelTaskFromWordPress(WordPressWebhookParam webhookParam) {
        try {
            log.info("开始从WordPress取消任务，预约ID: {}", webhookParam.getAppointmentId());

            // 查找现有任务
            BizTask existingTask = null;
            if (StrUtil.isNotEmpty(webhookParam.getTaskId())) {
                existingTask = bizTaskService.getById(webhookParam.getTaskId());
            } else if (StrUtil.isNotEmpty(webhookParam.getAppointmentId())) {
                existingTask = findTaskByWordPressId(webhookParam.getAppointmentId());
            }

            if (existingTask == null) {
                log.warn("未找到要取消的任务，预约ID: {}", webhookParam.getAppointmentId());
                return "任务不存在";
            }

            // 恢复礼品卡余额
            restoreGiftCardBalances(existingTask.getId());

            // 恢复优惠券状态
            restoreVoucherStatus(existingTask.getId());

            // 设置任务状态为已取消
            existingTask.setState("TASK_STATE_CANCEL");
            existingTask.setDescription(existingTask.getDescription() + " [由WordPress取消]");
            bizTaskService.updateById(existingTask);

            // 更新任务统计状态
            List<BizTaskDataStatistics> statistics = bizTaskDataStatisticsService.lambdaQuery()
                    .eq(BizTaskDataStatistics::getMainId, existingTask.getId())
                    .list();
            for (BizTaskDataStatistics stat : statistics) {
                stat.setStatus("TASK_STATE_CANCEL");
            }
            if (CollUtil.isNotEmpty(statistics)) {
                bizTaskDataStatisticsService.updateBatchById(statistics);
            }

            log.info("WordPress任务取消成功，任务ID: {}, 预约ID: {}", existingTask.getId(), webhookParam.getAppointmentId());
            return "任务已取消";

        } catch (Exception e) {
            log.error("从WordPress取消任务失败", e);
            throw new CommonException("取消任务失败: " + e.getMessage());
        }
    }

    @Override
    public BizTask findTaskByWordPressId(String wordpressId) {
        if (StrUtil.isEmpty(wordpressId)) {
            return null;
        }

        List<BizTask> tasks = bizTaskService.lambdaQuery()
                .like(BizTask::getExtJson, "wordpressAppointmentId")
                .like(BizTask::getExtJson, wordpressId)
                .list();

        return tasks.stream()
                .filter(task -> {
                    try {
                        if (StrUtil.isEmpty(task.getExtJson())) {
                            return false;
                        }
                        Map<String, Object> extData = JSONUtil.toBean(task.getExtJson(), Map.class);
                        return wordpressId.equals(extData.get("wordpressAppointmentId"));
                    } catch (Exception e) {
                        return false;
                    }
                })
                .findFirst()
                .orElse(null);
    }

    @Override
    public String mapWordPressStaffToSystemUser(String wordpressStaffId) {
        if (StrUtil.isEmpty(wordpressStaffId)) {
            return null;
        }

        // 不再使用映射表，直接通过员工名称匹配用户账户
        // 注意：这里的wordpressStaffId实际上应该是员工名称
        List<SysUser> users = sysUserService.list();
        for (SysUser user : users) {
            // 通过用户账户(account)匹配Amelia员工名称
            if (wordpressStaffId.equals(user.getAccount())) {
                log.info("找到匹配用户: Amelia员工[{}] -> 系统用户[{}({})]", wordpressStaffId, user.getName(), user.getAccount());
                return user.getId();
            }
        }

        log.warn("未找到Amelia员工名称 {} 对应的系统用户账户", wordpressStaffId);
        return null;
    }

    /**
     * 通过Amelia location名称映射到系统组织ID
     * @param locationName Amelia中的location名称
     * @return 系统组织ID
     */
    @Override
    public String mapAmeliaLocationToSystemOrg(String locationName) {
        if (StrUtil.isEmpty(locationName)) {
            return null;
        }

        // 通过组织名称直接匹配
        List<SysOrg> orgs = sysOrgService.list();
        for (SysOrg org : orgs) {
            if (locationName.equals(org.getName())) {
                log.info("找到匹配组织: Amelia位置[{}] -> 系统组织[{}]", locationName, org.getName());
                return org.getId();
            }
        }

        log.warn("未找到Amelia位置名称 {} 对应的系统组织", locationName);
        return null;
    }

    @Override
    public String mapWordPressServiceToSystemProduct(String wordpressServiceId) {
        if (StrUtil.isEmpty(wordpressServiceId)) {
            return null;
        }

        // 这里需要根据实际情况实现映射逻辑
        // 可以通过配置表、硬编码映射或者其他方式实现
        return wordpressServiceId;
    }

    /**
     * 映射基本信息到任务
     */
    private void mapBasicInfoToTask(BizTask bizTask, WordPressWebhookParam webhookParam) {
        // 设置任务名称
        if (CollUtil.isNotEmpty(webhookParam.getServices())) {
            String itemName = webhookParam.getServices().stream()
                    .map(WordPressWebhookParam.ServiceInfo::getName)
                    .collect(Collectors.joining(", "));
            bizTask.setItemName(itemName);
        }

        // 设置状态
        String status = webhookParam.getStatus();
        if (StrUtil.isNotEmpty(status)) {
            switch (status.toLowerCase()) {
                case "confirmed":
                case "approved":
                    bizTask.setState("TASK_STATE_PROGRESS");
                    break;
                case "pending":
                    bizTask.setState("TASK_STATE_WAIT");
                    break;
                case "cancelled":
                    bizTask.setState("TASK_STATE_CANCEL");
                    break;
                case "completed":
                    bizTask.setState("TASK_STATE_FINISH");
                    break;
                default:
                    bizTask.setState("TASK_STATE_WAIT");
                    break;
            }
        } else {
            bizTask.setState("TASK_STATE_WAIT");
        }

        // 设置备注
        if (StrUtil.isNotEmpty(webhookParam.getNotes())) {
            bizTask.setDescription(webhookParam.getNotes());
        }
    }

    /**
     * 映射员工信息到任务
     */
    private void mapStaffInfoToTask(BizTask bizTask, WordPressWebhookParam webhookParam) {
        if (webhookParam.getStaff() != null) {
            WordPressWebhookParam.StaffInfo staffInfo = webhookParam.getStaff();

            // 映射员工ID
            String systemUserId = staffInfo.getId(); // 现在直接使用传入的系统用户ID
            if (StrUtil.isNotEmpty(systemUserId)) {
                bizTask.setAssign(systemUserId);
                bizTask.setCreateUser(systemUserId);
                // 获取员工详细信息
                SysUser sysUser = sysUserService.getById(systemUserId);
                if (sysUser != null) {
                    bizTask.setStaff(sysUser.getName());
                    // 优先使用员工的orgId
                    if (StrUtil.isNotEmpty(sysUser.getOrgId())) {
                        bizTask.setOrgId(sysUser.getOrgId());
                    }
                }
            } else {
                bizTask.setAssign("Unregistered");
                bizTask.setStaff(staffInfo.getName());
            }

            // 如果staff中有departmentId，也设置到orgId
            if (StrUtil.isNotEmpty(staffInfo.getDepartmentId())) {
                bizTask.setOrgId(staffInfo.getDepartmentId());
            }
        }

        // 如果还没有orgId，尝试从metadata中获取
        if (StrUtil.isEmpty(bizTask.getOrgId()) && webhookParam.getMetadata() != null) {
            String metadataOrgId = (String) webhookParam.getMetadata().get("orgId");
            if (StrUtil.isNotEmpty(metadataOrgId)) {
                bizTask.setOrgId(metadataOrgId);
                log.info("从metadata中设置orgId: {}", metadataOrgId);
            }
        }
    }

    /**
     * 映射客户信息到任务
     */
    private void mapCustomerInfoToTask(BizTask bizTask, WordPressWebhookParam webhookParam) {
        if (webhookParam.getCustomer() != null) {
            WordPressWebhookParam.CustomerInfo customerInfo = webhookParam.getCustomer();

            // 查找或创建客户
            BizCustomer customer = findOrCreateCustomer(customerInfo);
            if (customer != null) {
                // bizTask.setCustomerId(customer.getId()); // 注释掉客户ID赋值
                // bizTask.setCustomerName((customer.getFirstName() != null ? customer.getFirstName() : "") +
                //                       (customer.getLastName() != null ? " " + customer.getLastName() : "")); // 注释掉客户姓名赋值
                log.info("客户信息已查找到但暂不赋值到任务: {}", customer.getId());
            }
        }
    }

    /**
     * 映射时间信息到任务
     */
    private void mapTimeInfoToTask(BizTask bizTask, WordPressWebhookParam webhookParam) {
        if (webhookParam.getAppointmentTime() != null) {
            WordPressWebhookParam.AppointmentTime timeInfo = webhookParam.getAppointmentTime();

            bizTask.setStartTime(timeInfo.getStartTime());
            bizTask.setPlanningEndTime(timeInfo.getEndTime());

            // 计算TOTAL_PLAN_HOURS为HH:mm格式
            if (StrUtil.isNotEmpty(timeInfo.getStartTime()) && StrUtil.isNotEmpty(timeInfo.getEndTime())) {
                try {
                    LocalDateTime startTime = LocalDateTime.parse(timeInfo.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime endTime = LocalDateTime.parse(timeInfo.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    Duration duration = Duration.between(startTime, endTime);

                    long totalMinutes = duration.toMinutes();
                    long hours = totalMinutes / 60;
                    long minutes = totalMinutes % 60;

                    String totalPlanHours = String.format("%02d:%02d", hours, minutes);
                    bizTask.setTotalPlanHours(totalPlanHours);

                    log.info("时间计算: {} -> {} = {}分钟 = {}", 
                        timeInfo.getStartTime(), timeInfo.getEndTime(), totalMinutes, totalPlanHours);
                } catch (Exception e) {
                    log.warn("计算TOTAL_PLAN_HOURS失败: {}", e.getMessage());
                    bizTask.setTotalPlanHours("01:00"); // 默认1小时
                }
            } else if (timeInfo.getDuration() != null) {
                // 如果没有具体时间，使用duration字段
                double durationHours = timeInfo.getDuration();
                long hours = (long) durationHours;
                long minutes = (long) ((durationHours - hours) * 60);
                String totalPlanHours = String.format("%02d:%02d", hours, minutes);
                bizTask.setTotalPlanHours(totalPlanHours);
                log.info("使用Duration计算: {}小时 = {}", durationHours, totalPlanHours);
            } else {
                bizTask.setTotalPlanHours("01:00"); // 默认1小时
                log.info("使用默认时长: 01:00");
            }
        }
    }

    /**
     * 映射付款信息到任务
     */
    private void mapPaymentInfoToTask(BizTask bizTask, WordPressWebhookParam webhookParam) {
        if (webhookParam.getPayment() != null) {
            WordPressWebhookParam.PaymentInfo paymentInfo = webhookParam.getPayment();

            bizTask.setTotalPrice(String.valueOf(paymentInfo.getTotalAmount() != null ? paymentInfo.getTotalAmount() : 0));
            bizTask.setCash(String.valueOf(paymentInfo.getCash() != null ? paymentInfo.getCash() : 0));
            bizTask.setCard(String.valueOf(paymentInfo.getCard() != null ? paymentInfo.getCard() : 0));
            bizTask.setSurchargeFee(String.valueOf(paymentInfo.getSurchargeFee() != null ? paymentInfo.getSurchargeFee() : 0));
            bizTask.setGiftCard(String.valueOf(paymentInfo.getGiftCard() != null ? paymentInfo.getGiftCard() : 0));
            bizTask.setVoucher(String.valueOf(paymentInfo.getVoucher() != null ? paymentInfo.getVoucher() : 0));
            bizTask.setInsuranceValue(paymentInfo.getInsurance() != null ? paymentInfo.getInsurance() : 0);
            bizTask.setTip(String.valueOf(paymentInfo.getTip() != null ? paymentInfo.getTip() : 0));

            if (StrUtil.isNotEmpty(paymentInfo.getVoucherCode())) {
                bizTask.setVoucherNo(paymentInfo.getVoucherCode());
            }
        }
    }

    /**
     * 创建任务子项
     */
    private void createTaskItems(BizTask bizTask, WordPressWebhookParam webhookParam) {
        List<BizTaskItem> taskItems = new ArrayList<>();

        if (webhookParam.getServices() != null && !webhookParam.getServices().isEmpty()) {
            for (WordPressWebhookParam.ServiceInfo service : webhookParam.getServices()) {
                BizTaskItem taskItem = new BizTaskItem();
                taskItem.setMainId(bizTask.getId());
                taskItem.setStaffId(bizTask.getAssign());
                taskItem.setStaffName(bizTask.getStaff());
                taskItem.setOrgId(bizTask.getOrgId());
                taskItem.setProductId(service.getId());
                taskItem.setProductName(service.getName());
                taskItem.setType(service.getType());
                
                // 将duration（小时数）转换为HH:mm格式
                if (service.getDuration() != null) {
                    double durationHours = service.getDuration();
                    long hours = (long) durationHours;
                    long minutes = (long) ((durationHours - hours) * 60);
                    String planningHours = String.format("%02d:%02d", hours, minutes);
                    taskItem.setPlanningHours(planningHours);
                    log.info("任务子项Duration转换: {}小时 -> {}", durationHours, planningHours);
                } else {
                    taskItem.setPlanningHours("01:00"); // 默认1小时
                }
                
                taskItem.setListPrice(service.getPrice() != null ?
                    String.valueOf(service.getPrice()) : "0");
                taskItem.setCreateUser(bizTask.getAssign());
                taskItems.add(taskItem);
            }
        }

        if (!taskItems.isEmpty()) {
            bizTaskItemService.saveBatch(taskItems);
            log.info("任务子项记录创建成功，数量: {}", taskItems.size());
        }
    }

    /**
     * 创建任务统计数据
     */
    private void createTaskStatistics(BizTask bizTask, WordPressWebhookParam webhookParam) {
        BizTaskDataStatistics mainStats = new BizTaskDataStatistics();
        mainStats.setMainId(bizTask.getId());
        mainStats.setParentId(bizTask.getId());
        mainStats.setStaffId(bizTask.getAssign());
        mainStats.setStaff(bizTask.getStaff());
        mainStats.setOrgId(bizTask.getOrgId());
        mainStats.setStartTime(bizTask.getStartTime());
        mainStats.setPlanningEndTime(bizTask.getPlanningEndTime());
        mainStats.setStatus(bizTask.getState());
        mainStats.setProgress("0");
        mainStats.setActualHours("0");
        mainStats.setCreateUser(bizTask.getAssign());

        // 计算planning_hours：planning_end_time减去start_time
        if (StrUtil.isNotEmpty(bizTask.getStartTime()) && StrUtil.isNotEmpty(bizTask.getPlanningEndTime())) {
            try {
                LocalDateTime startTime = LocalDateTime.parse(bizTask.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                LocalDateTime endTime = LocalDateTime.parse(bizTask.getPlanningEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                Duration duration = Duration.between(startTime, endTime);
                double hours = duration.toMinutes() / 60.0;
                mainStats.setPlanningHours(String.format("%.2f", hours));
            } catch (Exception e) {
                log.warn("计算planning_hours失败: {}", e.getMessage());
                mainStats.setPlanningHours("1.0");
            }
        } else {
            mainStats.setPlanningHours("1.0");
        }

        // 查找并关联biz_schedule的id作为parent_id
        try {
            String assignDate = DateUtil.format(DateUtil.parse(bizTask.getStartTime(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd");
            BizSchedule schedule = bizScheduleService.lambdaQuery()
                    .eq(BizSchedule::getStaffId, bizTask.getAssign())
                    .eq(BizSchedule::getAssignDate, assignDate)
                    .eq(BizSchedule::getOrganizationId, bizTask.getOrgId())
                    .one();
            if (schedule != null) {
                mainStats.setParentId(schedule.getId());
            }
        } catch (Exception e) {
            log.warn("设置parent_id失败: {}", e.getMessage());
        }

        // 设置产品信息
        if (webhookParam.getServices() != null && !webhookParam.getServices().isEmpty()) {
            List<Map<String, Object>> productList = new ArrayList<>();
            for (WordPressWebhookParam.ServiceInfo service : webhookParam.getServices()) {
                Map<String, Object> product = new HashMap<>();
                product.put("id", service.getId());
                product.put("name", service.getName());
                product.put("price", service.getPrice());
                product.put("duration", service.getDuration());
                product.put("quantity", service.getQuantity());
                productList.add(product);
            }
            mainStats.setProduct(JSONUtil.toJsonStr(productList));
        }

        bizTaskDataStatisticsService.save(mainStats);
        log.info("任务数据统计记录创建成功: {}", mainStats.getId());
    }

    /**
     * 更新任务子项
     */
    private void updateTaskItems(BizTask bizTask, WordPressWebhookParam webhookParam) {
        // 删除现有的任务子项
        bizTaskItemService.lambdaUpdate()
                .eq(BizTaskItem::getMainId, bizTask.getId())
                .remove();

        // 创建新的任务子项
        createTaskItems(bizTask, webhookParam);
    }

    /**
     * 更新任务统计数据
     */
    private void updateTaskStatistics(BizTask bizTask, WordPressWebhookParam webhookParam) {
        List<BizTaskDataStatistics> statistics = bizTaskDataStatisticsService.lambdaQuery()
                .eq(BizTaskDataStatistics::getMainId, bizTask.getId())
                .list();

        for (BizTaskDataStatistics stat : statistics) {
            stat.setTaskName(bizTask.getItemName());
            stat.setStatus(bizTask.getState());
            stat.setStartTime(bizTask.getStartTime());
            stat.setPlanningEndTime(bizTask.getPlanningEndTime());
            
            // 计算planning_hours：保持与createTaskStatistics一致的小数格式
            if (StrUtil.isNotEmpty(bizTask.getStartTime()) && StrUtil.isNotEmpty(bizTask.getPlanningEndTime())) {
                try {
                    LocalDateTime startTime = LocalDateTime.parse(bizTask.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime endTime = LocalDateTime.parse(bizTask.getPlanningEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    Duration duration = Duration.between(startTime, endTime);
                    double hours = duration.toMinutes() / 60.0;
                    stat.setPlanningHours(String.format("%.2f", hours));
                } catch (Exception e) {
                    log.warn("更新时计算planning_hours失败: {}", e.getMessage());
                    stat.setPlanningHours("1.00");
                }
            } else {
                stat.setPlanningHours("1.00");
            }
            
            stat.setStaffId(bizTask.getAssign());
            stat.setStaff(bizTask.getStaff());
            stat.setOrgId(bizTask.getOrgId());
            stat.setCustomerId(bizTask.getCustomerId());
            stat.setCustomerName(bizTask.getCustomerName());
            stat.setRemark(bizTask.getDescription());
        }

        if (CollUtil.isNotEmpty(statistics)) {
            bizTaskDataStatisticsService.updateBatchById(statistics);
        }
    }

    /**
     * 处理礼品卡
     */
    private void processGiftCards(BizTask bizTask, WordPressWebhookParam webhookParam) {
        if (webhookParam.getPayment() != null && CollUtil.isNotEmpty(webhookParam.getPayment().getGiftCards())) {
            List<BizTask.GiftCardInfo> giftCardList = new ArrayList<>();

            for (WordPressWebhookParam.GiftCardInfo wpGiftCard : webhookParam.getPayment().getGiftCards()) {
                if (wpGiftCard.getAmount() != null && wpGiftCard.getAmount() > 0) {
                    // 验证并核销礼品卡
                    String result = bizGiftCardDetailService.redeemGiftCard(
                            wpGiftCard.getCardNo(),
                            wpGiftCard.getPin(),
                            String.valueOf(wpGiftCard.getAmount())
                    );

                    if ("success".equals(result)) {
                        BizTask.GiftCardInfo giftCard = new BizTask.GiftCardInfo();
                        giftCard.setCardNo(wpGiftCard.getCardNo());
                        giftCard.setPin(wpGiftCard.getPin());
                        giftCard.setAmount(wpGiftCard.getAmount());
                        giftCard.setBalance(wpGiftCard.getBalance());
                        giftCard.setAvailableBalance(wpGiftCard.getAvailableBalance());
                        giftCard.setVerified(wpGiftCard.getVerified() != null ? wpGiftCard.getVerified() : false);

                        giftCardList.add(giftCard);
                    } else {
                        log.warn("礼品卡核销失败: {}", result);
                    }
                }
            }

            if (CollUtil.isNotEmpty(giftCardList)) {
                bizTask.setGiftCardList(giftCardList);

                // 保存礼品卡关联信息
                List<BizTaskGiftCard> taskGiftCards = giftCardList.stream()
                        .map(gc -> {
                            BizTaskGiftCard taskGiftCard = new BizTaskGiftCard();
                            taskGiftCard.setTaskId(bizTask.getId());
                            taskGiftCard.setCardNo(gc.getCardNo());
                            taskGiftCard.setPin(gc.getPin());
                            taskGiftCard.setAmount(gc.getAmount());
                            taskGiftCard.setBalance(gc.getBalance());
                            taskGiftCard.setAvailableBalance(gc.getAvailableBalance());
                            taskGiftCard.setVerified(gc.isVerified());
                            return taskGiftCard;
                        }).collect(Collectors.toList());

                bizTaskGiftCardService.saveTaskGiftCards(bizTask.getId(), taskGiftCards);
            }
        }
    }

    /**
     * 处理优惠券
     */
    private void processVoucher(BizTask bizTask, WordPressWebhookParam webhookParam) {
        if (webhookParam.getPayment() != null && StrUtil.isNotEmpty(webhookParam.getPayment().getVoucherCode())) {
            String voucherCode = webhookParam.getPayment().getVoucherCode();
            String totalPrice = bizTask.getTotalPrice();

            String result = bizVoucherService.redeemVoucher(voucherCode, totalPrice);
            if (result.contains("success")) {
                String voucherValue = result.replace("success", "");
                bizTask.setVoucher(voucherValue);
            } else {
                log.warn("优惠券核销失败: {}", result);
            }
        }
    }

    /**
     * 查找或创建客户
     */
    private BizCustomer findOrCreateCustomer(WordPressWebhookParam.CustomerInfo customerInfo) {
        if (customerInfo == null || StrUtil.isEmpty(customerInfo.getEmail())) {
            return null;
        }

        // 先通过邮箱查找客户
        BizCustomer customer = bizCustomerService.lambdaQuery()
                .eq(BizCustomer::getEmail, customerInfo.getEmail())
                .one();

        if (customer == null && StrUtil.isNotEmpty(customerInfo.getPhone())) {
            // 通过电话查找客户
            customer = bizCustomerService.lambdaQuery()
                    .eq(BizCustomer::getPhone, customerInfo.getPhone())
                    .one();
        }

        if (customer == null) {
            // 创建新客户
            customer = new BizCustomer();
            // 分割姓名为firstName和lastName
            if (StrUtil.isNotEmpty(customerInfo.getName())) {
                String[] nameParts = customerInfo.getName().split(" ", 2);
                customer.setFirstName(nameParts[0]);
                if (nameParts.length > 1) {
                    customer.setLastName(nameParts[1]);
                }
            }
            customer.setEmail(customerInfo.getEmail());
            customer.setPhone(customerInfo.getPhone());

            // 设置WordPress客户ID到扩展字段，包含地址信息
            Map<String, Object> extData = new HashMap<>();
            if (StrUtil.isNotEmpty(customerInfo.getId())) {
                extData.put("wordpressCustomerId", customerInfo.getId());
            }
            if (StrUtil.isNotEmpty(customerInfo.getAddress())) {
                extData.put("address", customerInfo.getAddress());
            }
            if (!extData.isEmpty()) {
                customer.setExtJson(JSONUtil.toJsonStr(extData));
            }

            bizCustomerService.save(customer);
        }

        return customer;
    }

    /**
     * 恢复礼品卡余额
     */
    private void restoreGiftCardBalances(String taskId) {
        // 这里应该实现恢复礼品卡余额的逻辑
        // 具体实现需要根据现有的礼品卡系统来定制
        log.info("恢复任务 {} 的礼品卡余额", taskId);
    }

    /**
     * 恢复优惠券状态
     */
    private void restoreVoucherStatus(String taskId) {
        // 这里应该实现恢复优惠券状态的逻辑
        // 具体实现需要根据现有的优惠券系统来定制
        log.info("恢复任务 {} 的优惠券状态", taskId);
    }

    /**
     * 检查并创建排班记录
     */
    private void checkAndCreateSchedule(BizTask bizTask) {
        try {
            // 获取任务日期
            String assignDate = DateUtil.format(DateUtil.parse(bizTask.getStartTime(), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd");

            // 检查该员工当天是否已有排班
            List<BizSchedule> existingSchedules = bizScheduleService.lambdaQuery()
                    .eq(BizSchedule::getStaffId, bizTask.getAssign())
                    .eq(BizSchedule::getAssignDate, assignDate)
                    .isNotNull(BizSchedule::getWorkTime)
                    .list();

            if (existingSchedules.isEmpty()) {
                // 自动创建排班记录
                BizSchedule schedule = new BizSchedule();
                schedule.setStaffId(bizTask.getAssign());
                schedule.setStaffName(bizTask.getStaff());
                schedule.setAssignDate(assignDate);
                schedule.setOrganizationId(bizTask.getOrgId());
                schedule.setOrganizationName("-");
                schedule.setWorkTime("[\"08:00:00\",\"20:00:00\"]"); // 默认工作时间
                schedule.setCreateUser(bizTask.getAssign());

                bizScheduleService.save(schedule);
                log.info("自动创建排班记录成功: 员工ID={}, 日期={}", bizTask.getAssign(), assignDate);
            }
        } catch (Exception e) {
            log.warn("创建排班记录失败: {}", e.getMessage());
            // 排班记录创建失败不影响任务创建
        }
    }
}
