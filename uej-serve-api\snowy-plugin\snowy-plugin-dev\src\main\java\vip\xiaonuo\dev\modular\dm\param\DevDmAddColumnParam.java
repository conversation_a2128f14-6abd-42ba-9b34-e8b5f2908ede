
package vip.xiaonuo.dev.modular.dm.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态字段配置添加参数
 *
 * <AUTHOR>
 * @date 2023/08/04 08:18
 **/
@Getter
@Setter
public class DevDmAddColumnParam {

    /**
     * 数据源
     */
    @Schema(description = "数据源")
    @NotEmpty(message = "dbsId不能为空")
    private String dbsId;

    /**
     * 表名称
     */
    @Schema(description = "表名称")
    @NotEmpty(message = "tableName不能为空")
    private String tableName;

    /**
     * 字段名称
     */
    @Schema(description = "字段名称")
    @NotEmpty(message = "columnName不能为空")
    private String columnName;

    /**
     * 字段类型
     */
    @Schema(description = "字段类型")
    @NotEmpty(message = "columnType不能为空")
    private String columnType;

    /**
     * 字段长度
     */
    @Schema(description = "字段长度")
    private Integer columnLength = 0;

    /**
     * 字段注释
     */
    @Schema(description = "字段注释")
    @NotEmpty(message = "columnRemark不能为空")
    private String columnRemark;

    /**
     * 插入位置：在某个字段名之后
     */
    @Schema(description = "插入位置：在某个字段名之后")
    private String afterColumnName;
}
