
package vip.xiaonuo.flw.modular.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 编辑模型参数
 *
 * <AUTHOR>
 * @date 2022/7/31 17:55
 */
@Getter
@Setter
public class FlwModelEditParam {

    /** id */
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 管理员id */
    @Schema(description = "管理员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "adminId不能为空")
    private String adminId;

    /** 名称 */
    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "name不能为空")
    private String name;

    /** 类型 */
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "formType不能为空")
    private String formType;

    /** 分类 */
    @Schema(description = "分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "category不能为空")
    private String category;

    /** 图标 */
    @Schema(description = "图标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "icon不能为空")
    private String icon;

    /** 移动端图标 */
    @Schema(description = "移动端图标")
    private String iconMobile;

    /** 颜色 */
    @Schema(description = "颜色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "color不能为空")
    private String color;

    /** 排序码 */
    @Schema(description = "排序码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "sortCode不能为空")
    private Integer sortCode;

    /** 数据库表JSON */
    @Schema(description = "数据库表JSON")
    private String tableJson;

    /** 表单JSON */
    @Schema(description = "表单JSON")
    private String formJson;

    /** 流程JSON */
    @Schema(description = "流程JSON")
    private String processJson;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;
}
