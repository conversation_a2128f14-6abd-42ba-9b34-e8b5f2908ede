
package vip.xiaonuo.dbs.config;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.handler.EntityListHandler;
import cn.hutool.db.sql.SqlExecutor;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent;
import com.baomidou.dynamic.datasource.provider.AbstractDataSourceProvider;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import vip.xiaonuo.common.enums.CommonDeleteFlagEnum;
import vip.xiaonuo.dbs.modular.entity.DbsStorage;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源相关配置
 *
 * <AUTHOR>
 * @date 2022/1/6 23:10
 */
@Configuration
public class DbsConfigure {

    /**
     * 自定义数据源来源
     *
     * <AUTHOR>
     * @date 2022/3/8 18:57
     **/
    @Component
    @Order
    public static class DynamicDataSourceProvider extends AbstractDataSourceProvider {

        private final DynamicDataSourceProperties dynamicDataSourceProperties;

        @Resource
        private final MybatisPlusProperties mybatisPlusProperties;

        @Resource
        private final DefaultDataSourceCreator defaultDataSourceCreator;

        public DynamicDataSourceProvider(DefaultDataSourceCreator defaultDataSourceCreator, DynamicDataSourceProperties dynamicDataSourceProperties, MybatisPlusProperties mybatisPlusProperties, DefaultDataSourceCreator defaultDataSourceCreator1) {
            super(defaultDataSourceCreator);
            this.dynamicDataSourceProperties = dynamicDataSourceProperties;
            this.mybatisPlusProperties = mybatisPlusProperties;
            this.defaultDataSourceCreator = defaultDataSourceCreator1;
        }

        @Override
        public Map<String, DataSource> loadDataSources() {
            HashMap<String, DataSource> dataSourceHashMap = MapUtil.newHashMap();
            Map<String, DataSourceProperty> dataSourcePropertyMap = dynamicDataSourceProperties.getDatasource();
            if(ObjectUtil.isNotEmpty(dataSourcePropertyMap)) {
                String primaryDsName = new DynamicDataSourceProperties().getPrimary();
                DataSourceProperty masterDataSourceProperty = dataSourcePropertyMap.get(primaryDsName);
                if(ObjectUtil.isNotEmpty(masterDataSourceProperty)) {
                    Connection conn = null;
                    try {
                        if (ObjectUtil.isEmpty(masterDataSourceProperty.getPublicKey())) {
                            masterDataSourceProperty.setPublicKey(dynamicDataSourceProperties.getPublicKey());
                        }
                        EncDataSourceInitEvent encDataSourceInitEvent = new EncDataSourceInitEvent();
                        encDataSourceInitEvent.beforeCreate(masterDataSourceProperty);
                        conn = DriverManager.getConnection(masterDataSourceProperty.getUrl(), masterDataSourceProperty.getUsername(),
                                masterDataSourceProperty.getPassword());
                        String dbsTableName;
                        Object annotationValue = AnnotationUtil.getAnnotationValue(DbsStorage.class, TableName.class);
                        if(ObjectUtil.isNotEmpty(annotationValue)) {
                            dbsTableName = Convert.toStr(annotationValue);
                        } else {
                            dbsTableName = StrUtil.toUnderlineCase(DbsStorage.class.getSimpleName());
                        }
                        GlobalConfig.DbConfig dbConfig = mybatisPlusProperties.getGlobalConfig().getDbConfig();
                        String logicDeleteField = dbConfig.getLogicDeleteField();
                        if(ObjectUtil.isEmpty(logicDeleteField)) {
                            logicDeleteField = "DELETE_FLAG";
                        }
                        String logicNotDeleteValue = dbConfig.getLogicNotDeleteValue();
                        if(ObjectUtil.isEmpty(logicNotDeleteValue)) {
                            logicNotDeleteValue = EnumUtil.toString(CommonDeleteFlagEnum.NOT_DELETE);
                        }
                        List<Entity> entityList = SqlExecutor.query(conn, "SELECT * FROM " + dbsTableName + " WHERE " + logicDeleteField + " = \"" + logicNotDeleteValue + "\"", new EntityListHandler());
                        entityList.forEach(entity -> {
                            DataSourceProperty dataSourceProperty = new DataSourceProperty();
                            BeanUtil.copyProperties(entity, dataSourceProperty, true);
                            dataSourceHashMap.put(dataSourceProperty.getPoolName(), defaultDataSourceCreator.createDataSource(dataSourceProperty));
                        });
                    } catch (SQLException ignored) {
                    } finally {
                        DbUtil.close(conn);
                    }
                }
            }
            return dataSourceHashMap;
        }
    }
}
