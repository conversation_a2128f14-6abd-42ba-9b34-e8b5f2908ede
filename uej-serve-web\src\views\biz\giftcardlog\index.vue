<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Task Id" name="taskId">
						<a-input v-model:value="searchFormState.taskId" placeholder="Please enter Task Id" @pressEnter="tableRef.refresh(true)"/>
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Card No" name="cardNo">
						<a-input v-model:value="searchFormState.cardNo" placeholder="Please enter Card No" @pressEnter="tableRef.refresh(true)"/>
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Type" name="type">
						<a-select v-model:value="searchFormState.type">
							<a-select-option value="giftcard">Card</a-select-option>
							<a-select-option value="voucher">Voucher</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-button type="primary" @click="tableRef.refresh(true)">{{ $t('common.searchButton') }}</a-button>
					<a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
				</a-col>
			</a-row>
		</a-form>
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
			:scroll="{x:'max-content'}"
		>
			<!-- <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('bizGiftCardLogAdd')">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.addButton') }}
                    </a-button>
                    <xn-batch-delete
                        v-if="hasPerm('bizGiftCardLogBatchDelete')"
                        :selectedRowKeys="selectedRowKeys"
                        @batchDelete="deleteBatchBizGiftCardLog"
						:buttonName="$t('common.batchRemoveButton')"
                    />
                </a-space>
            </template> -->
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<!-- <a @click="formRef.onOpen(record)" v-if="hasPerm('bizGiftCardLogEdit')">{{ $t('common.editButton') }}</a> -->
						<!-- <a-divider type="vertical" v-if="hasPerm(['bizGiftCardLogEdit', 'bizGiftCardLogDelete'], 'and')" /> -->
						<a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizGiftCardLog(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('bizGiftCardLogDelete')">{{
								$t('common.removeButton')
							}}</a-button>
						</a-popconfirm>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh(true)" />
</template>

<script setup name="giftcardlog">
	import { cloneDeep } from 'lodash-es'
	import Form from './form.vue'
	import bizGiftCardLogApi from '@/api/biz/bizGiftCardLogApi'
	const searchFormState = ref({})
	const searchFormRef = ref()
	const tableRef = ref()
	const formRef = ref()
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	const columns = [
		{
			title: 'Card No',
			dataIndex: 'cardNo'
		},
		{
			title: 'Type',
			dataIndex: 'type'
		},
		{
			title: 'Task Id',
			dataIndex: 'taskId'
		},
		{
			title: 'Use Value',
			dataIndex: 'useValue'
		},
		{
			title: 'Used By',
			dataIndex: 'usedBy'
		}
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['bizGiftCardLogEdit', 'bizGiftCardLogDelete'])) {
		columns.push({
			title: 'action',
			dataIndex: 'action',
			align: 'center',
			width: '90px'
		})
	}
	const selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		// columns数字类型字段加入 needTotal: true 可以勾选自动算账
		alert: {
			show: true,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		const searchFormParam = cloneDeep(searchFormState.value)
		return bizGiftCardLogApi.bizGiftCardLogPage(Object.assign(parameter, searchFormParam)).then((data) => {
			return data
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 删除
	const deleteBizGiftCardLog = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		bizGiftCardLogApi.bizGiftCardLogDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchBizGiftCardLog = (params) => {
		bizGiftCardLogApi.bizGiftCardLogDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}
</script>
