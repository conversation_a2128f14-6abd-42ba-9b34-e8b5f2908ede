
package vip.xiaonuo.biz.modular.giftcarddetail.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡信息实体
 *
 * <AUTHOR>
 * @date  2024/07/01 18:08
 **/
@Getter
@Setter
@TableName("biz_gift_card_detail")
public class BizGiftCardDetail {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** gift_card_id */
    @Schema(description = "gift_card_id")
    private String mainId;

    /** Card Number */
    @Schema(description = "Card Number")
    private String cardNumber;

    /** Pin */
    @Schema(description = "Pin")
    private String pin;

    /** Create Date */
    @Schema(description = "Create Date")
    private String createDate;

    /** Active Date */
    @Schema(description = "Active Date")
    private String activeDate;

    /** Exp Date */
    @Schema(description = "Exp Date")
    private String expDate;

    /** Used Date */
    @Schema(description = "Used Date")
    private String usedDate;

    /** Value */
    @Schema(description = "Value")
    private String value;

    /** Rest Value */
    @Schema(description = "Rest Value")
    private String restValue;

    /** If Avtived */
    @Schema(description = "If Avtived")
    private String actived;

    /** EXT_JSON */
    @Schema(description = "EXT_JSON")
    private String extJson;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String orgId;

    private String orgName;

    private String taskId;

    @TableField(exist = false)
    private int num;
}
