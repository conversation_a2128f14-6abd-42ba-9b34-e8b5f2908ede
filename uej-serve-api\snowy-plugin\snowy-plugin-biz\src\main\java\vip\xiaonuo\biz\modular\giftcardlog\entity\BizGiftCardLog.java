
package vip.xiaonuo.biz.modular.giftcardlog.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡记录实体
 *
 * <AUTHOR>
 * @date  2024/06/13 09:46
 **/
@Getter
@Setter
@TableName("biz_gift_card_log")
public class BizGiftCardLog {

    /** 主键 */
    @TableId
    @Schema(description = "主键")
    private String id;

    /** 主表ID */
    @Schema(description = "主表ID")
    private String mainId;

    /** Total Value */
    @Schema(description = "Total Value")
    private String totalValue;

    /** Use Value */
    @Schema(description = "Use Value")
    private String useValue;

    /** Rest Value */
    @Schema(description = "Rest Value")
    private String restValue;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 组织ID */
    @Schema(description = "组织ID")
    private String orgId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 更新用户 */
    @Schema(description = "更新用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String extJson;

    private String usedBy;

    private String cardNo;

    private String type;

    private String taskId;
}
