package vip.xiaonuo.biz.modular.giftcarddetail.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.giftcard.entity.BizGiftCard;
import vip.xiaonuo.biz.modular.giftcard.service.BizGiftCardService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.giftcarddetail.entity.BizGiftCardDetail;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailAddParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailEditParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailIdParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailPageParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailChangeStatusParam;
import vip.xiaonuo.biz.modular.giftcarddetail.service.BizGiftCardDetailService;
import cn.hutool.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 礼品卡信息控制器
 *
 * <AUTHOR>
 * @date  2024/07/01 18:08
 */
@Tag(name = "礼品卡信息控制器")
@RestController
@Validated
public class BizGiftCardDetailController {

    @Resource
    private BizGiftCardDetailService bizGiftCardDetailService;

    @Resource
    private BizGiftCardService bizGiftCardService;

    /**
     * 获取礼品卡信息分页
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    @Operation(summary = "获取礼品卡信息分页")
    @SaCheckPermission("/biz/giftcarddetail/page")
    @GetMapping("/biz/giftcarddetail/page")
    public CommonResult<Page<BizGiftCardDetail>> page(BizGiftCardDetailPageParam bizGiftCardDetailPageParam) {
        return CommonResult.data(bizGiftCardDetailService.page(bizGiftCardDetailPageParam));
    }

    /**
     * 添加礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    @Operation(summary = "添加礼品卡信息")
    @CommonLog("添加礼品卡信息")
    @SaCheckPermission("/biz/giftcarddetail/add")
    @PostMapping("/biz/giftcarddetail/add")
    public CommonResult<String> add(@RequestBody @Valid BizGiftCardDetail bizGiftCardDetail) {
        BizGiftCard giftCard = bizGiftCardService.lambdaQuery().eq(BizGiftCard::getId, bizGiftCardDetail.getMainId()).one();
        if (ObjectUtil.isEmpty(giftCard)) {
            return CommonResult.ok();
        }
        bizGiftCardDetail.setValue(giftCard.getValue());
        bizGiftCardDetail.setRestValue(giftCard.getValue());
        String prefix = ObjectUtil.isEmpty(giftCard.getPrefix()) ? DateUtil.now() : giftCard.getPrefix();
        int startNo = ObjectUtil.isEmpty(giftCard.getStartNo()) ? 0 : giftCard.getStartNo();
        int num = bizGiftCardDetail.getNum() == 0 ? 1 : bizGiftCardDetail.getNum();

        // 获取当前登录用户的orgId
        String currentUserOrgId = null;
        try {
            currentUserOrgId = vip.xiaonuo.auth.core.util.StpLoginUserUtil.getLoginUser().getOrgId();
        } catch (Exception e) {
            // 获取失败时继续执行，orgId为空
        }

        List<BizGiftCardDetail> giftCardDetails = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            BizGiftCardDetail giftCardDetail = new BizGiftCardDetail();
            /*String baseStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
            String randomStr = RandomUtil.randomString(baseStr, 5);
            bizGiftCardDetail.setCardNumber(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + randomStr);*/
            bizGiftCardDetail.setCardNumber(prefix + String.format("%03d", startNo + i));
            BeanUtil.copyProperties(bizGiftCardDetail, giftCardDetail);
            
            // 设置orgId
            if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                giftCardDetail.setOrgId(currentUserOrgId);
            }
            
            giftCardDetails.add(giftCardDetail);
        }
        bizGiftCardDetailService.saveBatch(giftCardDetails);
        giftCard.setStartNo(startNo + bizGiftCardDetail.getNum());
        bizGiftCardService.updateById(giftCard);
        return CommonResult.ok();
    }

    /**
     * 编辑礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    @Operation(summary = "编辑礼品卡信息")
    @CommonLog("编辑礼品卡信息")
    @SaCheckPermission("/biz/giftcarddetail/edit")
    @PostMapping("/biz/giftcarddetail/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizGiftCardDetailEditParam bizGiftCardDetailEditParam) {
        bizGiftCardDetailService.edit(bizGiftCardDetailEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    @Operation(summary = "删除礼品卡信息")
    @CommonLog("删除礼品卡信息")
    @SaCheckPermission("/biz/giftcarddetail/delete")
    @PostMapping("/biz/giftcarddetail/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "List cannot be empty")
                                                   List<BizGiftCardDetailIdParam> bizGiftCardDetailIdParamList) {
        bizGiftCardDetailService.delete(bizGiftCardDetailIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取礼品卡信息详情
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    @Operation(summary = "获取礼品卡信息详情")
    @SaCheckPermission("/biz/giftcarddetail/detail")
    @GetMapping("/biz/giftcarddetail/detail")
    public CommonResult<BizGiftCardDetail> detail(@Valid BizGiftCardDetailIdParam bizGiftCardDetailIdParam) {
        return CommonResult.data(bizGiftCardDetailService.detail(bizGiftCardDetailIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    @Operation(summary = "获取礼品卡信息动态字段的配置")
    @SaCheckPermission("/biz/giftcarddetail/dynamicFieldConfigList")
    @GetMapping("/biz/giftcarddetail/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizGiftCardDetailService.dynamicFieldConfigList(columnName));
    }

    /**
     * 修改礼品卡激活状态
     *
     * <AUTHOR>
     * @date  2024/12/19
     */
    @Operation(summary = "修改礼品卡激活状态")
    @CommonLog("修改礼品卡激活状态")
    @SaCheckPermission("/biz/giftcarddetail/changeStatus")
    @PostMapping("/biz/giftcarddetail/changeStatus")
    public CommonResult<String> changeActivationStatus(@RequestBody @Valid BizGiftCardDetailChangeStatusParam bizGiftCardDetailChangeStatusParam) {
        bizGiftCardDetailService.changeActivationStatus(bizGiftCardDetailChangeStatusParam);
        return CommonResult.ok();
    }
}
