
package vip.xiaonuo.flw.core.enums;

import lombok.Getter;

/**
 * 业务表流程状态枚举
 *
 * <AUTHOR>
 * @date 2023/5/11 13:26
 **/
@Getter
public enum NodeBusinessTableFlwStatusEnum {

    /**
     * 审批中
     */
    IN_APPROVAL("IN_APPROVAL"),

    /**
     * 审批完成
     */
    APPROVAL_COMPLETE("APPROVAL_COMPLETE"),

    /**
     * 审批已退回
     */
    APPROVAL_BACK("APPROVAL_BACK"),

    /**
     * 审批已拒绝
     */
    APPROVAL_REJECT("APPROVAL_REJECT"),

    /**
     * 审批已终止
     */
    APPROVAL_CLOSE("APPROVAL_CLOSE"),

    /**
     * 审批已撤回
     */
    APPROVAL_REVOKE("APPROVAL_REVOKE");

    private final String value;

    NodeBusinessTableFlwStatusEnum(String value) {
        this.value = value;
    }
}
