package vip.xiaonuo.biz.modular.offeringgroup.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.offeringgroup.entity.BizOfferingGroup;
import vip.xiaonuo.biz.modular.offeringgroup.mapper.BizOfferingGroupMapper;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupAddParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupEditParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupIdParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupPageParam;
import vip.xiaonuo.biz.modular.offeringgroup.service.BizOfferingGroupService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 服务或产品组合Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 16:57
 **/
@Service
public class BizOfferingGroupServiceImpl extends ServiceImpl<BizOfferingGroupMapper, BizOfferingGroup> implements BizOfferingGroupService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizOfferingGroup> page(BizOfferingGroupPageParam bizOfferingGroupPageParam) {
        QueryWrapper<BizOfferingGroup> queryWrapper = new QueryWrapper<BizOfferingGroup>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizOfferingGroupPageParam.getCategory())) {
            queryWrapper.lambda().eq(BizOfferingGroup::getCategory, bizOfferingGroupPageParam.getCategory());
        }
        if(ObjectUtil.isNotEmpty(bizOfferingGroupPageParam.getName())) {
            queryWrapper.lambda().like(BizOfferingGroup::getName, bizOfferingGroupPageParam.getName());
        }
        if(ObjectUtil.isAllNotEmpty(bizOfferingGroupPageParam.getSortField(), bizOfferingGroupPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizOfferingGroupPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizOfferingGroupPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizOfferingGroupPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BizOfferingGroup::getCreateTime);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizOfferingGroup::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizOfferingGroup::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizOfferingGroup::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizOfferingGroupAddParam bizOfferingGroupAddParam) {
        BizOfferingGroup bizOfferingGroup = BeanUtil.toBean(bizOfferingGroupAddParam, BizOfferingGroup.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizOfferingGroup.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizOfferingGroup.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizOfferingGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizOfferingGroupEditParam bizOfferingGroupEditParam) {
        BizOfferingGroup bizOfferingGroup = this.queryEntity(bizOfferingGroupEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查组合所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizOfferingGroup.getOrgId()) && !loginUserDataScope.contains(bizOfferingGroup.getOrgId())) {
                throw new CommonException("您没有权限编辑该服务产品组合，组合名称：{}", bizOfferingGroup.getName());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizOfferingGroup.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该服务产品组合，组合名称：{}", bizOfferingGroup.getName());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该服务产品组合，组合名称：{}", bizOfferingGroup.getName());
            }
        }
        
        BeanUtil.copyProperties(bizOfferingGroupEditParam, bizOfferingGroup);
        this.updateById(bizOfferingGroup);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizOfferingGroupIdParam> bizOfferingGroupIdParamList) {
        List<String> offeringGroupIdList = CollStreamUtil.toList(bizOfferingGroupIdParamList, BizOfferingGroupIdParam::getId);
        if(ObjectUtil.isNotEmpty(offeringGroupIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizOfferingGroup> offeringGroupList = this.listByIds(offeringGroupIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查组合所属机构是否在权限范围内
                for(BizOfferingGroup offeringGroup : offeringGroupList) {
                    if(ObjectUtil.isNotEmpty(offeringGroup.getOrgId()) && !loginUserDataScope.contains(offeringGroup.getOrgId())) {
                        throw new CommonException("您没有权限删除该服务产品组合，组合名称：{}", offeringGroup.getName());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizOfferingGroup offeringGroup : offeringGroupList) {
                        if(!offeringGroup.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该服务产品组合，组合名称：{}", offeringGroup.getName());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除服务产品组合");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(offeringGroupIdList);
    }

    @Override
    public BizOfferingGroup detail(BizOfferingGroupIdParam bizOfferingGroupIdParam) {
        BizOfferingGroup bizOfferingGroup = this.queryEntity(bizOfferingGroupIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查组合所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizOfferingGroup.getOrgId()) && !loginUserDataScope.contains(bizOfferingGroup.getOrgId())) {
                throw new CommonException("您没有权限查看该服务产品组合，组合名称：{}", bizOfferingGroup.getName());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizOfferingGroup.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该服务产品组合，组合名称：{}", bizOfferingGroup.getName());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该服务产品组合，组合名称：{}", bizOfferingGroup.getName());
            }
        }
        
        return bizOfferingGroup;
    }

    @Override
    public BizOfferingGroup queryEntity(String id) {
        BizOfferingGroup bizOfferingGroup = this.getById(id);
        if(ObjectUtil.isEmpty(bizOfferingGroup)) {
            throw new CommonException("服务或产品组合不存在，id值为：{}", id);
        }
        return bizOfferingGroup;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizOfferingGroupServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizOfferingGroup.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}


