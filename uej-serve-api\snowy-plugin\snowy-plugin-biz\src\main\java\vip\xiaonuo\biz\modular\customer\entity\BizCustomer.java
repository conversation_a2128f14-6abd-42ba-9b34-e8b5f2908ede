
package vip.xiaonuo.biz.modular.customer.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外部会员信息实体
 *
 * <AUTHOR>
 * @date  2024/06/12 17:22
 **/
@Getter
@Setter
@TableName("biz_customer")
public class BizCustomer {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** First Name */
    @Schema(description = "First Name")
    private String firstName;

    /** Last Name */
    @Schema(description = "Last Name")
    private String lastName;

    /** Gender */
    @Schema(description = "Gender")
    private String gender;

    /** Level */
    @Schema(description = "Level")
    private String level;

    /** Reward Points */
    @Schema(description = "Reward Points")
    private String rewardPoints;

    /** Attend Time */
    @Schema(description = "Attend Time")
    private String attendTime;

    /** Email */
    @Schema(description = "Email")
    private String email;

    /** Phone */
    @Schema(description = "Phone")
    private String phone;

    private String extJson;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 组织id */
    @Schema(description = "组织id")
    private String orgId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "会员号")
    private String memberId;

    @Schema(description = "保险公司")
    private String InsuranceCompany;

    @Schema(description = "医保卡子账号")
    private String personalNo;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "邮编")
    private String postCode;

    @Schema(description = "紧急联系电话")
    private String emergencyContact;

    private String medicalCondition;
    private String medications;
    private String massageOilAllergic;
    private String pregnant;
    private String pastIllnesses;
    private String skinInfection;
}
