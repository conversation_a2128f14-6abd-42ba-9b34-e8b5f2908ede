package vip.xiaonuo.biz.modular.scheduledate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.scheduledate.entity.BizScheduleDate;
import vip.xiaonuo.biz.modular.scheduledate.mapper.BizScheduleDateMapper;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateAddParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateEditParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateIdParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDatePageParam;
import vip.xiaonuo.biz.modular.scheduledate.service.BizScheduleDateService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;

import java.util.List;

/**
 * 排班表当日属性Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/18 16:34
 **/
@Service
public class BizScheduleDateServiceImpl extends ServiceImpl<BizScheduleDateMapper, BizScheduleDate> implements BizScheduleDateService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizScheduleDate> page(BizScheduleDatePageParam bizScheduleDatePageParam) {
        QueryWrapper<BizScheduleDate> queryWrapper = new QueryWrapper<BizScheduleDate>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizScheduleDatePageParam.getRemark())) {
            queryWrapper.lambda().like(BizScheduleDate::getRemark, bizScheduleDatePageParam.getRemark());
        }
        if(ObjectUtil.isAllNotEmpty(bizScheduleDatePageParam.getSortField(), bizScheduleDatePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizScheduleDatePageParam.getSortOrder());
            queryWrapper.orderBy(true, bizScheduleDatePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizScheduleDatePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizScheduleDate::getId);
        }
        
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizScheduleDate::getOrganizationId, loginUserDataScope);
        } else {
            // 如果没有数据范围权限，只能查看自己创建的排班日期配置
            queryWrapper.lambda().eq(BizScheduleDate::getCreateUser, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizScheduleDateAddParam bizScheduleDateAddParam) {
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(ObjectUtil.isNotEmpty(bizScheduleDateAddParam.getOrganizationId()) && !loginUserDataScope.contains(bizScheduleDateAddParam.getOrganizationId())) {
                throw new CommonException("您没有权限在该机构下创建排班日期配置，机构id：{}", bizScheduleDateAddParam.getOrganizationId());
            }
        }
        // 注意：如果没有数据范围权限，仍允许创建，但会记录创建用户
        
        BizScheduleDate bizScheduleDate = BeanUtil.toBean(bizScheduleDateAddParam, BizScheduleDate.class);
        this.save(bizScheduleDate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizScheduleDateEditParam bizScheduleDateEditParam) {
        BizScheduleDate bizScheduleDate = this.queryEntity(bizScheduleDateEditParam.getId());
        
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizScheduleDate.getOrganizationId())) {
                throw new CommonException("您没有权限编辑该机构下的排班日期配置，机构id：{}", bizScheduleDate.getOrganizationId());
            }
        } else {
            // 如果没有数据范围权限，只能编辑自己创建的配置
            if(!bizScheduleDate.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该排班日期配置，配置id：{}", bizScheduleDate.getId());
            }
        }
        
        BeanUtil.copyProperties(bizScheduleDateEditParam, bizScheduleDate);
        this.updateById(bizScheduleDate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizScheduleDateIdParam> bizScheduleDateIdParamList) {
        List<String> scheduleDateIdList = CollStreamUtil.toList(bizScheduleDateIdParamList, BizScheduleDateIdParam::getId);
        if(ObjectUtil.isNotEmpty(scheduleDateIdList)) {
            // 校验数据范围 - 基于organizationId进行权限控制
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            List<BizScheduleDate> scheduleDateList = this.listByIds(scheduleDateIdList);
            
            for(BizScheduleDate scheduleDate : scheduleDateList) {
                if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                    if(!loginUserDataScope.contains(scheduleDate.getOrganizationId())) {
                        throw new CommonException("您没有权限删除该机构下的排班日期配置，机构id：{}", scheduleDate.getOrganizationId());
                    }
                } else {
                    // 如果没有数据范围权限，只能删除自己创建的配置
                    if(!scheduleDate.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该排班日期配置，配置id：{}", scheduleDate.getId());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(scheduleDateIdList);
    }

    @Override
    public BizScheduleDate detail(BizScheduleDateIdParam bizScheduleDateIdParam) {
        BizScheduleDate bizScheduleDate = this.queryEntity(bizScheduleDateIdParam.getId());
        
        // 校验数据范围 - 基于organizationId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizScheduleDate.getOrganizationId())) {
                throw new CommonException("您没有权限查看该机构下的排班日期配置，机构id：{}", bizScheduleDate.getOrganizationId());
            }
        } else {
            // 如果没有数据范围权限，只能查看自己创建的配置
            if(!bizScheduleDate.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该排班日期配置，配置id：{}", bizScheduleDate.getId());
            }
        }
        
        return bizScheduleDate;
    }

    @Override
    public BizScheduleDate queryEntity(String id) {
        BizScheduleDate bizScheduleDate = this.getById(id);
        if(ObjectUtil.isEmpty(bizScheduleDate)) {
            throw new CommonException("排班表当日属性不存在，id值为：{}", id);
        }
        return bizScheduleDate;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizScheduleDateServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizScheduleDate.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}
