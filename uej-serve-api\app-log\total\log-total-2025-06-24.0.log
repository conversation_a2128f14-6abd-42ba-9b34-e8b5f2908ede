2025-06-24T08:33:52.561+08:00  INFO 4800 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 4800 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-24T08:33:52.564+08:00  INFO 4800 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-24T08:33:56.886+08:00  INFO 4800 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24T08:33:56.892+08:00  INFO 4800 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-24T08:33:57.137+08:00  INFO 4800 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 216 ms. Found 0 Redis repository interfaces.
2025-06-24T08:33:57.644+08:00  WARN 4800 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-24T08:33:57.978+08:00  WARN 4800 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-24T08:33:58.527+08:00  WARN 4800 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-24T08:33:59.080+08:00  INFO 4800 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-24T08:33:59.104+08:00  INFO 4800 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-24T08:33:59.104+08:00  INFO 4800 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-24T08:33:59.216+08:00  INFO 4800 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-24T08:33:59.217+08:00  INFO 4800 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6595 ms
2025-06-24T08:33:59.676+08:00  INFO 4800 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-24T08:34:00.122+08:00  INFO 4800 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-24T08:34:00.123+08:00  INFO 4800 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-24T08:34:00.124+08:00  INFO 4800 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-24T08:34:00.124+08:00  INFO 4800 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-24T08:34:00.498+08:00  INFO 4800 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-24T08:34:04.822+08:00  INFO 4800 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-24T08:34:05.649+08:00  INFO 4800 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-24T08:34:08.028+08:00  INFO 4800 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@189f9f9f, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-24T08:34:08.066+08:00  INFO 4800 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-24T08:34:08.067+08:00  INFO 4800 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-24T08:34:08.067+08:00  INFO 4800 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-24T08:34:08.067+08:00  INFO 4800 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-24T08:34:08.082+08:00  INFO 4800 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-24T08:34:08.578+08:00  INFO 4800 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@75381b61)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@47b72d3b, clock: SystemClock, configuration: Configuration(false)]
2025-06-24T08:34:10.901+08:00  INFO 4800 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-24T08:34:10.906+08:00  INFO 4800 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-24T08:34:11.069+08:00  INFO 4800 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-24T08:34:14.207+08:00  INFO 4800 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-24T08:34:14.361+08:00  WARN 4800 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'container'
2025-06-24T08:34:14.372+08:00  INFO 4800 --- [main] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-24T08:34:14.387+08:00  INFO 4800 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-24T08:34:14.393+08:00  INFO 4800 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-24T08:34:14.399+08:00  INFO 4800 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-24T08:34:14.399+08:00  INFO 4800 --- [main] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-24T08:34:14.399+08:00  INFO 4800 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-24T08:34:14.401+08:00  INFO 4800 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-24T08:34:14.401+08:00  INFO 4800 --- [main] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-24T08:34:14.401+08:00  INFO 4800 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-24T08:34:14.415+08:00  INFO 4800 --- [main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-24T08:34:14.434+08:00 ERROR 4800 --- [main] o.s.boot.SpringApplication               : Application run failed

org.springframework.context.ApplicationContextException: Failed to start bean 'container'
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:287)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:467)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:256)
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:201)
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:979)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:628)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:762)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:464)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at vip.xiaonuo.Application.main(Application.java:40)
Caused by: org.springframework.data.redis.listener.adapter.RedisListenerExecutionFailedException: org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.lazyListen(RedisMessageListenerContainer.java:382)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.start(RedisMessageListenerContainer.java:360)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:284)
	... 11 common frames omitted
Caused by: org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1805)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1736)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1538)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.lambda$getConnection$0(LettuceConnectionFactory.java:1518)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.doInLock(LettuceConnectionFactory.java:1478)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1515)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1199)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:1006)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.lambda$initialize$0(RedisMessageListenerContainer.java:1239)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.doInLock(RedisMessageListenerContainer.java:1453)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer$Subscriber.initialize(RedisMessageListenerContainer.java:1233)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.doSubscribe(RedisMessageListenerContainer.java:427)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.lazyListen(RedisMessageListenerContainer.java:403)
	at org.springframework.data.redis.listener.RedisMessageListenerContainer.lazyListen(RedisMessageListenerContainer.java:373)
	... 13 common frames omitted
Caused by: org.springframework.data.redis.connection.PoolException: Could not get a resource from the pool
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:104)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1734)
	... 25 common frames omitted
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to 127.0.0.1/<unresolved>:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:350)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:215)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:112)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:112)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.lambda$getConnection$0(LettucePoolingConnectionProvider.java:93)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:211)
	at io.lettuce.core.support.ConnectionPoolSupport$RedisPooledObjectFactory.create(ConnectionPoolSupport.java:201)
	at org.apache.commons.pool2.BasePooledObjectFactory.makeObject(BasePooledObjectFactory.java:71)
	at org.apache.commons.pool2.impl.GenericObjectPool.create(GenericObjectPool.java:566)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:306)
	at org.apache.commons.pool2.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:233)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:122)
	at io.lettuce.core.support.ConnectionPoolSupport$1.borrowObject(ConnectionPoolSupport.java:117)
	at org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.getConnection(LettucePoolingConnectionProvider.java:99)
	... 26 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: /127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:946)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:335)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-24T08:38:37.268+08:00  INFO 29004 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 29004 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-24T08:38:37.270+08:00  INFO 29004 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-24T08:38:39.749+08:00  INFO 29004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24T08:38:39.755+08:00  INFO 29004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-24T08:38:39.974+08:00  INFO 29004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 192 ms. Found 0 Redis repository interfaces.
2025-06-24T08:38:40.406+08:00  WARN 29004 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-24T08:38:40.747+08:00  WARN 29004 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-24T08:38:41.082+08:00  WARN 29004 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-24T08:38:41.461+08:00  INFO 29004 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-24T08:38:41.480+08:00  INFO 29004 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-24T08:38:41.480+08:00  INFO 29004 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-24T08:38:41.578+08:00  INFO 29004 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-24T08:38:41.579+08:00  INFO 29004 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4243 ms
2025-06-24T08:38:41.923+08:00  INFO 29004 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-24T08:38:42.205+08:00  INFO 29004 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-24T08:38:42.207+08:00  INFO 29004 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-24T08:38:42.208+08:00  INFO 29004 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-24T08:38:42.208+08:00  INFO 29004 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-24T08:38:42.528+08:00  INFO 29004 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-24T08:38:46.503+08:00  INFO 29004 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-24T08:38:47.278+08:00  INFO 29004 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-24T08:38:49.234+08:00  INFO 29004 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@36dbfa68, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-24T08:38:49.261+08:00  INFO 29004 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-24T08:38:49.261+08:00  INFO 29004 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-24T08:38:49.261+08:00  INFO 29004 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-24T08:38:49.261+08:00  INFO 29004 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-24T08:38:49.270+08:00  INFO 29004 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-24T08:38:49.677+08:00  INFO 29004 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@7bfd8f92)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@1641dfb3, clock: SystemClock, configuration: Configuration(false)]
2025-06-24T08:38:51.853+08:00  INFO 29004 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-24T08:38:51.858+08:00  INFO 29004 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-24T08:38:52.002+08:00  INFO 29004 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-24T08:38:54.562+08:00  INFO 29004 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-24T08:38:54.656+08:00  INFO 29004 --- [main] vip.xiaonuo.Application                  : Started Application in 18.567 seconds (process running for 19.785)
2025-06-24T08:38:54.732+08:00  INFO 29004 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-24T08:40:15.663+08:00  INFO 29004 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24T08:40:15.663+08:00  INFO 29004 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-24T08:40:15.667+08:00  INFO 29004 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-06-24T10:22:52.917+08:00  INFO 29004 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-24T10:22:52.959+08:00  INFO 29004 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-24T10:22:52.969+08:00  INFO 29004 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-24T10:22:52.983+08:00  INFO 29004 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-24T10:22:52.983+08:00  INFO 29004 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-24T10:22:52.983+08:00  INFO 29004 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-24T10:22:52.987+08:00  INFO 29004 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-24T10:22:52.987+08:00  INFO 29004 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-24T10:22:52.988+08:00  INFO 29004 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-24T10:23:17.676+08:00  INFO 25956 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 25956 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-24T10:23:17.679+08:00  INFO 25956 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-24T10:23:21.602+08:00  INFO 25956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24T10:23:21.608+08:00  INFO 25956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-24T10:23:21.894+08:00  INFO 25956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 260 ms. Found 0 Redis repository interfaces.
2025-06-24T10:23:22.328+08:00  WARN 25956 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-24T10:23:22.698+08:00  WARN 25956 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-24T10:23:23.122+08:00  WARN 25956 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-24T10:23:23.790+08:00  INFO 25956 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-24T10:23:23.817+08:00  INFO 25956 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-24T10:23:23.817+08:00  INFO 25956 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-24T10:23:23.968+08:00  INFO 25956 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-24T10:23:23.969+08:00  INFO 25956 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6231 ms
2025-06-24T10:23:24.889+08:00  INFO 25956 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-24T10:23:26.331+08:00  INFO 25956 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-24T10:23:26.334+08:00  INFO 25956 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-24T10:23:26.339+08:00  INFO 25956 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-24T10:23:26.339+08:00  INFO 25956 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-24T10:23:26.859+08:00  INFO 25956 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-24T10:23:32.437+08:00  INFO 25956 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-24T10:23:33.464+08:00  INFO 25956 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-24T10:23:36.066+08:00  INFO 25956 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@********, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-24T10:23:36.100+08:00  INFO 25956 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-24T10:23:36.101+08:00  INFO 25956 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-24T10:23:36.101+08:00  INFO 25956 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-24T10:23:36.101+08:00  INFO 25956 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-24T10:23:36.113+08:00  INFO 25956 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-24T10:23:36.596+08:00  INFO 25956 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@67887bf2)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@1a4340f2, clock: SystemClock, configuration: Configuration(false)]
2025-06-24T10:23:39.372+08:00  INFO 25956 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-24T10:23:39.377+08:00  INFO 25956 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-24T10:23:39.529+08:00  INFO 25956 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-24T10:23:42.946+08:00  INFO 25956 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-24T10:23:43.065+08:00  INFO 25956 --- [main] vip.xiaonuo.Application                  : Started Application in 26.95 seconds (process running for 28.55)
2025-06-24T10:23:43.153+08:00  INFO 25956 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-24T10:24:06.622+08:00  INFO 25956 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24T10:24:06.622+08:00  INFO 25956 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-24T10:24:06.624+08:00  INFO 25956 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-24T13:07:17.032+08:00  INFO 25956 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-24T13:07:17.076+08:00  INFO 25956 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-24T13:07:17.143+08:00  INFO 25956 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-24T13:07:17.156+08:00  INFO 25956 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-24T13:07:17.156+08:00  INFO 25956 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-24T13:07:17.156+08:00  INFO 25956 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-24T13:07:17.159+08:00  INFO 25956 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-24T13:07:17.160+08:00  INFO 25956 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-24T13:07:17.160+08:00  INFO 25956 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-24T13:07:37.949+08:00  INFO 33764 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 33764 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-24T13:07:37.951+08:00  INFO 33764 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-24T13:07:41.417+08:00  INFO 33764 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-24T13:07:41.421+08:00  INFO 33764 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-24T13:07:41.622+08:00  INFO 33764 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 182 ms. Found 0 Redis repository interfaces.
2025-06-24T13:07:42.052+08:00  WARN 33764 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-24T13:07:42.910+08:00  WARN 33764 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-24T13:07:44.060+08:00  WARN 33764 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-24T13:07:44.570+08:00  INFO 33764 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-24T13:07:44.591+08:00  INFO 33764 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-24T13:07:44.591+08:00  INFO 33764 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-24T13:07:44.708+08:00  INFO 33764 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-24T13:07:44.708+08:00  INFO 33764 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6696 ms
2025-06-24T13:07:45.092+08:00  INFO 33764 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-24T13:07:45.414+08:00  INFO 33764 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-24T13:07:45.415+08:00  INFO 33764 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-24T13:07:45.418+08:00  INFO 33764 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-24T13:07:45.418+08:00  INFO 33764 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-24T13:07:45.844+08:00  INFO 33764 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-24T13:07:50.091+08:00  INFO 33764 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-24T13:07:50.983+08:00  INFO 33764 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-24T13:07:53.541+08:00  INFO 33764 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@66d766b9, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-24T13:07:53.578+08:00  INFO 33764 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-24T13:07:53.578+08:00  INFO 33764 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-24T13:07:53.578+08:00  INFO 33764 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-24T13:07:53.579+08:00  INFO 33764 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-24T13:07:53.593+08:00  INFO 33764 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-24T13:07:54.058+08:00  INFO 33764 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@78b888df)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@22b581a5, clock: SystemClock, configuration: Configuration(false)]
2025-06-24T13:07:56.449+08:00  INFO 33764 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-24T13:07:56.454+08:00  INFO 33764 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-24T13:07:56.603+08:00  INFO 33764 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-24T13:07:59.500+08:00  INFO 33764 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-24T13:07:59.597+08:00  INFO 33764 --- [main] vip.xiaonuo.Application                  : Started Application in 22.855 seconds (process running for 24.203)
2025-06-24T13:07:59.658+08:00  INFO 33764 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-24T13:08:12.132+08:00  INFO 33764 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24T13:08:12.132+08:00  INFO 33764 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-24T13:08:12.135+08:00  INFO 33764 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-06-24T17:30:31.475+08:00  INFO 33764 --- [lettuce-nioEventLoop-4-2] i.lettuce.core.protocol.CommandHandler   : null Unexpected exception during request: java.net.SocketException: Connection reset

java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-24T17:30:31.475+08:00  INFO 33764 --- [lettuce-nioEventLoop-4-1] i.lettuce.core.protocol.CommandHandler   : null Unexpected exception during request: java.net.SocketException: Connection reset

java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-24T17:30:31.475+08:00  INFO 33764 --- [lettuce-nioEventLoop-7-1] i.lettuce.core.protocol.CommandHandler   : null Unexpected exception during request: java.net.SocketException: Connection reset

java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-24T17:30:31.508+08:00  INFO 33764 --- [lettuce-eventExecutorLoop-5-3] i.l.core.protocol.ConnectionWatchdog     : Reconnecting, last destination was /127.0.0.1:6379
2025-06-24T17:30:31.518+08:00  WARN 33764 --- [lettuce-nioEventLoop-7-2] i.l.core.protocol.ConnectionWatchdog     : Cannot reconnect to [127.0.0.1/<unresolved>:6379]: Connection refused: no further information: /127.0.0.1:6379
2025-06-24T17:30:31.535+08:00  INFO 33764 --- [lettuce-eventExecutorLoop-1-16] i.l.core.protocol.ConnectionWatchdog     : Reconnecting, last destination was /127.0.0.1:6379
2025-06-24T17:30:31.535+08:00  INFO 33764 --- [lettuce-eventExecutorLoop-1-1] i.l.core.protocol.ConnectionWatchdog     : Reconnecting, last destination was /127.0.0.1:6379
2025-06-24T17:30:31.538+08:00  WARN 33764 --- [lettuce-nioEventLoop-4-4] i.l.core.protocol.ConnectionWatchdog     : Cannot reconnect to [127.0.0.1/<unresolved>:6379]: Connection refused: no further information: /127.0.0.1:6379
2025-06-24T17:30:31.538+08:00  WARN 33764 --- [lettuce-nioEventLoop-4-3] i.l.core.protocol.ConnectionWatchdog     : Cannot reconnect to [127.0.0.1/<unresolved>:6379]: Connection refused: no further information: /127.0.0.1:6379
2025-06-24T17:30:40.606+08:00  INFO 33764 --- [lettuce-eventExecutorLoop-5-14] i.l.core.protocol.ConnectionWatchdog     : Reconnecting, last destination was 127.0.0.1/<unresolved>:6379
2025-06-24T17:30:40.610+08:00  WARN 33764 --- [lettuce-nioEventLoop-7-14] i.l.core.protocol.ConnectionWatchdog     : Cannot reconnect to [127.0.0.1/<unresolved>:6379]: Connection refused: no further information: /127.0.0.1:6379
2025-06-24T17:30:40.637+08:00  INFO 33764 --- [lettuce-eventExecutorLoop-1-8] i.l.core.protocol.ConnectionWatchdog     : Reconnecting, last destination was 127.0.0.1/<unresolved>:6379
2025-06-24T17:30:40.637+08:00  INFO 33764 --- [lettuce-eventExecutorLoop-1-9] i.l.core.protocol.ConnectionWatchdog     : Reconnecting, last destination was 127.0.0.1/<unresolved>:6379
2025-06-24T17:30:40.639+08:00  WARN 33764 --- [lettuce-nioEventLoop-4-11] i.l.core.protocol.ConnectionWatchdog     : Cannot reconnect to [127.0.0.1/<unresolved>:6379]: Connection refused: no further information: /127.0.0.1:6379
2025-06-24T17:30:40.639+08:00  WARN 33764 --- [lettuce-nioEventLoop-4-12] i.l.core.protocol.ConnectionWatchdog     : Cannot reconnect to [127.0.0.1/<unresolved>:6379]: Connection refused: no further information: /127.0.0.1:6379
