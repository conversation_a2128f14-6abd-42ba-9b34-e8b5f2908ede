---
description: 
globs: 
alwaysApply: false
---
# 后端开发规范

## 项目技术栈

后端项目基于以下技术栈开发：
- Java
- Spring Boot
- Maven

## 项目模块

后端项目分为多个模块：

- [snowy-web-app/](mdc:uej-serve-api/snowy-web-app) - Web应用主模块
- [snowy-plugin/](mdc:uej-serve-api/snowy-plugin) - 插件模块
- [snowy-common/](mdc:uej-serve-api/snowy-common) - 通用组件模块
- [snowy-plugin-api/](mdc:uej-serve-api/snowy-plugin-api) - 插件API模块

## 开发规范

### 代码风格
- 遵循Java代码规范
- 类名使用PascalCase命名
- 方法名和变量名使用camelCase命名
- 常量使用SNAKE_CASE全大写命名

### API开发
- RESTful API设计
- 合理使用HTTP方法（GET、POST、PUT、DELETE等）
- 统一的响应格式

### 数据库
- 数据库表名和字段名使用下划线命名法
- 表名应具有业务含义
- 主键命名为id
- 使用SQL文件管理数据库结构：[uej_serve_foreign.sql](mdc:uej_serve_foreign.sql)
- 每个表必须包含的字段为TENANT_ID varchar(20)\DELETE_FLAG varchar(255)\CREATE_TIME datetime\CREATE_USER varchar(20)\UPDATE_TIME datetime\UPDATE_USER varchar(20)
-  数据库表字段都为大写

## 构建与部署

- 使用Maven构建：`mvn clean package`
- 构建后的JAR包位于target目录
