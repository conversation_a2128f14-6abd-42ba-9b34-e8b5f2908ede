
package vip.xiaonuo.biz.modular.giftcard.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.giftcard.entity.BizGiftCard;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardAddParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardEditParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardIdParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardPageParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardVerifyParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardVerifyResult;
import vip.xiaonuo.biz.modular.giftcard.service.BizGiftCardService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 礼品卡信息控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 17:07
 */
@Tag(name = "礼品卡信息控制器")
@RestController
@Validated
public class BizGiftCardController {

    @Resource
    private BizGiftCardService bizGiftCardService;

    /**
     * 获取礼品卡信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    @Operation(summary = "获取礼品卡信息分页")
    @SaCheckPermission("/biz/giftcard/page")
    @GetMapping("/biz/giftcard/page")
    public CommonResult<Page<BizGiftCard>> page(BizGiftCardPageParam bizGiftCardPageParam) {
        return CommonResult.data(bizGiftCardService.page(bizGiftCardPageParam));
    }

    /**
     * 添加礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    @Operation(summary = "添加礼品卡信息")
    @CommonLog("添加礼品卡信息")
    @SaCheckPermission("/biz/giftcard/add")
    @PostMapping("/biz/giftcard/add")
    public CommonResult<String> add(@RequestBody @Valid BizGiftCardAddParam bizGiftCardAddParam) {
        bizGiftCardService.add(bizGiftCardAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    @Operation(summary = "编辑礼品卡信息")
    @CommonLog("编辑礼品卡信息")
    @SaCheckPermission("/biz/giftcard/edit")
    @PostMapping("/biz/giftcard/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizGiftCardEditParam bizGiftCardEditParam) {
        bizGiftCardService.edit(bizGiftCardEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    @Operation(summary = "删除礼品卡信息")
    @CommonLog("删除礼品卡信息")
    @SaCheckPermission("/biz/giftcard/delete")
    @PostMapping("/biz/giftcard/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizGiftCardIdParam> bizGiftCardIdParamList) {
        bizGiftCardService.delete(bizGiftCardIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取礼品卡信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    @Operation(summary = "获取礼品卡信息详情")
    @SaCheckPermission("/biz/giftcard/detail")
    @GetMapping("/biz/giftcard/detail")
    public CommonResult<BizGiftCard> detail(@Valid BizGiftCardIdParam bizGiftCardIdParam) {
        return CommonResult.data(bizGiftCardService.detail(bizGiftCardIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    @Operation(summary = "获取礼品卡信息动态字段的配置")
    @SaCheckPermission("/biz/giftcard/dynamicFieldConfigList")
    @GetMapping("/biz/giftcard/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizGiftCardService.dynamicFieldConfigList(columnName));
    }

    /**
     * 验证礼品卡
     *
     * <AUTHOR>
     * @date  2024/12/19
     */
    @Operation(summary = "验证礼品卡")
    @CommonLog("验证礼品卡")
    @SaCheckPermission("/biz/giftcard/verify")
    @PostMapping("/biz/giftcard/verify")
    public CommonResult<BizGiftCardVerifyResult> verify(@RequestBody @Valid BizGiftCardVerifyParam bizGiftCardVerifyParam) {
        return CommonResult.data(bizGiftCardService.verify(bizGiftCardVerifyParam));
    }
}
