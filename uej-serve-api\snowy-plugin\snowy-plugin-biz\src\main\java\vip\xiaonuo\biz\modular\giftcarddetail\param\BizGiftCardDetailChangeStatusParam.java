package vip.xiaonuo.biz.modular.giftcarddetail.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 礼品卡激活状态修改参数
 *
 * <AUTHOR>
 * @date  2024/12/19
 **/
@Getter
@Setter
public class BizGiftCardDetailChangeStatusParam {

    /** ID */
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "ID cannot be empty")
    private String id;

    /** If Avtived */
    @Schema(description = "If Avtived", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "Activation status cannot be empty")
    private String actived;
} 