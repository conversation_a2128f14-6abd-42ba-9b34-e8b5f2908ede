
package vip.xiaonuo.biz.modular.taskitem.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemAddParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemEditParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemIdParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemPageParam;
import vip.xiaonuo.biz.modular.taskitem.service.BizTaskItemService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 任务子表控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 17:34
 */
@Tag(name = "任务子表控制器")
@RestController
@Validated
public class BizTaskItemController {

    @Resource
    private BizTaskItemService bizTaskItemService;

    /**
     * 获取任务子表分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    @Operation(summary = "获取任务子表分页")
    @SaCheckPermission("/biz/taskitem/page")
    @GetMapping("/biz/taskitem/page")
    public CommonResult<Page<BizTaskItem>> page(BizTaskItemPageParam bizTaskItemPageParam) {
        return CommonResult.data(bizTaskItemService.page(bizTaskItemPageParam));
    }

    /**
     * 添加任务子表
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    @Operation(summary = "添加任务子表")
    @CommonLog("添加任务子表")
    @SaCheckPermission("/biz/taskitem/add")
    @PostMapping("/biz/taskitem/add")
    public CommonResult<String> add(@RequestBody @Valid BizTaskItemAddParam bizTaskItemAddParam) {
        bizTaskItemService.add(bizTaskItemAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑任务子表
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    @Operation(summary = "编辑任务子表")
    @CommonLog("编辑任务子表")
    @SaCheckPermission("/biz/taskitem/edit")
    @PostMapping("/biz/taskitem/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizTaskItemEditParam bizTaskItemEditParam) {
        bizTaskItemService.edit(bizTaskItemEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除任务子表
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    @Operation(summary = "删除任务子表")
    @CommonLog("删除任务子表")
    @SaCheckPermission("/biz/taskitem/delete")
    @PostMapping("/biz/taskitem/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizTaskItemIdParam> bizTaskItemIdParamList) {
        bizTaskItemService.delete(bizTaskItemIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取任务子表详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    @Operation(summary = "获取任务子表详情")
    @SaCheckPermission("/biz/taskitem/detail")
    @GetMapping("/biz/taskitem/detail")
    public CommonResult<BizTaskItem> detail(@Valid BizTaskItemIdParam bizTaskItemIdParam) {
        return CommonResult.data(bizTaskItemService.detail(bizTaskItemIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    @Operation(summary = "获取任务子表动态字段的配置")
    @SaCheckPermission("/biz/taskitem/dynamicFieldConfigList")
    @GetMapping("/biz/taskitem/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizTaskItemService.dynamicFieldConfigList(columnName));
    }
}
