<template>
	<div class="code-reader-content">
		<div class="page">
			<video ref="video" autoplay id="video"></video>
			<div class="scan-box">
				<div class="frame upper-left"></div>
				<div class="frame upper-right"></div>
				<div class="frame lower-right"></div>
				<div class="frame lower-left"></div>
				<div class="pointer-box">
					<div class="pointer"></div>
				</div>
			</div>
		</div>
		<div class="mt-4">
			<a-button type="primary" @click="toggle">retry</a-button>
		</div>
	</div>
	<a-modal v-model:open="open" :closable="true" :footer="null" :maskClosable="false" @cancel="cancal">
		<div class="flex flex-col items-center justify-center gap-2">
			<a-button :loading="loading" type="primary" @click="check('clockIn')" class="w-[120px]">ClockIn</a-button>
			<a-button :loading="loading" type="primary" @click="check('clockOut')" class="w-[120px]">ClockOut</a-button>
		</div>
	</a-modal>
</template>
<script setup>
	import { BrowserMultiFormatReader } from '@zxing/library'
	import bizAttendanceApi from '@/api/biz/bizAttendanceApi'
	import { message } from 'ant-design-vue'

	const codeReader = ref(null)
	const tipMsg = ref('正在尝试识别....')
	const tipShow = ref(true)
	const textContent = ref(undefined)
	const videoInputDevicesArray = ref([])
	const deviceId = ref('')
	const isEswitch = ref(false)
	const timer = ref(null)
	const open = ref(false)
	const loading = ref(false)
	const text = ref('')

	onMounted(async () => {
		await openScan()
	})

	onUnmounted(() => {
		codeReader.value.stopContinuousDecode()
		codeReader.value.reset()
	})

	const openScan = async () => {
		codeReader.value = await new BrowserMultiFormatReader()
		codeReader.value
			.getVideoInputDevices()
			.then(async (videoInputDevices) => {
				tipShow.value = true
				tipMsg.value = '正在尝试识别....'
				videoInputDevicesArray.value = videoInputDevices
				console.log('获取到的摄像头', videoInputDevicesArray.value)
				deviceId.value =
					videoInputDevicesArray.value.length > 1
						? videoInputDevicesArray.value[1].deviceId
						: videoInputDevicesArray.value[0].deviceId
				decodeFromInputVideoFunc()
			})
			.catch(() => {
				tipShow.value = false
			})
	}

	const decodeFromInputVideoFunc = () => {
		if (videoInputDevicesArray.value.length == 0) {
			textContent.value = '初始化摄像头失败'
			document.getElementById('video').style.display = 'none'
			return
		}
		codeReader.value.reset()
		codeReader.value.decodeFromInputVideoDeviceContinuously(deviceId.value, 'video', (result) => {
			tipMsg.value = '正在扫描'
			console.log('扫描中', result)
			if (result) {
				if (result.text) {
					text.value = result.text
					console.log('扫描成功', result)
					open.value = true

					tipMsg.value = '扫描成功'
					tipShow.value = true
					window && window.getResultEvent(result)
					window?.parent?.Gikam?.toast('扫码成功')
					codeReader.value.reset()
					codeReader.value.stopContinuousDecode()
				}
			}
		})
	}

	const cutover = () => {
		if (videoInputDevicesArray.value && videoInputDevicesArray.value.length > 1) {
			deviceId.value =
				deviceId.value === videoInputDevicesArray.value[0].deviceId
					? videoInputDevicesArray.value[1].deviceId
					: videoInputDevicesArray.value[0].deviceId
		}
		codeReader.value.stopStreams()
	}

	const check = (val) => {
		loading.value = true
		bizAttendanceApi.bizAttendanceClock({time:text.value,type:val}).then((res) => {
			message.success('Success')
			toggle()
			loading.value = false
			open.value = false
		}).finally(() => {
			loading.value = false
		}).catch(() => {
			loading.value = false
		})
	}

	const cancal = () => {
		toggle()
		loading.value = false
		open.value = false
	}

	const toggle = async () => {
		codeReader.value.stopStreams()
		timer.value = setTimeout(() => {
			timer.value = null
		}, 2000)
		if (timer.value) {
			await codeReader.value.tryPlayVideo('video')
			cutover()
			decodeFromInputVideoFunc()
		}
	}


</script>

<style lang="less" scoped>
	.code-reader-content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.page {
			max-width: 300px;
			height: 300px;
			position: relative;
			#video {
				height: 100%;
				width: 100%;
				object-fit: fill;
			}
			.scan-box {
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				height: 50%;
				width: 50%;
				.frame {
					position: absolute;
					width: 15px;
					height: 15px;
					border: 3px solid transparent;
				}
				.upper-left {
					top: 0;
					left: 0;
					border-left-color: rgba(66, 133, 244, 1);
					border-top-color: rgba(66, 133, 244, 1);
				}
				.upper-right {
					top: 0;
					right: 0;
					border-right-color: rgba(66, 133, 244, 1);
					border-top-color: rgba(66, 133, 244, 1);
				}
				.lower-right {
					bottom: 0;
					right: 0;
					border-bottom-color: rgba(66, 133, 244, 1);
					border-right-color: rgba(66, 133, 244, 1);
				}
				.lower-left {
					bottom: 0;
					left: 0;
					border-left-color: rgba(66, 133, 244, 1);
					border-bottom-color: rgba(66, 133, 244, 1);
				}
				.pointer-box {
					position: absolute;
					top: 0;
					left: 0;
					width: 98%;
					height: 100%;
					overflow: hidden;
					.pointer {
						height: 3px;
						background-image: linear-gradient(to right, transparent 0%, rgba(66, 133, 244, 1) 50%, transparent 100%);
						transform: translateY(-3px);
						animation: move 2s linear infinite;
					}
					@keyframes move {
						0% {
							transform: translateY(-3px);
						}
						100% {
							transform: translateY(calc(20vh - 3px));
						}
					}
				}
				.tip {
					position: absolute;
					left: 50%;
					top: 120%;
					transform: translate(-50%, 0);
					white-space: nowrap;
					color: rgb(85, 209, 28);
					font-size: 16px;
				}
				.btn-switch {
					position: absolute;
					left: 50%;
					top: 140%;
					width: 20px;
					height: 20px;
					transform: translate(-50%, 0);
					background-color: green;
					// background: url('../../../img/icon/switch.svg') no-repeat center;
				}
			}
		}
	}
</style>
