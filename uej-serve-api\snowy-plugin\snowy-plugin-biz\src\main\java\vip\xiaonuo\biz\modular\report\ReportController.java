package vip.xiaonuo.biz.modular.report;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.biz.modular.schedule.service.BizScheduleService;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.service.BizTaskService;
import vip.xiaonuo.biz.modular.taskdatastatistics.entity.BizTaskDataStatistics;
import vip.xiaonuo.biz.modular.taskdatastatistics.service.BizTaskDataStatisticsService;
import vip.xiaonuo.biz.modular.user.entity.BizUser;
import vip.xiaonuo.biz.modular.user.service.BizUserService;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.util.ExcelExportUtil;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class ReportController {

    @Resource
    private BizTaskService bizTaskService;

    @Resource
    private BizUserService bizUserService;

    @Resource
    private BizScheduleService bizScheduleService;

    @Resource
    private BizTaskDataStatisticsService bizTaskDataStatisticsService;

    @Operation(summary = "营业额报表")
    @PostMapping("/biz/report/revenue")
    public CommonResult<Map<String, Double>> revenue(@RequestBody ReportParam param) {
        Map<String, String> resultParam = getParamDate(param.getType(), Convert.toDate(param.getDate()));
        LambdaQueryWrapper<BizTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(param.getOrgId()), BizTask::getOrgId, param.getOrgId());
        queryWrapper.between(BizTask::getStartTime, resultParam.get("begin"), resultParam.get("end"));
        queryWrapper.eq(BizTask::getState, "TASK_STATE_FINISH");
        List<BizTask> bizTasks = bizTaskService.list(queryWrapper);
        Map<String, Double> map = new HashMap<>();
        if (param.getType().equals("week") || param.getType().equals("month")) {
            Map<String, Double> result = bizTasks.stream()
                    .map(o -> {
                        o.setStartTime(o.getStartTime().substring(0, 10));
                        o.setMoney(Convert.toDouble(o.getTotalPrice()));
                        return o;
                    })
                    .collect(Collectors.groupingBy(BizTask::getStartTime,
                            Collectors.summingDouble(BizTask::getMoney)
                    ));
            if (param.getType().equals("week") && result.size() < 7) {
                for (int i = 0; i < 7; i++) {
                    DateTime date = DateUtil.offsetDay(DateUtil.parse(resultParam.get("begin")), i);
                    String day = DateUtil.format(date, "yyyy-MM-dd");
                    map.put(day, result.keySet().contains(day) ? result.get(day) : 0.0);
                }
            }
            if (param.getType().equals("month") && result.size() < Convert.toInt(resultParam.get("totalDay"))) {
                for (int i = 0; i < Convert.toInt(resultParam.get("totalDay")); i++) {
                    DateTime date = DateUtil.offsetDay(DateUtil.parse(resultParam.get("begin")), i);
                    String day = DateUtil.format(date, "yyyy-MM-dd");
                    map.put(day, result.keySet().contains(day) ? result.get(day) : 0.0);
                }
            }
        }
        if (param.getType().equals("quarter") || param.getType().equals("year")) {
            Map<String, Double> result = bizTasks.stream()
                    .map(o -> {
                        o.setStartTime(o.getStartTime().substring(0, 7));
                        o.setMoney(Convert.toDouble(o.getTotalPrice()));
                        return o;
                    })
                    .collect(Collectors.groupingBy(BizTask::getStartTime,
                            Collectors.summingDouble(BizTask::getMoney)
                    ));
            if (param.getType().equals("quarter")) {
                for (int i = 0; i < 3; i++) {
                    DateTime date = DateUtil.offsetMonth(DateUtil.parse(resultParam.get("begin")), i);
                    String day = DateUtil.format(date, "yyyy-MM");
                    map.put(day, result.keySet().contains(day) ? result.get(day) : 0.0);
                }
            }
            if (param.getType().equals("year")) {
                for (int i = 0; i < 12; i++) {
                    DateTime date = DateUtil.offsetMonth(DateUtil.parse(resultParam.get("begin")), i);
                    String day = DateUtil.format(date, "yyyy-MM");
                    map.put(day, result.keySet().contains(day) ? result.get(day) : 0.0);
                }
            }
        }
        return CommonResult.data(map);
    }

    @Operation(summary = "工资报表")
    @PostMapping("/biz/report/staffWage")
    public CommonResult<List<StaffWage>> staffWage(@RequestBody ReportParam param) {
        Map<String, String> resultParam = getParamDate(param.getType(), Convert.toDate(param.getDate()));
        List<StaffWage> results = new ArrayList<>();
        List<BizUser> users = bizUserService.lambdaQuery().isNotNull(BizUser::getWageHour).or().isNotNull(BizUser::getWagePercent).list();
        List<BizSchedule> schedules = bizScheduleService.lambdaQuery()
                .isNotNull(BizSchedule::getWorkTime)
                .eq(ObjectUtil.isNotEmpty(param.getOrgId()), BizSchedule::getOrganizationId, param.getOrgId())
                .between(BizSchedule::getAssignDate, resultParam.get("begin"), resultParam.get("end")).list();
        Map<String, Double> workingHours = schedules.stream().map(o -> {
            o.setWorkingHours(calculateTime(JSONUtil.parseArray(o.getWorkTime()).toArray(new String[0])));
            return o;
        }).collect(Collectors.groupingBy(BizSchedule::getStaffId, Collectors.summingDouble(BizSchedule::getWorkingHours)));

        List<BizTask> tasks = bizTaskService.lambdaQuery().eq(BizTask::getState, "TASK_STATE_FINISH").list();
        Map<String, Double> saleAmount = tasks.stream().map(o -> {
            o.setMoney(Convert.toDouble(o.getTotalPrice()));
            return o;
        }).collect(Collectors.groupingBy(BizTask::getAssign, Collectors.summingDouble(BizTask::getMoney)));

        String date = "from " + resultParam.get("begin") + " to " + resultParam.get("end");
        for (BizUser user : users) {
            StaffWage result = new StaffWage();
            long count = tasks.stream().filter(o -> o.getAssign().equals(user.getId())).count();

            result.setName(user.getName());
            result.setTaskNumber(count);
            result.setDate(date);
            
            // 初始化工资为0
            BigDecimal salary = BigDecimal.ZERO;
            
            // 计算小时工资
            Double workingHour = workingHours.get(user.getId());
            if (ObjectUtil.isNotEmpty(workingHour) && ObjectUtil.isNotEmpty(user.getWageHour()) && !user.getWageHour().equals("-")) {
                salary = salary.add(Convert.toBigDecimal(user.getWageHour()).multiply(Convert.toBigDecimal(workingHour)));
            }
            
            // 计算提成工资
            Double sale = saleAmount.get(user.getId());
            if (ObjectUtil.isNotEmpty(sale) && ObjectUtil.isNotEmpty(user.getWagePercent()) && !user.getWagePercent().equals("-")) {
                salary = salary.add(Convert.toBigDecimal(user.getWagePercent()).multiply(Convert.toBigDecimal(sale)));
            }
            
            result.setSalary(salary);
            
            // 设置tips字段
            if (count > 0) {
                result.setTips("Normal");
            } else {
                result.setTips("No Tasks");
            }
            
            results.add(result);
        }
        return CommonResult.data(results);
    }

    @Operation(summary = "工作效率报表")
    @PostMapping("/biz/report/staffEfficiency")
    public CommonResult<List<StaffEfficiency>> staffEfficiency(@RequestBody ReportParam param) {
        Map<String, String> resultParam = getParamDate(param.getType(), Convert.toDate(param.getDate()));
        List<StaffEfficiency> results = new ArrayList<>();
        List<BizTaskDataStatistics> statistics = bizTaskDataStatisticsService.lambdaQuery()
                .eq(BizTaskDataStatistics::getStatus, "TASK_STATE_FINISH")
                .isNull(BizTaskDataStatistics::getParentId)
                .eq(ObjectUtil.isNotEmpty(param.getOrgId()), BizTaskDataStatistics::getOrgId, param.getOrgId())
                .between(BizTaskDataStatistics::getStartTime, resultParam.get("begin"), resultParam.get("end")).list();

        Map<String, Double> actualHourTotal = statistics.stream()
                .collect(Collectors.groupingBy(BizTaskDataStatistics::getStaffId,
                        Collectors.summingDouble(o -> ObjectUtil.isEmpty(o.getActualHours()) ? 0 : Double.parseDouble(o.getActualHours()))));

        Map<String, Double> planHourTotal = statistics.stream()
                .collect(Collectors.groupingBy(BizTaskDataStatistics::getStaffId,
                        Collectors.summingDouble(o -> ObjectUtil.isEmpty(o.getPlanningHours()) ? 0 : Double.parseDouble(o.getPlanningHours()))));

        String date = "from " + resultParam.get("begin") + " to " + resultParam.get("end");
        for (String staffId : actualHourTotal.keySet()) {
            StaffEfficiency result = new StaffEfficiency();
            if (planHourTotal.containsKey(staffId) && planHourTotal.get(staffId) != 0 && actualHourTotal.get(staffId) != 0) {
                String staff = statistics.stream().filter(o -> o.getStaffId().equals(staffId)).toList().get(0).getStaff();
                long taskNumber = statistics.stream().filter(o -> o.getStaffId().equals(staffId)).count();
                Double actualHours = actualHourTotal.get(staffId);
                Double planHours = planHourTotal.get(staffId);
                
                // 修复效率计算：效率 = (计划工时 / 实际工时) * 100%
                // 如果实际工时少于计划工时，效率>100%（高效）
                // 如果实际工时多于计划工时，效率<100%（低效）
                BigDecimal percent = new BigDecimal(planHours).divide(new BigDecimal(actualHours), 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                
                result.setName(staff);
                result.setDate(date);
                result.setTaskNumber(taskNumber);
                result.setTotalTime(actualHours);
                result.setEfficiency(percent.setScale(2, BigDecimal.ROUND_HALF_UP) + "%");
                results.add(result);
            }
        }
        return CommonResult.data(results);
    }

    @Operation(summary = "导出")
    @PostMapping("/biz/report/exportStaffEfficiency")
    public void exportStaffEfficiency(@RequestBody ReportParam param, HttpServletResponse response) {
        List<StaffEfficiency> list = param.getEfficiency();
        if(list.isEmpty()) {
            throw new CommonException("No Date");
        }
        ExcelExportUtil.exportExcel(list, StaffEfficiency.class, response);
    }

    @Operation(summary = "导出")
    @PostMapping("/biz/report/exportStaffWage")
    public void exportStaffWage(@RequestBody ReportParam param, HttpServletResponse response) {
        List<StaffWage> list = param.getWage();
        if(list.isEmpty()) {
            throw new CommonException("No Date");
        }
        ExcelExportUtil.exportExcel(list, StaffWage.class, response);
    }

    @Operation(summary = "日收入明细")
    @PostMapping("/biz/report/dailyDetail")
    public CommonResult<Map<String, BigDecimal>> dailyDetail(@RequestBody ReportParam param) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        BigDecimal cash = new BigDecimal(0);
        BigDecimal card = new BigDecimal(0);
        BigDecimal giftCard = new BigDecimal(0);
        BigDecimal voucher = new BigDecimal(0);
        BigDecimal insurance = new BigDecimal(0);

        List<BizTask> bizTasks = bizTaskService.lambdaQuery()
                .eq(BizTask::getState, "TASK_STATE_FINISH")
                .eq(ObjectUtil.isNotEmpty(param.getOrgId()), BizTask::getOrgId, param.getOrgId())
                .like(BizTask::getStartTime, param.getDate()).list();
        for (BizTask bizTask : bizTasks) {
            cash = cash.add(ObjectUtil.isEmpty(bizTask.getCash()) ? BigDecimal.ZERO : new BigDecimal(bizTask.getCash()));
            card = card.add(ObjectUtil.isEmpty(bizTask.getCard()) ? BigDecimal.ZERO : new BigDecimal(bizTask.getCard()));
            giftCard = giftCard.add(ObjectUtil.isEmpty(bizTask.getGiftCard()) ? BigDecimal.ZERO : new BigDecimal(bizTask.getGiftCard()));
            voucher = voucher.add(ObjectUtil.isEmpty(bizTask.getVoucher()) ? BigDecimal.ZERO : new BigDecimal(bizTask.getVoucher()));
            insurance = insurance.add(ObjectUtil.isEmpty(bizTask.getInsuranceValue()) ? BigDecimal.ZERO : new BigDecimal(bizTask.getInsuranceValue()));
        }
        resultMap.put("cash", cash);
        resultMap.put("card", card);
        resultMap.put("giftCard", giftCard);
        resultMap.put("voucher", voucher);
        resultMap.put("insurance", insurance);
        return CommonResult.data(resultMap);
    }

    private static Map<String, String> getParamDate(String type, Date date) {
        Map<String, String> result = new HashMap<>();
        if (type.equals("week")) {
            result.put("begin", DateUtil.beginOfWeek(date, true).toString());
            result.put("end", DateUtil.endOfWeek(date, true).toString());
        }
        if (type.equals("twoWeek")) {
            DateTime dateTime = DateUtil.beginOfWeek(date, true);
            result.put("begin", DateUtil.format(DateUtil.offsetDay(dateTime, -15), "yyyy-MM-dd"));
            result.put("end", DateUtil.format(DateUtil.offsetDay(dateTime, -1), "yyyy-MM-dd"));
        }
        if (type.equals("month")) {
            result.put("begin", DateUtil.beginOfMonth(date).toString());
            result.put("end", DateUtil.endOfMonth(date).toString());
            result.put("totalDay", Convert.toStr(DateUtil.endOfMonth(date).dayOfMonth()));
        }
        if (type.equals("quarter")) {
            result.put("begin", DateUtil.beginOfQuarter(date).toString());
            result.put("end", DateUtil.endOfQuarter(date).toString());
        }
        if (type.equals("year")) {
            result.put("begin", DateUtil.beginOfYear(date).toString());
            result.put("end", DateUtil.endOfYear(date).toString());
        }
        return result;
    }

    public static double calculateTime(String[] timeRange) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        LocalTime startTime = LocalTime.parse(timeRange[0], formatter);
        LocalTime endTime = LocalTime.parse(timeRange[1], formatter);
        Duration duration = Duration.between(startTime, endTime);
        return Convert.toDouble(duration.toHours());
    }
}
