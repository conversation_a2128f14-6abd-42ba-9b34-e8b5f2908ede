
package vip.xiaonuo.flw.modular.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 模型Id参数
 *
 * <AUTHOR>
 * @date 2022/7/31 17:55
 */
@Getter
@Setter
public class FlwModelIdParam {

    /** id */
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;
}
