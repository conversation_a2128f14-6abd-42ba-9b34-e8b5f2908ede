
package vip.xiaonuo.biz.modular.offeringgroupitem.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 套餐明细编辑参数
 *
 * <AUTHOR>
 * @date  2024/06/13 09:58
 **/
@Getter
@Setter
public class BizOfferingGroupItemEditParam {

    /** 主键 */
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;

    @Schema(description = "Offering Group ID")
    private String mainId;

    /** OFFERING_ID */
    @Schema(description = "OFFERING_ID")
    private String offeringId;

    /** OFFERING_NAME */
    @Schema(description = "OFFERING_NAME")
    private String offeringName;

    /** LIST PRICE */
    @Schema(description = "LIST PRICE")
    private String listPrice;

    /** PLANNING HOURS */
    @Schema(description = "PLANNING HOURS")
    private String planningHours;

    /** NUM */
    @Schema(description = "NUM")
    private String num;

    /** TOTAL PRICE */
    @Schema(description = "TOTAL PRICE")
    private String totalPrice;

}
