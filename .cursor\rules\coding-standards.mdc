---
description:
globs:
alwaysApply: false
---
# 编码规范与工具配置

## 代码风格与格式化

项目使用以下工具进行代码格式化和规范检查：

- ESLint - 用于JavaScript/Vue代码检查
  - 配置文件：[.eslintrc.js](mdc:uej-serve-web/.eslintrc.js)
- Prettier - 用于代码格式化
  - 配置文件：[prettier.config.js](mdc:uej-serve-web/prettier.config.js)
- EditorConfig - 用于编辑器通用配置
  - 配置文件：[.editorconfig](mdc:uej-serve-web/.editorconfig)

## 提交规范

代码提交时应遵循以下规范：
- 提交信息应简洁明了，描述本次提交的主要内容
- 提交前应确保代码通过ESLint检查
- 避免提交不必要的文件，参考 [.gitignore](mdc:uej-serve-web/.gitignore)

## 命名规范

### 通用命名规范

- 使用有意义且能准确表达意图的名称
- 避免使用拼音、缩写和特殊字符
- 使用英文作为命名语言

### 前端命名规范

- 组件名：使用PascalCase (如 UserProfile.vue)
- JS变量和函数：使用camelCase (如 getUserInfo)
- CSS类名：使用kebab-case (如 user-profile)
- 常量：使用SNAKE_CASE (如 MAX_COUNT)

### 后端命名规范

- Java类名：使用PascalCase (如 UserService)
- 方法和变量名：使用camelCase (如 getUserById)
- 常量：使用SNAKE_CASE (如 MAX_CONNECTION_COUNT)
- 包名：全小写 (如 com.example.service)
