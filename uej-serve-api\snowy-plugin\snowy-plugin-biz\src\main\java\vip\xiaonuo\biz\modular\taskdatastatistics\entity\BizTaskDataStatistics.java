
package vip.xiaonuo.biz.modular.taskdatastatistics.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务数据统计实体
 *
 * <AUTHOR>
 * @date  2024/06/13 15:48
 **/
@Getter
@Setter
@TableName("biz_task_data_statistics")
public class BizTaskDataStatistics {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** 主表ID(task表) */
    @Schema(description = "主表ID(task表)")
    private String mainId;

    @Schema(description = "父级ID")
    private String parentId;

    private String taskName;

    /** Status */
    @Schema(description = "Status")
    private String status;

    /** Start Time */
    @Schema(description = "Start Time")
    private String startTime;

    @Schema(description = "Pause Time")
    private String pauseTime;

    @Schema(description = "Planning End Time")
    private String planningEndTime;

    /** Planning Hours */
    @Schema(description = "Planning Hours")
    private String planningHours;

    /** Actual Hours */
    @Schema(description = "Actual Hours")
    private String actualHours;

    @Schema(description = "任务完成进度")
    private String progress;

    private String staff;

    private String staffId;

    private String orgId;

    private String product;

    private String remark;

    @Schema(description = "会员id")
    private String customerId;

    @Schema(description = "会员姓名")
    private String customerName;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String extJson;

    @TableField(exist = false)
    private List<BizTaskItem> bizTaskItemList;

    @TableField(exist = false)
    private String voucher;

    @TableField(exist = false)
    private String giftCard;

    @TableField(exist = false)
    private String cash;

    @TableField(exist = false)
    private String card;

    @TableField(exist = false)
    private BigDecimal paid;

    @TableField(exist = false)
    private BigDecimal paidAll;

    @TableField(exist = false)
    private double insurance;

    @TableField(exist = false)
    private String tip;
}
