package vip.xiaonuo.biz.modular.task.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 任务礼品卡关联表实体
 *
 * <AUTHOR>
 * @date  2024/12/24
 **/
@Getter
@Setter
@TableName("biz_task_gift_card")
public class BizTaskGiftCard {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** 任务ID */
    @Schema(description = "任务ID")
    private String taskId;

    /** 礼品卡号 */
    @Schema(description = "礼品卡号")
    private String cardNo;

    /** 卡密 */
    @Schema(description = "卡密")
    private String pin;

    /** 使用金额 */
    @Schema(description = "使用金额")
    private double amount;

    /** 卡总余额 */
    @Schema(description = "卡总余额")
    private double balance;

    /** 可用余额 */
    @Schema(description = "可用余额")
    private double availableBalance;

    /** 是否已验证 */
    @Schema(description = "是否已验证")
    private boolean verified;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;
} 