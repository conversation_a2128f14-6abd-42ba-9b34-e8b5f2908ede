
package vip.xiaonuo.biz.modular.offering.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.offering.entity.BizOffering;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingAddParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingEditParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingIdParam;
import vip.xiaonuo.biz.modular.offering.param.BizOfferingPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 服务或产品清单Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 16:09
 **/
public interface BizOfferingService extends IService<BizOffering> {

    /**
     * 获取服务或产品清单分页
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    Page<BizOffering> page(BizOfferingPageParam bizOfferingPageParam);

    /**
     * 添加服务或产品清单
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    void add(BizOfferingAddParam bizOfferingAddParam);

    /**
     * 编辑服务或产品清单
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    void edit(BizOfferingEditParam bizOfferingEditParam);

    /**
     * 删除服务或产品清单
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    void delete(List<BizOfferingIdParam> bizOfferingIdParamList);

    /**
     * 获取服务或产品清单详情
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     */
    BizOffering detail(BizOfferingIdParam bizOfferingIdParam);

    /**
     * 获取服务或产品清单详情
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
     **/
    BizOffering queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 16:09
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
