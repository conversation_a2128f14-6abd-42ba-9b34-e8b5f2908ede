
package vip.xiaonuo.biz.modular.insurancecompany.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 保险公司Id参数
 *
 * <AUTHOR>
 * @date  2024/08/09 09:42
 **/
@Getter
@Setter
public class BizInsuranceCompanyIdParam {

    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;
}
