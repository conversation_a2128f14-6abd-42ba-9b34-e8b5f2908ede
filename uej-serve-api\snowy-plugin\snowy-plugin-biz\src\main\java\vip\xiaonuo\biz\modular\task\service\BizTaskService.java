
package vip.xiaonuo.biz.modular.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.param.BizTaskAddParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskEditParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskIdParam;
import vip.xiaonuo.biz.modular.task.param.BizTaskPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 任务信息Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 15:42
 **/
public interface BizTaskService extends IService<BizTask> {

    /**
     * 获取任务信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    Page<BizTask> page(BizTaskPageParam bizTaskPageParam);

    /**
     * 添加任务信息
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    void add(BizTaskAddParam bizTaskAddParam);

    /**
     * 编辑任务信息
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    void edit(BizTaskEditParam bizTaskEditParam);

    /**
     * 删除任务信息
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    void delete(List<BizTaskIdParam> bizTaskIdParamList);

    /**
     * 获取任务信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     */
    BizTask detail(BizTaskIdParam bizTaskIdParam);

    /**
     * 获取任务信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
     **/
    BizTask queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 15:42
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
