package vip.xiaonuo.biz.modular.workhours.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.workhours.entity.BizWorkHours;
import vip.xiaonuo.biz.modular.workhours.mapper.BizWorkHoursMapper;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursAddParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursEditParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursIdParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursPageParam;
import vip.xiaonuo.biz.modular.workhours.service.BizWorkHoursService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;

import java.util.List;

/**
 * 工时Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 17:27
 **/
@Service
public class BizWorkHoursServiceImpl extends ServiceImpl<BizWorkHoursMapper, BizWorkHours> implements BizWorkHoursService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizWorkHours> page(BizWorkHoursPageParam bizWorkHoursPageParam) {
        QueryWrapper<BizWorkHours> queryWrapper = new QueryWrapper<BizWorkHours>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizWorkHoursPageParam.getName())) {
            queryWrapper.lambda().like(BizWorkHours::getName, bizWorkHoursPageParam.getName());
        }
        if(ObjectUtil.isAllNotEmpty(bizWorkHoursPageParam.getSortField(), bizWorkHoursPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizWorkHoursPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizWorkHoursPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizWorkHoursPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizWorkHours::getId);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizWorkHours::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizWorkHours::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizWorkHours::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizWorkHoursAddParam bizWorkHoursAddParam) {
        BizWorkHours bizWorkHours = BeanUtil.toBean(bizWorkHoursAddParam, BizWorkHours.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizWorkHours.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizWorkHours.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizWorkHours);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizWorkHoursEditParam bizWorkHoursEditParam) {
        BizWorkHours bizWorkHours = this.queryEntity(bizWorkHoursEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查工时记录所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizWorkHours.getOrgId()) && !loginUserDataScope.contains(bizWorkHours.getOrgId())) {
                throw new CommonException("您没有权限编辑该工时记录，记录id：{}", bizWorkHours.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizWorkHours.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该工时记录，记录id：{}", bizWorkHours.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该工时记录，记录id：{}", bizWorkHours.getId());
            }
        }
        
        BeanUtil.copyProperties(bizWorkHoursEditParam, bizWorkHours);
        this.updateById(bizWorkHours);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizWorkHoursIdParam> bizWorkHoursIdParamList) {
        List<String> workHoursIdList = CollStreamUtil.toList(bizWorkHoursIdParamList, BizWorkHoursIdParam::getId);
        if(ObjectUtil.isNotEmpty(workHoursIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizWorkHours> workHoursList = this.listByIds(workHoursIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查工时记录所属机构是否在权限范围内
                for(BizWorkHours workHours : workHoursList) {
                    if(ObjectUtil.isNotEmpty(workHours.getOrgId()) && !loginUserDataScope.contains(workHours.getOrgId())) {
                        throw new CommonException("您没有权限删除该工时记录，记录id：{}", workHours.getId());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizWorkHours workHours : workHoursList) {
                        if(!workHours.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该工时记录，记录id：{}", workHours.getId());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除工时记录");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(workHoursIdList);
    }

    @Override
    public BizWorkHours detail(BizWorkHoursIdParam bizWorkHoursIdParam) {
        BizWorkHours bizWorkHours = this.queryEntity(bizWorkHoursIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查工时记录所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizWorkHours.getOrgId()) && !loginUserDataScope.contains(bizWorkHours.getOrgId())) {
                throw new CommonException("您没有权限查看该工时记录，记录id：{}", bizWorkHours.getId());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizWorkHours.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该工时记录，记录id：{}", bizWorkHours.getId());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该工时记录，记录id：{}", bizWorkHours.getId());
            }
        }
        
        return bizWorkHours;
    }

    @Override
    public BizWorkHours queryEntity(String id) {
        BizWorkHours bizWorkHours = this.getById(id);
        if(ObjectUtil.isEmpty(bizWorkHours)) {
            throw new CommonException("工时不存在，id值为：{}", id);
        }
        return bizWorkHours;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizWorkHoursServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizWorkHours.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}

