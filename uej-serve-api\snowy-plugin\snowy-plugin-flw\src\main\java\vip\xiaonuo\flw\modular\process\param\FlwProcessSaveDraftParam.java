
package vip.xiaonuo.flw.modular.process.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 保存草稿参数
 *
 * <AUTHOR>
 * @date 2022/8/1 10:36
 */
@Getter
@Setter
public class FlwProcessSaveDraftParam {

    /** 模型id */
    @Schema(description = "模型id")
    @NotBlank(message = "modelId不能为空")
    private String modelId;

    /** 填写的数据 */
    @Schema(description = "填写的数据")
    @NotBlank(message = "dataJson不能为空")
    private String dataJson;

    /** 草稿id */
    @Schema(description = "草稿id")
    private String id;
}
