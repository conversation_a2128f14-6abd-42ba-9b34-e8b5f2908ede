package vip.xiaonuo.biz.modular.productcategory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import vip.xiaonuo.biz.modular.productcategory.entity.BizProductCategory;
import vip.xiaonuo.biz.modular.productcategory.mapper.BizProductCategoryMapper;
import vip.xiaonuo.biz.modular.productcategory.param.*;
import vip.xiaonuo.biz.modular.productcategory.service.BizProductCategoryService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 产品类目Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/12/19
 **/
@Service
public class BizProductCategoryServiceImpl extends ServiceImpl<BizProductCategoryMapper, BizProductCategory> implements BizProductCategoryService {

    private static final String PRODUCT_CATEGORY = "BIZ";

    @Override
    public Page<BizProductCategory> page(BizProductCategoryPageParam bizProductCategoryPageParam) {
        QueryWrapper<BizProductCategory> queryWrapper = new QueryWrapper<BizProductCategory>().checkSqlInjection();
        // 查询部分字段
        queryWrapper.lambda().select(BizProductCategory::getId, BizProductCategory::getParentId, BizProductCategory::getCategory, BizProductCategory::getDictLabel,
                BizProductCategory::getDictValue, BizProductCategory::getSortCode).eq(BizProductCategory::getCategory, PRODUCT_CATEGORY);
        if (ObjectUtil.isNotEmpty(bizProductCategoryPageParam.getParentId())) {
            queryWrapper.lambda().and(q -> q.eq(BizProductCategory::getParentId, bizProductCategoryPageParam.getParentId())
                    .or().eq(BizProductCategory::getId, bizProductCategoryPageParam.getParentId()));
        }
        if (ObjectUtil.isNotEmpty(bizProductCategoryPageParam.getSearchKey())) {
            queryWrapper.lambda().like(BizProductCategory::getDictLabel, bizProductCategoryPageParam.getSearchKey());
        }
        if (ObjectUtil.isAllNotEmpty(bizProductCategoryPageParam.getSortField(), bizProductCategoryPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizProductCategoryPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizProductCategoryPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizProductCategoryPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizProductCategory::getSortCode);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizProductCategory::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizProductCategory::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizProductCategory::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<Tree<String>> tree() {
        // 1. 查根节点（不加权限）
        LambdaQueryWrapper<BizProductCategory> rootQuery = new LambdaQueryWrapper<>();
        rootQuery.eq(BizProductCategory::getCategory, PRODUCT_CATEGORY)
                 .eq(BizProductCategory::getDictValue, "PRODUCT_TYPE")
                 .eq(BizProductCategory::getParentId, "0");
        BizProductCategory root = this.getOne(rootQuery);
        if (root == null) {
            // 没有根节点直接返回空
            return List.of();
        }

        // 2. 查有权限的子节点
        LambdaQueryWrapper<BizProductCategory> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(BizProductCategory::getCategory, PRODUCT_CATEGORY)
                  .ne(BizProductCategory::getParentId, "0")
                  .orderByAsc(BizProductCategory::getSortCode);

        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        if (ObjectUtil.isNotEmpty(loginUserDataScope)) {
            childQuery.in(BizProductCategory::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                childQuery.eq(BizProductCategory::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                childQuery.eq(BizProductCategory::getId, "IMPOSSIBLE_ID");
            }
        }
        List<BizProductCategory> children = this.list(childQuery);

        // 3. 组装树
        List<TreeNode<String>> treeNodeList = new java.util.ArrayList<>();
        treeNodeList.add(new TreeNode<>(root.getId(), root.getParentId(), root.getDictLabel(), root.getSortCode()).setExtra(JSONUtil.parseObj(root)));
        treeNodeList.addAll(children.stream().map(bizProductCategory ->
                new TreeNode<>(bizProductCategory.getId(), bizProductCategory.getParentId(),
                        bizProductCategory.getDictLabel(), bizProductCategory.getSortCode()).setExtra(JSONUtil.parseObj(bizProductCategory)))
                .collect(Collectors.toList()));
        List<Tree<String>> fullTree = TreeUtil.build(treeNodeList, "0");
        return fullTree;
    }

    @Override
    public void add(BizProductCategoryAddParam bizProductCategoryAddParam) {
        checkParam(bizProductCategoryAddParam.getDictValue(), bizProductCategoryAddParam.getParentId(), null);
        BizProductCategory bizProductCategory = BeanUtil.toBean(bizProductCategoryAddParam, BizProductCategory.class);
        bizProductCategory.setCategory(PRODUCT_CATEGORY);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizProductCategory.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizProductCategory.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizProductCategory);
    }

    @Override
    public void edit(BizProductCategoryEditParam bizProductCategoryEditParam) {
        BizProductCategory bizProductCategory = this.queryEntity(bizProductCategoryEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查产品分类所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizProductCategory.getOrgId()) && !loginUserDataScope.contains(bizProductCategory.getOrgId())) {
                throw new CommonException("您没有权限编辑该产品分类，分类名称：{}", bizProductCategory.getDictLabel());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizProductCategory.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该产品分类，分类名称：{}", bizProductCategory.getDictLabel());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该产品分类，分类名称：{}", bizProductCategory.getDictLabel());
            }
        }
        
        checkParam(bizProductCategoryEditParam.getDictValue(), bizProductCategoryEditParam.getParentId(), bizProductCategoryEditParam.getId());
        BeanUtil.copyProperties(bizProductCategoryEditParam, bizProductCategory);
        bizProductCategory.setCategory(PRODUCT_CATEGORY);
        this.updateById(bizProductCategory);
    }

    @Override
    public void delete(List<BizProductCategoryIdParam> bizProductCategoryIdParamList) {
        List<String> bizProductCategoryIdList = bizProductCategoryIdParamList.stream().map(BizProductCategoryIdParam::getId).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(bizProductCategoryIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizProductCategory> productCategoryList = this.listByIds(bizProductCategoryIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查产品分类所属机构是否在权限范围内
                for(BizProductCategory productCategory : productCategoryList) {
                    if(ObjectUtil.isNotEmpty(productCategory.getOrgId()) && !loginUserDataScope.contains(productCategory.getOrgId())) {
                        throw new CommonException("您没有权限删除该产品分类，分类名称：{}", productCategory.getDictLabel());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizProductCategory productCategory : productCategoryList) {
                        if(!productCategory.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该产品分类，分类名称：{}", productCategory.getDictLabel());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除产品分类");
                }
            }
            
            // 检查是否有下级，有下级不能删除
            bizProductCategoryIdList.forEach(this::checkChildrenById);
            this.removeByIds(bizProductCategoryIdList);
        }
    }

    @Override
    public BizProductCategory detail(BizProductCategoryIdParam bizProductCategoryIdParam) {
        BizProductCategory bizProductCategory = this.queryEntity(bizProductCategoryIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查产品分类所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizProductCategory.getOrgId()) && !loginUserDataScope.contains(bizProductCategory.getOrgId())) {
                throw new CommonException("您没有权限查看该产品分类，分类名称：{}", bizProductCategory.getDictLabel());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizProductCategory.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该产品分类，分类名称：{}", bizProductCategory.getDictLabel());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该产品分类，分类名称：{}", bizProductCategory.getDictLabel());
            }
        }
        
        return bizProductCategory;
    }

    @Override
    public BizProductCategory queryEntity(String id) {
        BizProductCategory bizProductCategory = this.getById(id);
        if (ObjectUtil.isEmpty(bizProductCategory)) {
            throw new CommonException("产品类目不存在，id值为：{}", id);
        }
        return bizProductCategory;
    }

    /**
     * 检查参数
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    private void checkParam(String dictValue, String parentId, String id) {
        LambdaQueryWrapper<BizProductCategory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BizProductCategory::getCategory, PRODUCT_CATEGORY)
                .eq(BizProductCategory::getDictValue, dictValue)
                .eq(BizProductCategory::getParentId, parentId);
        // 新增：同一组织下唯一
        try {
            String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
            if (ObjectUtil.isNotEmpty(currentUserOrgId)) {
                lambdaQueryWrapper.eq(BizProductCategory::getOrgId, currentUserOrgId);
            }
        } catch (Exception e) {
            // 获取用户信息失败，继续执行但不加orgId条件
        }
        if (ObjectUtil.isNotEmpty(id)) {
            lambdaQueryWrapper.ne(BizProductCategory::getId, id);
        }
        boolean exists = this.count(lambdaQueryWrapper) > 0;
        if (exists) {
            throw new CommonException("同一组织下存在重复的产品类目编码：{}", dictValue);
        }
    }

    /**
     * 检查是否有下级
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    private void checkChildrenById(String id) {
        LambdaQueryWrapper<BizProductCategory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BizProductCategory::getCategory, PRODUCT_CATEGORY).eq(BizProductCategory::getParentId, id);
        boolean hasChildren = this.count(lambdaQueryWrapper) > 0;
        if (hasChildren) {
            throw new CommonException("产品类目：{}下有子类目，不可删除", this.getById(id).getDictLabel());
        }
    }
}

