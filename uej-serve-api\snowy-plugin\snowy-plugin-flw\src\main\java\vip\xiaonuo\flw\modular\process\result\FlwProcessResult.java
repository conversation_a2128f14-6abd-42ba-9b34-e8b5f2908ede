
package vip.xiaonuo.flw.modular.process.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程结果
 *
 * <AUTHOR>
 * @date 2022/5/11 15:51
 **/
@Getter
@Setter
public class FlwProcessResult {

    /** id */
    @Schema(description = "id")
    private String id;

    /** 流水号 */
    @Schema(description = "流水号")
    private String sn;

    /** 标题 */
    @Schema(description = "标题")
    private String title;

    /** 摘要 */
    @Schema(description = "摘要")
    private String abstractTitle;

    /** 业务主键 */
    @Schema(description = "业务主键")
    private String businessKey;

    /** 流程定义id */
    @Schema(description = "流程定义id")
    private String processDefinitionId;

    /** 流程定义名称 */
    @Schema(description = "流程定义名称")
    private String processDefinitionName;

    /** 流程定义版本 */
    @Schema(description = "流程定义版本")
    private String processDefinitionVersion;

    /** 发起时间 */
    @Schema(description = "发起时间")
    private String startTime;

    /** 结束时间 */
    @Schema(description = "结束时间")
    private String endTime;

    /** 耗时 */
    @Schema(description = "耗时")
    private String durationInfo;

    /** 发起人id */
    @Schema(description = "发起人id")
    private String initiator;

    /** 发起人姓名 */
    @Schema(description = "发起人姓名")
    private String initiatorName;

    /** 发起人组织id */
    @Schema(description = "发起人组织id")
    private String initiatorOrgId;

    /** 发起人组织名称 */
    @Schema(description = "发起人组织名称")
    private String initiatorOrgName;

    /** 发起人职位id */
    @Schema(description = "发起人职位id")
    private String initiatorPositionId;

    /** 发起人职位名称 */
    @Schema(description = "发起人职位名称")
    private String initiatorPositionName;

    /** 当前节点 */
    @Schema(description = "当前节点")
    private String currentActivityNames;

    /** 当前办理人 */
    @Schema(description = "当前办理人")
    private String assignees;

    /** 状态值 */
    @Schema(description = "状态值")
    private String stateText;

    /** 状态码 */
    @Schema(description = "状态码")
    private String stateCode;
}
