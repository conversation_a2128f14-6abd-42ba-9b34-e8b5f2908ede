
package vip.xiaonuo.biz.modular.offering.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 服务或产品清单实体
 *
 * <AUTHOR>
 * @date  2024/06/12 16:09
 **/
@Getter
@Setter
@TableName("biz_offering")
public class BizOffering {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** Category */
    @Schema(description = "Category")
    private String category;

    /** Name */
    @Schema(description = "Name")
    private String name;

    /** Planning Hours */
    @Schema(description = "Planning Hours")
    private String planningHours;

    /** Invoice Hours */
    @Schema(description = "Invoice Hours")
    private String invoiceHours;

    /** List Price */
    @Schema(description = "List Price")
    private String listPrice;

    /** Cost Price */
    @Schema(description = "Cost Price")
    private String costPrice;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 组织id */
    @Schema(description = "组织id")
    private String orgId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String typeId;

    private String type;

    private String extJson;
}
