
package vip.xiaonuo.biz.modular.taskitem.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemAddParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemEditParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemIdParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 任务子表Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 17:34
 **/
public interface BizTaskItemService extends IService<BizTaskItem> {

    /**
     * 获取任务子表分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    Page<BizTaskItem> page(BizTaskItemPageParam bizTaskItemPageParam);

    /**
     * 添加任务子表
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    void add(BizTaskItemAddParam bizTaskItemAddParam);

    /**
     * 编辑任务子表
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    void edit(BizTaskItemEditParam bizTaskItemEditParam);

    /**
     * 删除任务子表
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    void delete(List<BizTaskItemIdParam> bizTaskItemIdParamList);

    /**
     * 获取任务子表详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     */
    BizTaskItem detail(BizTaskItemIdParam bizTaskItemIdParam);

    /**
     * 获取任务子表详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
     **/
    BizTaskItem queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:34
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
