<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5zbm93eS1wbHVnaW4tYXV0aDwvaWQ+PGNsYXNzcGF0aD48ZGlyIG5hbWU9IkQ6L2xvY2FsL3Vlai1zZXJ2ZS1hcGkvc25vd3ktcGx1Z2luL3Nub3d5LXBsdWdpbi1hdXRoL3RhcmdldC9jbGFzc2VzIj48L2Rpcj48L2NsYXNzcGF0aD48L2FwcGxpY2F0aW9uPg==" />
          </map>
        </option>
        <option name="version" value="5" />
      </configuration>
    </facet>
  </component>
</module>