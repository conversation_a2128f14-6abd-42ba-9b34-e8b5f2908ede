
package vip.xiaonuo.biz.modular.scheduledate.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.scheduledate.entity.BizScheduleDate;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateAddParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateEditParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDateIdParam;
import vip.xiaonuo.biz.modular.scheduledate.param.BizScheduleDatePageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 排班表当日属性Service接口
 *
 * <AUTHOR>
 * @date  2024/06/18 16:34
 **/
public interface BizScheduleDateService extends IService<BizScheduleDate> {

    /**
     * 获取排班表当日属性分页
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    Page<BizScheduleDate> page(BizScheduleDatePageParam bizScheduleDatePageParam);

    /**
     * 添加排班表当日属性
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    void add(BizScheduleDateAddParam bizScheduleDateAddParam);

    /**
     * 编辑排班表当日属性
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    void edit(BizScheduleDateEditParam bizScheduleDateEditParam);

    /**
     * 删除排班表当日属性
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    void delete(List<BizScheduleDateIdParam> bizScheduleDateIdParamList);

    /**
     * 获取排班表当日属性详情
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     */
    BizScheduleDate detail(BizScheduleDateIdParam bizScheduleDateIdParam);

    /**
     * 获取排班表当日属性详情
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
     **/
    BizScheduleDate queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/18 16:34
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
