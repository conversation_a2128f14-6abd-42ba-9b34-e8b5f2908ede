package vip.xiaonuo.biz.modular.giftcarddetail.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.giftcarddetail.entity.BizGiftCardDetail;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailAddParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailEditParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailIdParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailPageParam;
import vip.xiaonuo.biz.modular.giftcarddetail.param.BizGiftCardDetailChangeStatusParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 礼品卡信息Service接口
 *
 * <AUTHOR>
 * @date  2024/07/01 18:08
 **/
public interface BizGiftCardDetailService extends IService<BizGiftCardDetail> {

    /**
     * 获取礼品卡信息分页
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    Page<BizGiftCardDetail> page(BizGiftCardDetailPageParam bizGiftCardDetailPageParam);

    /**
     * 添加礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    void add(BizGiftCardDetailAddParam bizGiftCardDetailAddParam);

    /**
     * 编辑礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    void edit(BizGiftCardDetailEditParam bizGiftCardDetailEditParam);

    /**
     * 删除礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    void delete(List<BizGiftCardDetailIdParam> bizGiftCardDetailIdParamList);

    /**
     * 获取礼品卡信息详情
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     */
    BizGiftCardDetail detail(BizGiftCardDetailIdParam bizGiftCardDetailIdParam);

    /**
     * 获取礼品卡信息详情
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
     **/
    BizGiftCardDetail queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/07/01 18:08
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);

    /** 核销礼品卡 */
    String redeemGiftCard(String cardNo, String pin, String usedAmount);

    /** 恢复礼品卡余额 */
    String restoreGiftCardBalance(String cardNo, String pin, String restoreAmount);

    /**
     * 修改礼品卡激活状态
     *
     * <AUTHOR>
     * @date  2024/12/19
     */
    void changeActivationStatus(BizGiftCardDetailChangeStatusParam bizGiftCardDetailChangeStatusParam);
}
