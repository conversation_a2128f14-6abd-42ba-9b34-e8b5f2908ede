
package vip.xiaonuo.biz.modular.attendance.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤记录编辑参数
 *
 * <AUTHOR>
 * @date  2024/08/01 13:54
 **/
@Getter
@Setter
public class BizAttendanceEditParam {

    /** 主键 */
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** Staff ID */
    @Schema(description = "Staff ID")
    private String staffId;

    /** Staff Name */
    @Schema(description = "Staff Name")
    private String staffName;

    /** Org ID */
    @Schema(description = "Org ID")
    private String orgId;

    /** Org Name */
    @Schema(description = "Org Name")
    private String orgName;

    /** Work Time */
    @Schema(description = "Work Time")
    private Date workTime;

    /** Closing Time */
    @Schema(description = "Closing Time")
    private Date closingTime;

}
