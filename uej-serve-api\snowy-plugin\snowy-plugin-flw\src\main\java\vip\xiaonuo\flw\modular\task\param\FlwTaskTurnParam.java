
package vip.xiaonuo.flw.modular.task.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 审批转办参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:45
 */
@Getter
@Setter
public class FlwTaskTurnParam {

    /** 任务Id */
    @Schema(description = "任务Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 要转给的用户Id */
    @Schema(description = "要转给的用户Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "userId不能为空")
    private String userId;

    /** 意见 */
    @Schema(description = "意见")
    private String comment;

    /** 附件名称 */
    @Schema(description = "附件名称")
    private String attachmentName;

    /** 附件地址 */
    @Schema(description = "附件地址")
    private String attachmentUrl;

    /** 填写数据 */
    @Schema(description = "填写数据")
    private String dataJson;

    /** 是否记录审批意见（流程监控转办不记录） */
    private boolean recordComment = true;

    /** 附件信息 */
    @Schema(description = "附件信息")
    private List<FlwTaskAttachmentParam> attachmentList;
}
