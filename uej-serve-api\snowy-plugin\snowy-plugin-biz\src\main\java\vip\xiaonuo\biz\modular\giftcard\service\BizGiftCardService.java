
package vip.xiaonuo.biz.modular.giftcard.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.giftcard.entity.BizGiftCard;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardAddParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardEditParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardIdParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardPageParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardVerifyParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardVerifyResult;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 礼品卡信息Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 17:07
 **/
public interface BizGiftCardService extends IService<BizGiftCard> {

    /**
     * 获取礼品卡信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    Page<BizGiftCard> page(BizGiftCardPageParam bizGiftCardPageParam);

    /**
     * 添加礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    void add(BizGiftCardAddParam bizGiftCardAddParam);

    /**
     * 编辑礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    void edit(BizGiftCardEditParam bizGiftCardEditParam);

    /**
     * 删除礼品卡信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    void delete(List<BizGiftCardIdParam> bizGiftCardIdParamList);

    /**
     * 获取礼品卡信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     */
    BizGiftCard detail(BizGiftCardIdParam bizGiftCardIdParam);

    /**
     * 获取礼品卡信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
     **/
    BizGiftCard queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:07
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);

    /**
     * 验证礼品卡
     *
     * <AUTHOR>
     * @date  2024/12/19
     **/
    BizGiftCardVerifyResult verify(BizGiftCardVerifyParam bizGiftCardVerifyParam);
}
