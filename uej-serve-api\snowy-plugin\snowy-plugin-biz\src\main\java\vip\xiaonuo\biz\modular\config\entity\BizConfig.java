package vip.xiaonuo.biz.modular.config.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务配置实体
 *
 * <AUTHOR>
 * @date  2024/07/08 19:09
 **/
@Getter
@Setter
@TableName("biz_config")
public class BizConfig {

    /** 主键 */
    @TableId
    @Schema(description = "主键")
    private String id;

    /** Parent Key */
    @Schema(description = "Parent Key")
    private String parentBizKey;

    /** Key */
    @Schema(description = "Key")
    private String bizKey;

    /** Value */
    @Schema(description = "Value")
    private String bizValue;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 组织id */
    @Schema(description = "组织id")
    private String orgId;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 更新用户 */
    @Schema(description = "更新用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;
}
