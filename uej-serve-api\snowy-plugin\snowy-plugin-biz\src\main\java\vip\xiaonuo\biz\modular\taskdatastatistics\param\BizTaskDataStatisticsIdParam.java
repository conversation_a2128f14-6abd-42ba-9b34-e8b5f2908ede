
package vip.xiaonuo.biz.modular.taskdatastatistics.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 任务数据统计Id参数
 *
 * <AUTHOR>
 * @date  2024/06/13 15:48
 **/
@Getter
@Setter
public class BizTaskDataStatisticsIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;
}
