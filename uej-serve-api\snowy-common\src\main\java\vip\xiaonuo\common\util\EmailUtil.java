package vip.xiaonuo.common.util;

import cn.hutool.extra.mail.Mail;
import cn.hutool.extra.mail.MailAccount;

public class EmailUtil {
    /**
     * 发送邮件
     *
     * @param to      收件人邮箱地址
     * @param subject 邮件主题
     * @param content 邮件内容
     */
    public static void sendEmail(String to, String subject, String content, MailAccount mailAccount) {
        String send = Mail.create(mailAccount)
                .setTos(to)
                .setTitle(subject)
                .setContent(content)
                .setHtml(true) // 如果是HTML内容，设置为true
                .send();
        System.err.println(send);
    }
}
