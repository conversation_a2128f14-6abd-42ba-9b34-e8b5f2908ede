
package vip.xiaonuo.biz.modular.giftcard.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡信息添加参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:07
 **/
@Getter
@Setter
public class BizGiftCardAddParam {

    /** Card Number */
    @Schema(description = "Card Number")
    private String cardNumber;

    /** Pin */
    @Schema(description = "Pin")
    private String pin;

    /** Create Date */
    @Schema(description = "Create Date")
    private String createDate;

    /** Active Date */
    @Schema(description = "Active Date")
    private String activeDate;

    /** Exp Date */
    @Schema(description = "Exp Date")
    private String expDate;

    /** Used Date */
    @Schema(description = "Used Date")
    private String usedDate;

    /** Value */
    @Schema(description = "Value")
    private String value;

    /** Rest Value */
    @Schema(description = "Rest Value")
    private String restValue;

    /** If Avtived */
    @Schema(description = "If Avtived")
    private String actived;

    @Schema(description = "前缀")
    private String prefix;

    @Schema(description = "开始序号")
    private Integer startNo;

    /** 组织ID */
    @Schema(description = "组织ID")
    private String orgId;

    private String extJson;
}
