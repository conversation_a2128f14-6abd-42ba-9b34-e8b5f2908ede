2025-06-23T08:15:44.421+08:00  INFO 17420 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 17420 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-23T08:15:44.426+08:00  INFO 17420 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-23T08:15:47.731+08:00  INFO 17420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23T08:15:47.737+08:00  INFO 17420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23T08:15:47.943+08:00  INFO 17420 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 179 ms. Found 0 Redis repository interfaces.
2025-06-23T08:15:48.336+08:00  WARN 17420 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-23T08:15:48.677+08:00  WARN 17420 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-23T08:15:49.174+08:00  WARN 17420 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-23T08:15:49.617+08:00  INFO 17420 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-23T08:15:49.640+08:00  INFO 17420 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-23T08:15:49.641+08:00  INFO 17420 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-23T08:15:49.754+08:00  INFO 17420 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-23T08:15:49.754+08:00  INFO 17420 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5272 ms
2025-06-23T08:15:50.183+08:00  INFO 17420 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-23T08:15:50.636+08:00  INFO 17420 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-23T08:15:50.636+08:00  INFO 17420 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-23T08:15:50.638+08:00  INFO 17420 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-23T08:15:50.638+08:00  INFO 17420 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-23T08:15:50.980+08:00  INFO 17420 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-23T08:15:55.122+08:00  INFO 17420 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-23T08:15:55.959+08:00  INFO 17420 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-23T08:15:57.992+08:00  INFO 17420 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@1f062e10, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-23T08:15:58.030+08:00  INFO 17420 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-23T08:15:58.030+08:00  INFO 17420 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-23T08:15:58.030+08:00  INFO 17420 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-23T08:15:58.030+08:00  INFO 17420 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-23T08:15:58.046+08:00  INFO 17420 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-23T08:15:58.530+08:00  INFO 17420 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@465e9090)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@301434fb, clock: SystemClock, configuration: Configuration(false)]
2025-06-23T08:16:01.921+08:00  INFO 17420 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-23T08:16:01.935+08:00  INFO 17420 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-23T08:16:02.150+08:00  INFO 17420 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-23T08:16:05.933+08:00  INFO 17420 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-23T08:16:06.029+08:00  INFO 17420 --- [main] vip.xiaonuo.Application                  : Started Application in 22.616 seconds (process running for 24.308)
2025-06-23T08:16:06.098+08:00  INFO 17420 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-23T08:33:53.611+08:00  INFO 17420 --- [http-nio-10082-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23T08:33:53.612+08:00  INFO 17420 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-23T08:33:53.619+08:00  INFO 17420 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 7 ms
2025-06-23T08:54:57.772+08:00  INFO 17420 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-23T08:54:57.813+08:00  INFO 17420 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-23T08:54:57.909+08:00  INFO 17420 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-23T08:54:57.976+08:00  INFO 17420 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-23T08:54:57.977+08:00  INFO 17420 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-23T08:54:57.977+08:00  INFO 17420 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-23T08:54:57.986+08:00  INFO 17420 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-23T08:54:57.986+08:00  INFO 17420 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-23T08:54:57.986+08:00  INFO 17420 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-23T08:55:18.733+08:00  INFO 31784 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 31784 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-23T08:55:18.736+08:00  INFO 31784 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-23T08:55:21.835+08:00  INFO 31784 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23T08:55:21.841+08:00  INFO 31784 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23T08:55:22.051+08:00  INFO 31784 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 191 ms. Found 0 Redis repository interfaces.
2025-06-23T08:55:22.420+08:00  WARN 31784 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-23T08:55:22.788+08:00  WARN 31784 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-23T08:55:23.203+08:00  WARN 31784 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-23T08:55:23.738+08:00  INFO 31784 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-23T08:55:23.754+08:00  INFO 31784 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-23T08:55:23.755+08:00  INFO 31784 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-23T08:55:23.851+08:00  INFO 31784 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-23T08:55:23.852+08:00  INFO 31784 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5053 ms
2025-06-23T08:55:24.222+08:00  INFO 31784 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-23T08:55:24.514+08:00  INFO 31784 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-23T08:55:24.515+08:00  INFO 31784 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-23T08:55:24.516+08:00  INFO 31784 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-23T08:55:24.516+08:00  INFO 31784 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-23T08:55:24.850+08:00  INFO 31784 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-23T08:55:29.201+08:00  INFO 31784 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-23T08:55:30.140+08:00  INFO 31784 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-23T08:55:32.321+08:00  INFO 31784 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@1f062e10, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-23T08:55:32.354+08:00  INFO 31784 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-23T08:55:32.355+08:00  INFO 31784 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-23T08:55:32.355+08:00  INFO 31784 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-23T08:55:32.355+08:00  INFO 31784 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-23T08:55:32.374+08:00  INFO 31784 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-23T08:55:33.039+08:00  INFO 31784 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@465e9090)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@301434fb, clock: SystemClock, configuration: Configuration(false)]
2025-06-23T08:55:35.600+08:00  INFO 31784 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-23T08:55:35.605+08:00  INFO 31784 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-23T08:55:35.751+08:00  INFO 31784 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-23T08:55:38.693+08:00  INFO 31784 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-23T08:55:38.790+08:00  INFO 31784 --- [main] vip.xiaonuo.Application                  : Started Application in 21.45 seconds (process running for 22.795)
2025-06-23T08:55:38.854+08:00  INFO 31784 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-23T08:55:49.384+08:00  INFO 31784 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23T08:55:49.385+08:00  INFO 31784 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-23T08:55:49.389+08:00  INFO 31784 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-06-23T09:05:04.461+08:00  INFO 31784 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-23T09:05:04.480+08:00  INFO 31784 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-23T09:05:04.490+08:00  INFO 31784 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-23T09:05:04.502+08:00  INFO 31784 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-23T09:05:04.502+08:00  INFO 31784 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-23T09:05:04.503+08:00  INFO 31784 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-23T09:05:04.506+08:00  INFO 31784 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-23T09:05:04.506+08:00  INFO 31784 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-23T09:05:04.506+08:00  INFO 31784 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-23T09:05:24.864+08:00  INFO 29492 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 29492 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-23T09:05:24.866+08:00  INFO 29492 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-23T09:05:28.015+08:00  INFO 29492 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23T09:05:28.020+08:00  INFO 29492 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23T09:05:28.285+08:00  INFO 29492 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 245 ms. Found 0 Redis repository interfaces.
2025-06-23T09:05:28.746+08:00  WARN 29492 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-23T09:05:29.101+08:00  WARN 29492 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-23T09:05:29.460+08:00  WARN 29492 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-23T09:05:29.844+08:00  INFO 29492 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-23T09:05:29.863+08:00  INFO 29492 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-23T09:05:29.863+08:00  INFO 29492 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-23T09:05:29.966+08:00  INFO 29492 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-23T09:05:29.966+08:00  INFO 29492 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5040 ms
2025-06-23T09:05:30.363+08:00  INFO 29492 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-23T09:05:30.667+08:00  INFO 29492 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-23T09:05:30.669+08:00  INFO 29492 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-23T09:05:30.670+08:00  INFO 29492 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-23T09:05:30.670+08:00  INFO 29492 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-23T09:05:30.992+08:00  INFO 29492 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-23T09:05:35.575+08:00  INFO 29492 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-23T09:05:36.400+08:00  INFO 29492 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-23T09:05:38.667+08:00  INFO 29492 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@36dbfa68, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-23T09:05:38.702+08:00  INFO 29492 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-23T09:05:38.702+08:00  INFO 29492 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-23T09:05:38.702+08:00  INFO 29492 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-23T09:05:38.703+08:00  INFO 29492 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-23T09:05:38.714+08:00  INFO 29492 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-23T09:05:39.107+08:00  INFO 29492 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@7bfd8f92)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@1641dfb3, clock: SystemClock, configuration: Configuration(false)]
2025-06-23T09:05:41.454+08:00  INFO 29492 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-23T09:05:41.458+08:00  INFO 29492 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-23T09:05:41.604+08:00  INFO 29492 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-23T09:05:44.375+08:00  INFO 29492 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-23T09:05:44.496+08:00  INFO 29492 --- [main] vip.xiaonuo.Application                  : Started Application in 20.736 seconds (process running for 22.104)
2025-06-23T09:05:44.563+08:00  INFO 29492 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-23T09:05:50.129+08:00  INFO 29492 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23T09:05:50.130+08:00  INFO 29492 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-23T09:05:50.131+08:00  INFO 29492 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-23T13:41:58.443+08:00  INFO 29492 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-23T13:41:58.772+08:00  INFO 29492 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-23T13:41:58.831+08:00  INFO 29492 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-23T13:41:59.003+08:00  INFO 29492 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-23T13:41:59.005+08:00  INFO 29492 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-23T13:41:59.006+08:00  INFO 29492 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-23T13:41:59.023+08:00  INFO 29492 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-23T13:41:59.024+08:00  INFO 29492 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-23T13:41:59.026+08:00  INFO 29492 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
