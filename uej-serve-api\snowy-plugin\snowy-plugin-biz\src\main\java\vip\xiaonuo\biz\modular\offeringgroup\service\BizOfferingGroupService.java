
package vip.xiaonuo.biz.modular.offeringgroup.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.offeringgroup.entity.BizOfferingGroup;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupAddParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupEditParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupIdParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 服务或产品组合Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 16:57
 **/
public interface BizOfferingGroupService extends IService<BizOfferingGroup> {

    /**
     * 获取服务或产品组合分页
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    Page<BizOfferingGroup> page(BizOfferingGroupPageParam bizOfferingGroupPageParam);

    /**
     * 添加服务或产品组合
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    void add(BizOfferingGroupAddParam bizOfferingGroupAddParam);

    /**
     * 编辑服务或产品组合
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    void edit(BizOfferingGroupEditParam bizOfferingGroupEditParam);

    /**
     * 删除服务或产品组合
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    void delete(List<BizOfferingGroupIdParam> bizOfferingGroupIdParamList);

    /**
     * 获取服务或产品组合详情
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    BizOfferingGroup detail(BizOfferingGroupIdParam bizOfferingGroupIdParam);

    /**
     * 获取服务或产品组合详情
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     **/
    BizOfferingGroup queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
