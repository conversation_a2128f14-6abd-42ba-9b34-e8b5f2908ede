
package vip.xiaonuo.biz.modular.dailybalance.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目结款记录表实体
 *
 * <AUTHOR>
 * @date  2024/12/02 13:50
 **/
@Getter
@Setter
@TableName("biz_daily_balance")
public class BizDailyBalance {

    /** 主键 */
    @TableId
    @Schema(description = "主键")
    private String id;

    /** item */
    @Schema(description = "item")
    private String item;

    /** receivable amount */
    @Schema(description = "receivable amount")
    private BigDecimal receivableAmount;

    /** received amount */
    @Schema(description = "received amount")
    private BigDecimal receivedAmount;

    /** balance */
    @Schema(description = "balance")
    private BigDecimal balance;

    /** remark */
    @Schema(description = "remark")
    private String remark;

    /** shop id */
    @Schema(description = "shop id")
    private String orgId;

    /** shop name */
    @Schema(description = "shop name")
    private String orgName;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 更新用户 */
    @Schema(description = "更新用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;
}
