
package vip.xiaonuo.flw.modular.process.result;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 草稿结果
 *
 * <AUTHOR>
 * @date 2022/5/11 15:51
 **/
@Getter
@Setter
public class FlwProcessMyDraftResult extends OrderItem {

    /** 草稿id */
    @Schema(description = "草稿id")
    private String id;

    /** 模型id */
    @Schema(description = "模型id")
    private String modelId;

    /** 填写的数据 */
    @Schema(description = "填写的数据")
    private String dataJson;

    /** 流程名称 */
    @Schema(description = "流程名称")
    private String title;

    /** 创建时间 */
    @Schema(description = "创建时间")
    private String createTime;
}
