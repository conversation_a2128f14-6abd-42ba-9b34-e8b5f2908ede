
package vip.xiaonuo.biz.modular.customer.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 外部会员信息添加参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:22
 **/
@Getter
@Setter
public class BizCustomerAddParam {

    /** First Name */
    @Schema(description = "First Name")
    private String firstName;

    /** Last Name */
    @Schema(description = "Last Name")
    private String lastName;

    /** Gender */
    @Schema(description = "Gender")
    private String gender;

    /** Level */
    @Schema(description = "Level")
    private String level;

    /** Reward Points */
    @Schema(description = "Reward Points")
    private String rewardPoints;

    /** Attend Time */
    @Schema(description = "Attend Time")
    private String attendTime;

    /** Email */
    @Schema(description = "Email")
    private String email;

    /** Phone */
    @Schema(description = "Phone")
    private String phone;

    private String extJson;

    @Schema(description = "签名")
    private String signature;

    @Schema(description = "会员号")
    private String memberId;

    @Schema(description = "保险公司")
    private String InsuranceCompany;

    @Schema(description = "医保卡子账号")
    private String personalNo;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "邮编")
    private String postCode;

    @Schema(description = "紧急联系电话")
    private String emergencyContact;

    private String medicalCondition;
    private String medications;
    private String massageOilAllergic;
    private String pregnant;
    private String pastIllnesses;
    private String skinInfection;
}
