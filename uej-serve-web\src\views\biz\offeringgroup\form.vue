<template>
	<xn-form-container
		:title="formData.id ? 'edit offeringgroup' : 'add offeringgroup'"
		:width="800"
		v-model:open="open"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="Type：" name="category">
						<a-select
							v-model:value="formData.category"
							:options="categoryOptions"
							style="width: 100%"
							placeholder="Please enter Category"
						>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Name：" name="name">
						<a-input v-model:value="formData.name" placeholder="Please enter Name" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Planning Hours：" name="planningHours">
						<a-input v-model:value="formData.planningHours" placeholder="Please enter planningHours" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="List Price：" name="listPrice">
						<a-input v-model:value="formData.listPrice" placeholder="Please enter List Price" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="Cost Price：" name="costPrice">
						<a-input v-model:value="formData.costPrice" placeholder="Please enter Cost Price" allow-clear />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
					<xn-form-item :fieldConfig="item" :formData="dynamicFormData" />
				</a-col>
			</a-row>
		</a-form>
		<a-space class="mb-[10px]">
			<a-button type="primary" @click="productFormRef.onOpen()">
				<template #icon><plus-outlined /></template>
				{{ $t('common.addButton') }}
			</a-button>
		</a-space>
		<a-table :dataSource="dataSource" :columns="columns" :loading="loading">
			<template #bodyCell="{ column, record, index }">
				<template v-if="column.dataIndex === 'num'">
					<a-input-number v-model:value="record.num" :min="1" allow-clear style="width: 100%" />
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a @click="removeItem(index)" class="text-primary-6">{{ $t('common.removeButton') }}</a>
				</template>
			</template>
		</a-table>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
	<Form ref="productFormRef" @successful="getProductInfo"></Form>
</template>

<script setup name="bizOfferingGroupForm">
	import dayjs from 'dayjs'
	import tool from '@/utils/tool'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import bizOfferingGroupApi from '@/api/biz/bizOfferingGroupApi'
	import Form from './productForm.vue'
	import { message } from 'ant-design-vue'
	// 抽屉状态
	const open = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)
	// 动态表单
	const dynamicFormRef = ref()
	const dynamicFieldConfigList = ref([])
	const dynamicFormData = ref({})
	const categoryOptions = tool.dictList('PRODUCT_CATEGORY')

	const productFormRef = ref()
	const loading = ref(false)
	const dataSource = ref([])
	const columns = [
		{
			title: 'OFFERING_ID',
			dataIndex: 'offeringId'
		},
		{
			title: 'OFFERING_NAME',
			dataIndex: 'offeringName'
		},
		{
			title: 'LIST PRICE',
			dataIndex: 'listPrice'
		},
		{
			title: 'PLANNING HOURS',
			dataIndex: 'planningHours'
		},
		{
			title: 'NUM',
			dataIndex: 'num'
		},
		{
			title: 'TOTAL PRICE',
			dataIndex: 'totalPrice'
		},
		{
			title: 'Action',
			dataIndex: 'action'
		}
	]

	// 打开抽屉
	const onOpen = (record) => {
		open.value = true
		bizOfferingGroupApi.bizOfferingGroupDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
			dynamicFieldConfigList.value = data
		})
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			dynamicFormData.value = JSON.parse(formData.value.extJson || null) || {}
			loading.value = true
			bizOfferingGroupApi
				.bizOfferingGroupDetail({ id: record.id })
				.then((res) => {
					dataSource.value = res.offerings || []
					loading.value = false
				})
				.catch((err) => {
					loading.value = false
				})
		}
	}

	watch(
		() => dataSource,
		() => {
			dataSource.value.forEach((item) => {
				item.totalPrice = item.listPrice * item.num
			})
			let date = dataSource.value.reduce((prev, curr) => {
				const [hours, minutes] = curr.planningHours.split(':').map(Number)
				//转成分钟
				const min = hours * 60 + minutes
				return prev + min
			}, 0)

			formData.value.planningHours = tool.convertHoursToHHMM(date)
			formData.value.listPrice = dataSource.value.reduce((prev, curr) => prev + Number(curr.totalPrice), 0)
		},
		{ deep: true, immediate: true }
	)

	const removeItem = (index) => {
		dataSource.value.splice(index, 1)
	}

	const getProductInfo = (option) => {
		option.forEach((opt) => {
			const existingOptionIndex = dataSource.value.findIndex((item) => item.offeringId === opt.offeringId)
			if (existingOptionIndex !== -1) {
				dataSource.value[existingOptionIndex].num += 1
			} else {
				dataSource.value.push(opt)
			}
		})
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		dynamicFormData.value = {}
		dataSource.value = []
		open.value = false
	}
	// 默认要校验的
	const formRules = {}
	// 验证并提交数据
	const onSubmit = () => {
		const promiseList = []
		promiseList.push(
			new Promise((resolve, reject) => {
				formRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		promiseList.push(
			new Promise((resolve, reject) => {
				dynamicFormRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		submitLoading.value = true
		Promise.all(promiseList)
			.then(() => {
				const formDataParam = cloneDeep(formData.value)
				formDataParam.extJson = JSON.stringify(dynamicFormData.value)
				formDataParam.offerings = dataSource.value
				bizOfferingGroupApi
					.bizOfferingGroupSubmitForm(formDataParam, formDataParam.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			})
			.catch(() => {})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
