
package vip.xiaonuo.flw.modular.model.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 组织选择器结果
 *
 * <AUTHOR>
 * @date 2022/7/22 14:28
 **/
@Getter
@Setter
public class FlwModelOrgResult {

    /** id */
    @Schema(description = "id")
    private String id;

    /** 父id */
    @Schema(description = "父id")
    private String parentId;

    /** 名称 */
    @Schema(description = "名称")
    private String name;

    /** 分类 */
    @Schema(description = "分类")
    private String category;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;
}
