package vip.xiaonuo.biz.modular.productcategory.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * 产品类目实体
 *
 * <AUTHOR>
 * @date 2024/12/19
 **/
@Getter
@Setter
@TableName("DEV_DICT")
public class BizProductCategory extends CommonEntity {

    /** id */
    @Schema(description = "id")
    private String id;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 组织id */
    @Schema(description = "组织id")
    private String orgId;

    /** 父id */
    @Schema(description = "父id")
    private String parentId;

    /** 类目名称 */
    @Schema(description = "类目名称")
    private String dictLabel;

    /** 类目编码 */
    @Schema(description = "类目编码")
    private String dictValue;

    /** 分类（固定为PRODUCT_CATEGORY） */
    @Schema(description = "分类")
    private String category;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private String extJson;
} 