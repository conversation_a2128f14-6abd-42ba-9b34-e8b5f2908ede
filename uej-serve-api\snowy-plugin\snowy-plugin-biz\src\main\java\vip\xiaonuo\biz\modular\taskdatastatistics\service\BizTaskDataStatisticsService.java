
package vip.xiaonuo.biz.modular.taskdatastatistics.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.taskdatastatistics.entity.BizTaskDataStatistics;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsAddParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsEditParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsIdParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 任务数据统计Service接口
 *
 * <AUTHOR>
 * @date  2024/06/13 15:48
 **/
public interface BizTaskDataStatisticsService extends IService<BizTaskDataStatistics> {

    /**
     * 获取任务数据统计分页
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    Page<BizTaskDataStatistics> page(BizTaskDataStatisticsPageParam bizTaskDataStatisticsPageParam);

    /**
     * 添加任务数据统计
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    void add(BizTaskDataStatisticsAddParam bizTaskDataStatisticsAddParam);

    /**
     * 编辑任务数据统计
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    void edit(BizTaskDataStatisticsEditParam bizTaskDataStatisticsEditParam);

    /**
     * 删除任务数据统计
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    void delete(List<BizTaskDataStatisticsIdParam> bizTaskDataStatisticsIdParamList);

    /**
     * 获取任务数据统计详情
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    BizTaskDataStatistics detail(BizTaskDataStatisticsIdParam bizTaskDataStatisticsIdParam);

    /**
     * 获取任务数据统计详情
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     **/
    BizTaskDataStatistics queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
