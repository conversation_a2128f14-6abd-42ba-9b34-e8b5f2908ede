package vip.xiaonuo.biz.modular.insurancecompany.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.insurancecompany.entity.BizInsuranceCompany;
import vip.xiaonuo.biz.modular.insurancecompany.mapper.BizInsuranceCompanyMapper;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyAddParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyEditParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyIdParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyPageParam;
import vip.xiaonuo.biz.modular.insurancecompany.service.BizInsuranceCompanyService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 保险公司Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/09 09:42
 **/
@Service
public class BizInsuranceCompanyServiceImpl extends ServiceImpl<BizInsuranceCompanyMapper, BizInsuranceCompany> implements BizInsuranceCompanyService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizInsuranceCompany> page(BizInsuranceCompanyPageParam bizInsuranceCompanyPageParam) {
        QueryWrapper<BizInsuranceCompany> queryWrapper = new QueryWrapper<BizInsuranceCompany>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizInsuranceCompanyPageParam.getName())) {
            queryWrapper.lambda().like(BizInsuranceCompany::getName, bizInsuranceCompanyPageParam.getName());
        }
        if(ObjectUtil.isAllNotEmpty(bizInsuranceCompanyPageParam.getSortField(), bizInsuranceCompanyPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizInsuranceCompanyPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizInsuranceCompanyPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizInsuranceCompanyPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizInsuranceCompany::getId);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizInsuranceCompany::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizInsuranceCompany::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizInsuranceCompany::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizInsuranceCompanyAddParam bizInsuranceCompanyAddParam) {
        BizInsuranceCompany bizInsuranceCompany = BeanUtil.toBean(bizInsuranceCompanyAddParam, BizInsuranceCompany.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizInsuranceCompany.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizInsuranceCompany.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizInsuranceCompany);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizInsuranceCompanyEditParam bizInsuranceCompanyEditParam) {
        BizInsuranceCompany bizInsuranceCompany = this.queryEntity(bizInsuranceCompanyEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查保险公司所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizInsuranceCompany.getOrgId()) && !loginUserDataScope.contains(bizInsuranceCompany.getOrgId())) {
                throw new CommonException("您没有权限编辑该保险公司，公司名称：{}", bizInsuranceCompany.getName());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizInsuranceCompany.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该保险公司，公司名称：{}", bizInsuranceCompany.getName());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该保险公司，公司名称：{}", bizInsuranceCompany.getName());
            }
        }
        
        BeanUtil.copyProperties(bizInsuranceCompanyEditParam, bizInsuranceCompany);
        this.updateById(bizInsuranceCompany);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizInsuranceCompanyIdParam> bizInsuranceCompanyIdParamList) {
        List<String> insuranceCompanyIdList = CollStreamUtil.toList(bizInsuranceCompanyIdParamList, BizInsuranceCompanyIdParam::getId);
        if(ObjectUtil.isNotEmpty(insuranceCompanyIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizInsuranceCompany> insuranceCompanyList = this.listByIds(insuranceCompanyIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查保险公司所属机构是否在权限范围内
                for(BizInsuranceCompany insuranceCompany : insuranceCompanyList) {
                    if(ObjectUtil.isNotEmpty(insuranceCompany.getOrgId()) && !loginUserDataScope.contains(insuranceCompany.getOrgId())) {
                        throw new CommonException("您没有权限删除该保险公司，公司名称：{}", insuranceCompany.getName());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizInsuranceCompany insuranceCompany : insuranceCompanyList) {
                        if(!insuranceCompany.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该保险公司，公司名称：{}", insuranceCompany.getName());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除保险公司");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(insuranceCompanyIdList);
    }

    @Override
    public BizInsuranceCompany detail(BizInsuranceCompanyIdParam bizInsuranceCompanyIdParam) {
        BizInsuranceCompany bizInsuranceCompany = this.queryEntity(bizInsuranceCompanyIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查保险公司所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizInsuranceCompany.getOrgId()) && !loginUserDataScope.contains(bizInsuranceCompany.getOrgId())) {
                throw new CommonException("您没有权限查看该保险公司，公司名称：{}", bizInsuranceCompany.getName());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizInsuranceCompany.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该保险公司，公司名称：{}", bizInsuranceCompany.getName());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该保险公司，公司名称：{}", bizInsuranceCompany.getName());
            }
        }
        
        return bizInsuranceCompany;
    }

    @Override
    public BizInsuranceCompany queryEntity(String id) {
        BizInsuranceCompany bizInsuranceCompany = this.getById(id);
        if(ObjectUtil.isEmpty(bizInsuranceCompany)) {
            throw new CommonException("保险公司不存在，id值为：{}", id);
        }
        return bizInsuranceCompany;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizInsuranceCompanyServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizInsuranceCompany.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}

