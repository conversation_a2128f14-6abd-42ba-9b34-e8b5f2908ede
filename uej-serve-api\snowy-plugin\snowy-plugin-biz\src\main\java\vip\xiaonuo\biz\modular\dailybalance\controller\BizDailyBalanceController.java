
package vip.xiaonuo.biz.modular.dailybalance.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.dailybalance.entity.BizDailyBalance;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceAddParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceEditParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceIdParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalancePageParam;
import vip.xiaonuo.biz.modular.dailybalance.service.BizDailyBalanceService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 项目结款记录表控制器
 *
 * <AUTHOR>
 * @date  2024/12/02 13:50
 */
@Tag(name = "项目结款记录表控制器")
@RestController
@Validated
public class BizDailyBalanceController {

    @Resource
    private BizDailyBalanceService bizDailyBalanceService;

    /**
     * 获取项目结款记录表分页
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    @Operation(summary = "获取项目结款记录表分页")
    @SaCheckPermission("/biz/dailybalance/page")
    @GetMapping("/biz/dailybalance/page")
    public CommonResult<Page<BizDailyBalance>> page(BizDailyBalancePageParam bizDailyBalancePageParam) {
        return CommonResult.data(bizDailyBalanceService.page(bizDailyBalancePageParam));
    }

    /**
     * 添加项目结款记录表
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    @Operation(summary = "添加项目结款记录表")
    @CommonLog("添加项目结款记录表")
    @SaCheckPermission("/biz/dailybalance/add")
    @PostMapping("/biz/dailybalance/add")
    public CommonResult<String> add(@RequestBody @Valid BizDailyBalanceAddParam bizDailyBalanceAddParam) {
        bizDailyBalanceService.add(bizDailyBalanceAddParam);
        return CommonResult.ok();
    }

    @Operation(summary = "添加今日结款记录表")
    @SaCheckPermission("/biz/dailybalance/addBatch")
    @PostMapping("/biz/dailybalance/addBatch")
    public CommonResult<String> addBatch(@RequestBody @Valid List<BizDailyBalance> param) {
        bizDailyBalanceService.saveOrUpdateBatch(param);
        return CommonResult.ok();
    }

    /**
     * 编辑项目结款记录表
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    @Operation(summary = "编辑项目结款记录表")
    @CommonLog("编辑项目结款记录表")
    @SaCheckPermission("/biz/dailybalance/edit")
    @PostMapping("/biz/dailybalance/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizDailyBalanceEditParam bizDailyBalanceEditParam) {
        bizDailyBalanceService.edit(bizDailyBalanceEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除项目结款记录表
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    @Operation(summary = "删除项目结款记录表")
    @CommonLog("删除项目结款记录表")
    @SaCheckPermission("/biz/dailybalance/delete")
    @PostMapping("/biz/dailybalance/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizDailyBalanceIdParam> bizDailyBalanceIdParamList) {
        bizDailyBalanceService.delete(bizDailyBalanceIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取项目结款记录表详情
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    @Operation(summary = "获取项目结款记录表详情")
    @SaCheckPermission("/biz/dailybalance/detail")
    @GetMapping("/biz/dailybalance/detail")
    public CommonResult<BizDailyBalance> detail(@Valid BizDailyBalanceIdParam bizDailyBalanceIdParam) {
        return CommonResult.data(bizDailyBalanceService.detail(bizDailyBalanceIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    @Operation(summary = "获取项目结款记录表动态字段的配置")
    @SaCheckPermission("/biz/dailybalance/dynamicFieldConfigList")
    @GetMapping("/biz/dailybalance/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizDailyBalanceService.dynamicFieldConfigList(columnName));
    }

    /**
     * 检查当天是否已提交日扎帐
     *
     * <AUTHOR>
     * @date  2024/12/19
     */
    @Operation(summary = "检查当天是否已提交日扎帐")
    //@SaCheckPermission("/biz/dailybalance/checkTodaySubmitted")
    @SaCheckPermission("/biz/dailybalance/addBatch")
    @GetMapping("/biz/dailybalance/checkTodaySubmitted")
    public CommonResult<Boolean> checkTodaySubmitted(String orgId, String date) {
        return CommonResult.data(bizDailyBalanceService.checkTodaySubmitted(orgId, date));
    }

    /**
     * 解锁日扎帐提交
     *
     * <AUTHOR>
     * @date  2024/12/19
     */
    @Operation(summary = "解锁日扎帐提交")
    @CommonLog("解锁日扎帐提交")
    @SaCheckPermission("/biz/dailybalance/unlock")
    @PostMapping("/biz/dailybalance/unlock")
    public CommonResult<String> unlockDailyBalance(@RequestBody JSONObject param) {
        String orgId = param.getStr("orgId");
        String date = param.getStr("date");
        bizDailyBalanceService.unlockDailyBalance(orgId, date);
        return CommonResult.ok();
    }
}
