
package vip.xiaonuo.biz.modular.giftcardlog.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡记录添加参数
 *
 * <AUTHOR>
 * @date  2024/06/13 09:46
 **/
@Getter
@Setter
public class BizGiftCardLogAddParam {

    /** 主表ID */
    @Schema(description = "主表ID")
    private String mainId;

    /** Total Value */
    @Schema(description = "Total Value")
    private String totalValue;

    /** Use Value */
    @Schema(description = "Use Value")
    private String useValue;

    /** Rest Value */
    @Schema(description = "Rest Value")
    private String restValue;

    /** 组织ID */
    @Schema(description = "组织ID")
    private String orgId;

    private String extJson;
}
