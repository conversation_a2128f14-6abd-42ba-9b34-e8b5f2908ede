
package vip.xiaonuo.biz.modular.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.customer.entity.BizCustomer;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerAddParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerEditParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerIdParam;
import vip.xiaonuo.biz.modular.customer.param.BizCustomerPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 外部会员信息Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 17:22
 **/
public interface BizCustomerService extends IService<BizCustomer> {

    /**
     * 获取外部会员信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    Page<BizCustomer> page(BizCustomerPageParam bizCustomerPageParam);

    /**
     * 添加外部会员信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    void add(BizCustomerAddParam bizCustomerAddParam);

    /**
     * 编辑外部会员信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    void edit(BizCustomerEditParam bizCustomerEditParam);

    /**
     * 删除外部会员信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    void delete(List<BizCustomerIdParam> bizCustomerIdParamList);

    /**
     * 获取外部会员信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     */
    BizCustomer detail(BizCustomerIdParam bizCustomerIdParam);

    /**
     * 获取外部会员信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
     **/
    BizCustomer queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:22
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
