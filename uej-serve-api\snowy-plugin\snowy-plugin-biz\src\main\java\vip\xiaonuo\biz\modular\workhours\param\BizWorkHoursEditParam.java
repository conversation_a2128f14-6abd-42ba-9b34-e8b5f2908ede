
package vip.xiaonuo.biz.modular.workhours.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工时编辑参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:27
 **/
@Getter
@Setter
public class BizWorkHoursEditParam {

    /** ID */
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** Name */
    @Schema(description = "Name")
    private String name;

    /** Start Time */
    @Schema(description = "Start Time")
    private String startTime;

    /** End Time */
    @Schema(description = "End Time")
    private String endTime;

    private String extJson;
}
