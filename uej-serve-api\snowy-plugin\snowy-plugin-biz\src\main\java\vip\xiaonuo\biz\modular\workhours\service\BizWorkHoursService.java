
package vip.xiaonuo.biz.modular.workhours.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.workhours.entity.BizWorkHours;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursAddParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursEditParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursIdParam;
import vip.xiaonuo.biz.modular.workhours.param.BizWorkHoursPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 工时Service接口
 *
 * <AUTHOR>
 * @date  2024/06/12 17:27
 **/
public interface BizWorkHoursService extends IService<BizWorkHours> {

    /**
     * 获取工时分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    Page<BizWorkHours> page(BizWorkHoursPageParam bizWorkHoursPageParam);

    /**
     * 添加工时
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    void add(BizWorkHoursAddParam bizWorkHoursAddParam);

    /**
     * 编辑工时
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    void edit(BizWorkHoursEditParam bizWorkHoursEditParam);

    /**
     * 删除工时
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    void delete(List<BizWorkHoursIdParam> bizWorkHoursIdParamList);

    /**
     * 获取工时详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     */
    BizWorkHours detail(BizWorkHoursIdParam bizWorkHoursIdParam);

    /**
     * 获取工时详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
     **/
    BizWorkHours queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:27
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
