
package vip.xiaonuo.dbs.modular.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.dbs.modular.entity.DbsStorage;
import vip.xiaonuo.dbs.modular.param.*;
import vip.xiaonuo.dbs.modular.result.DbsTableColumnResult;
import vip.xiaonuo.dbs.modular.result.DbsTableResult;
import vip.xiaonuo.dbs.modular.service.DbsService;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据源控制器
 *
 * <AUTHOR>
 * @date 2022/6/8 19:33
 **/
@Tag(name = "数据源控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
public class DbsController {

    @Resource
    private DbsService dbsStorageService;

    /**
     * 获取数据源分页
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取数据源分页")
    @GetMapping("/dbs/storage/page")
    public CommonResult<Page<DbsStorage>> page(DbsStoragePageParam dbsStoragePageParam) {
        return CommonResult.data(dbsStorageService.page(dbsStoragePageParam));
    }

    /**
     * 添加数据源
     *
     * <AUTHOR>
     * @date 2022/4/24 20:47
     */
    @ApiOperationSupport(order = 2)
    @Operation(summary = "添加数据源")
    @CommonLog("添加数据源")
    @PostMapping("/dbs/storage/add")
    public CommonResult<String> add(@RequestBody @Valid DbsStorageAddParam dbsStorageAddParam) {
        dbsStorageService.add(dbsStorageAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑数据源
     *
     * <AUTHOR>
     * @date 2022/4/24 20:47
     */
    @ApiOperationSupport(order = 3)
    @Operation(summary = "编辑数据源")
    @CommonLog("编辑数据源")
    @PostMapping("/dbs/storage/edit")
    public CommonResult<String> edit(@RequestBody @Valid DbsStorageEditParam dbsStorageEditParam) {
        dbsStorageService.edit(dbsStorageEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除数据源
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 4)
    @Operation(summary = "删除数据源")
    @CommonLog("删除数据源")
    @PostMapping("/dbs/storage/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<DbsStorageIdParam> dbsStorageIdParamList) {
        dbsStorageService.delete(dbsStorageIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取数据源详情
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 5)
    @Operation(summary = "获取数据源详情")
    @GetMapping("/dbs/storage/detail")
    public CommonResult<DbsStorage> detail(@Valid DbsStorageIdParam dbsStorageIdParam) {
        return CommonResult.data(dbsStorageService.detail(dbsStorageIdParam));
    }

    /* ====数据源部分所需要用到的选择器==== */

    /**
     * 获取数据库中所有表
     *
     * <AUTHOR>
     * @date 2022/6/8 19:35
     **/
    @ApiOperationSupport(order = 6)
    @Operation(summary = "获取数据库中所有表")
    @GetMapping("/dbs/tables")
    public CommonResult<List<DbsTableResult>> tables() {
        return CommonResult.data(dbsStorageService.tables());
    }

    /**
     * 获取数据库表中所有字段
     *
     * <AUTHOR>
     * @date 2022/6/8 19:35
     **/
    @ApiOperationSupport(order = 7)
    @Operation(summary = "获取数据库表中所有字段")
    @GetMapping("/dbs/tableColumns")
    public CommonResult<List<DbsTableColumnResult>> tableColumns(@Valid DbsStorageTableColumnParam dbsStorageTableColumnParam) {
        return CommonResult.data(dbsStorageService.tableColumns(dbsStorageTableColumnParam));
    }
}
