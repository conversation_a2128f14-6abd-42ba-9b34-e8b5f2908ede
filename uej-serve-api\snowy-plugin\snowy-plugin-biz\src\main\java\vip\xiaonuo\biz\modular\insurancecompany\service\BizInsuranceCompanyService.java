
package vip.xiaonuo.biz.modular.insurancecompany.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.insurancecompany.entity.BizInsuranceCompany;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyAddParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyEditParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyIdParam;
import vip.xiaonuo.biz.modular.insurancecompany.param.BizInsuranceCompanyPageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 保险公司Service接口
 *
 * <AUTHOR>
 * @date  2024/08/09 09:42
 **/
public interface BizInsuranceCompanyService extends IService<BizInsuranceCompany> {

    /**
     * 获取保险公司分页
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    Page<BizInsuranceCompany> page(BizInsuranceCompanyPageParam bizInsuranceCompanyPageParam);

    /**
     * 添加保险公司
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    void add(BizInsuranceCompanyAddParam bizInsuranceCompanyAddParam);

    /**
     * 编辑保险公司
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    void edit(BizInsuranceCompanyEditParam bizInsuranceCompanyEditParam);

    /**
     * 删除保险公司
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    void delete(List<BizInsuranceCompanyIdParam> bizInsuranceCompanyIdParamList);

    /**
     * 获取保险公司详情
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     */
    BizInsuranceCompany detail(BizInsuranceCompanyIdParam bizInsuranceCompanyIdParam);

    /**
     * 获取保险公司详情
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
     **/
    BizInsuranceCompany queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/08/09 09:42
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
