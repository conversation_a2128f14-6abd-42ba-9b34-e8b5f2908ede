package vip.xiaonuo.biz.modular.voucher.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.voucher.entity.BizVoucher;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherAddParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherEditParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherIdParam;
import vip.xiaonuo.biz.modular.voucher.param.BizVoucherPageParam;
import vip.xiaonuo.biz.modular.voucher.service.BizVoucherService;
import cn.hutool.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 代金券信息控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 17:17
 */
@Tag(name = "代金券信息控制器")
@RestController
@Validated
public class BizVoucherController {

    @Resource
    private BizVoucherService bizVoucherService;

    /**
     * 获取代金券信息分页
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    @Operation(summary = "获取代金券信息分页")
    @SaCheckPermission("/biz/voucher/page")
    @GetMapping("/biz/voucher/page")
    public CommonResult<Page<BizVoucher>> page(BizVoucherPageParam bizVoucherPageParam) {
        return CommonResult.data(bizVoucherService.page(bizVoucherPageParam));
    }

    /**
     * 添加代金券信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    @Operation(summary = "添加代金券信息")
    @CommonLog("添加代金券信息")
    @SaCheckPermission("/biz/voucher/add")
    @PostMapping("/biz/voucher/add")
    public CommonResult<String> add(@RequestBody @Valid BizVoucher bizVoucher) {
        if (bizVoucher.getNum() < 2) {
            // 单个代金券添加
            if(bizVoucher!=null&& !StringUtils.isEmpty(bizVoucher.getPid())) {
                String baseStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
                String randomStr = RandomUtil.randomString(baseStr, 5);
                bizVoucher.setVoucherNumber(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + randomStr);
            }
            // 转换为AddParam并调用Service层的add方法以确保权限控制和orgId设置
            BizVoucherAddParam addParam = BeanUtil.toBean(bizVoucher, BizVoucherAddParam.class);
            bizVoucherService.add(addParam);
        } else {
            // 批量代金券添加
            for (int i = 0; i < bizVoucher.getNum(); i++) {
                BizVoucher voucher = new BizVoucher();
                if(bizVoucher!=null&& !StringUtils.isEmpty(bizVoucher.getPid())) {
                    String baseStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
                    String randomStr = RandomUtil.randomString(baseStr, 5);
                    voucher.setVoucherNumber(new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + randomStr);
                }
                BeanUtil.copyProperties(bizVoucher, voucher);

                // 转换为AddParam并调用Service层的add方法以确保权限控制和orgId设置
                BizVoucherAddParam addParam = BeanUtil.toBean(voucher, BizVoucherAddParam.class);
                bizVoucherService.add(addParam);
            }
        }
        return CommonResult.ok();
    }

    /**
     * 编辑代金券信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    @Operation(summary = "编辑代金券信息")
    @CommonLog("编辑代金券信息")
    @SaCheckPermission("/biz/voucher/edit")
    @PostMapping("/biz/voucher/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizVoucherEditParam bizVoucherEditParam) {
        bizVoucherService.edit(bizVoucherEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除代金券信息
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    @Operation(summary = "删除代金券信息")
    @CommonLog("删除代金券信息")
    @SaCheckPermission("/biz/voucher/delete")
    @PostMapping("/biz/voucher/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizVoucherIdParam> bizVoucherIdParamList) {
        // 调用Service层的delete方法以确保权限检查
        bizVoucherService.delete(bizVoucherIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取代金券信息详情
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    @Operation(summary = "获取代金券信息详情")
    @SaCheckPermission("/biz/voucher/detail")
    @GetMapping("/biz/voucher/detail")
    public CommonResult<BizVoucher> detail(@Valid BizVoucherIdParam bizVoucherIdParam) {
        return CommonResult.data(bizVoucherService.detail(bizVoucherIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 17:17
     */
    @Operation(summary = "获取代金券信息动态字段的配置")
    @SaCheckPermission("/biz/voucher/dynamicFieldConfigList")
    @GetMapping("/biz/voucher/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizVoucherService.dynamicFieldConfigList(columnName));
    }
}
