
package vip.xiaonuo.flw.modular.model.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.flw.modular.model.entity.FlwModel;
import vip.xiaonuo.flw.modular.model.param.*;
import vip.xiaonuo.flw.modular.model.result.FlwModelOrgResult;
import vip.xiaonuo.flw.modular.model.result.FlwModelPositionResult;
import vip.xiaonuo.flw.modular.model.result.FlwModelRoleResult;
import vip.xiaonuo.flw.modular.model.result.FlwModelUserResult;

import java.util.List;

/**
 * 模型Service接口
 *
 * <AUTHOR>
 * @date 2022/5/11 15:32
 **/
public interface FlwModelService extends IService<FlwModel> {

    /**
     * 获取模型分页
     *
     * <AUTHOR>
     * @date 2022/4/24 20:08
     */
    Page<FlwModel> page(FlwModelPageParam flwModelPageParam);

    /**
     * 获取所有模型列表
     *
     * <AUTHOR>
     * @date 2022/4/24 20:08
     */
    List<FlwModel> allList(FlwModelListParam flwModelListParam);

    /**
     * 获取我可以发起的模型列表
     *
     * <AUTHOR>
     * @date 2022/4/24 20:08
     */
    List<FlwModel> myList(FlwModelMyParam flwModelMyParam);

    /**
     * 添加模型
     *
     * <AUTHOR>
     * @date 2022/4/24 20:48
     */
    void add(FlwModelAddParam flwModelAddParam);

    /**
     * 编辑模型
     *
     * <AUTHOR>
     * @date 2022/4/24 21:13
     */
    void edit(FlwModelEditParam flwModelEditParam);

    /**
     * 删除模型
     *
     * <AUTHOR>
     * @date 2022/4/24 21:18
     */
    void delete(List<FlwModelIdParam> flwModelIdParamList);

    /**
     * 部署模型
     *
     * <AUTHOR>
     * @date 2022/5/22 14:56
     */
    void deploy(FlwModelIdParam flwModelIdParam);

    /**
     * 获取模型详情
     *
     * <AUTHOR>
     * @date 2022/4/24 21:18
     */
    FlwModel detail(FlwModelIdParam flwModelIdParam);

    /**
     * 获取模型详情
     *
     * <AUTHOR>
     * @date 2022/4/24 21:18
     */
    FlwModel queryEntity(String id);

    /**
     * 停用模型
     *
     * <AUTHOR>
     * @date 2022/8/14 18:58
     */
    void disableModel(FlwModelIdParam flwModelIdParam);

    /**
     * 启用模型
     *
     * <AUTHOR>
     * @date 2022/8/14 18:58
     */
    void enableModel(FlwModelIdParam flwModelIdParam);

    /**
     * 模型降版
     *
     * <AUTHOR>
     * @date 2022/8/14 18:58
     */
    void downVersion(FlwModelIdParam flwModelIdParam);

    /* ====模型部分所需要用到的选择器==== */

    /**
     * 获取组织树选择器
     *
     * <AUTHOR>
     * @date 2022/5/13 21:00
     */
    List<Tree<String>> orgTreeSelector();

    /**
     * 获取组织列表选择器
     *
     * <AUTHOR>
     * @date 2022/7/22 13:34
     **/
    Page<FlwModelOrgResult> orgListSelector(FlwModelSelectorOrgListParam flwModelSelectorOrgListParam);

    /**
     * 获取职位选择器
     *
     * <AUTHOR>
     * @date 2022/5/13 21:00
     */
    Page<FlwModelPositionResult> positionSelector(FlwModelSelectorPositionParam flwModelSelectorPositionParam);

    /**
     * 获取角色选择器
     *
     * <AUTHOR>
     * @date 2022/5/13 21:00
     */
    Page<FlwModelRoleResult> roleSelector(FlwModelSelectorRoleParam flwModelSelectorRoleParam);

    /**
     * 获取用户选择器
     *
     * <AUTHOR>
     * @date 2022/4/24 20:08
     */
    Page<FlwModelUserResult> userSelector(FlwModelSelectorUserParam flwModelSelectorUserParam);

    /**
     * 获取执行监听器选择器
     *
     * <AUTHOR>
     * @date 2023/5/11 15:05
     **/
    List<String> executionListenerSelector();

    /**
     * 获取自定义事件执行监听器选择器
     *
     * <AUTHOR>
     * @date 2023/5/11 15:05
     **/
    List<String> executionListenerSelectorForCustomEvent();

    /**
     * 获取任务监听器选择器
     *
     * <AUTHOR>
     * @date 2023/5/11 15:05
     **/
    List<String> taskListenerSelector();

    /**
     * 校验模型数据是否可被部署
     *
     * <AUTHOR>
     * @date 2023/5/27 16:15
     */
    FlwModel validModel(String modelId);

}
