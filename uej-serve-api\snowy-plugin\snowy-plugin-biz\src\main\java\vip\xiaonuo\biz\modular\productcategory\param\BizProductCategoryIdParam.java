package vip.xiaonuo.biz.modular.productcategory.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 产品类目ID参数
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Getter
@Setter
public class BizProductCategoryIdParam {

    /** id */
    @Schema(description = "id")
    @NotBlank(message = "ID cannot be empty")
    private String id;
} 