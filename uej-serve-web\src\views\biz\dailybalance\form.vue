<template>
    <xn-form-container
        :title="formData.id ? '编辑项目结款记录表' : '增加项目结款记录表'"
        :width="700"
        v-model:open="open"
        :destroy-on-close="true"
        @close="onClose"
    >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="item：" name="item">
                        <a-input v-model:value="formData.item" placeholder="Please enter item" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="receivable amount：" name="receivableAmount">
                        <a-input v-model:value="formData.receivableAmount" placeholder="Please enter receivable amount" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="received amount：" name="receivedAmount">
                        <a-input v-model:value="formData.receivedAmount" placeholder="Please enter received amount" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="balance：" name="balance">
                        <a-input v-model:value="formData.balance" placeholder="Please enter balance" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="remark：" name="remark">
                        <a-input v-model:value="formData.remark" placeholder="Please enter remark" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="orgId：" name="orgId">
                        <a-input v-model:value="formData.orgId" placeholder="Please enter orgId" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="orgName：" name="orgName">
                        <a-input v-model:value="formData.orgName" placeholder="Please enter orgName" allow-clear />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
        <a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
            <a-row :gutter="16">
                <a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
                    <xn-form-item :fieldConfig="item" :formData="dynamicFormData"/>
                </a-col>
            </a-row>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
        </template>
    </xn-form-container>
</template>

<script setup name="bizDailyBalanceForm">
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import bizDailyBalanceApi from '@/api/biz/bizDailyBalanceApi'
    // 抽屉状态
    const open = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    // 动态表单
    const dynamicFormRef = ref()
    const dynamicFieldConfigList = ref([])
    const dynamicFormData = ref({})

    // 打开抽屉
    const onOpen = (record) => {
        open.value = true
        bizDailyBalanceApi.bizDailyBalanceDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
            dynamicFieldConfigList.value = data
        })
        if (record) {
            let recordData = cloneDeep(record)
            formData.value = Object.assign({}, recordData)
            dynamicFormData.value = JSON.parse(formData.value.extJson) || {}
        }
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        dynamicFormData.value = {}
        open.value = false
    }
    // 默认要校验的
    const formRules = {
    }
    // 验证并提交数据
    const onSubmit = () => {
        const promiseList = []
        promiseList.push(
            new Promise((resolve, reject) => {
                formRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        promiseList.push(
            new Promise((resolve, reject) => {
                dynamicFormRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        submitLoading.value = true
        Promise.all(promiseList)
            .then(() => {
            const formDataParam = cloneDeep(formData.value)
            formDataParam.extJson = JSON.stringify(dynamicFormData.value)
            bizDailyBalanceApi
                .bizDailyBalanceSubmitForm(formDataParam, formDataParam.id)
                .then(() => {
                    onClose()
                    emit('successful')
                })
                .finally(() => {
                    submitLoading.value = false
                })
            })
            .catch(() => {})
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
