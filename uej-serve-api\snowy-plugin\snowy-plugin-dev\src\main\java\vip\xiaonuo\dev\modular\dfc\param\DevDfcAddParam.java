
package vip.xiaonuo.dev.modular.dfc.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态字段配置添加参数
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 **/
@Getter
@Setter
public class DevDfcAddParam {

    /** 数据源 */
    @Schema(description = "数据源")
    private String dbsId;

    /** 表名称 */
    @Schema(description = "表名称")
    private String tableName;

    /** 字段名称 */
    @Schema(description = "字段名称")
    private String columnName;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;

    /** 表单域属性名 */
    @Schema(description = "表单域属性名")
    private String name;

    /** 标签文本 */
    @Schema(description = "标签文本")
    private String label;

    /** 必填 */
    @Schema(description = "必填")
    private Integer required;

    /** 提示语 */
    @Schema(description = "提示语")
    private String placeholder;

    /** 字段类型 */
    @Schema(description = "字段类型")
    private String type;

    /** 选择项类型 */
    @Schema(description = "选择项类型")
    private String selectOptionType;

    /** 字典 */
    @Schema(description = "字典")
    private String dictTypeCode;

    /** 选择项api地址 */
    @Schema(description = "选择项api地址")
    private String selOptionApiUrl;

    /** 已选择数据api地址 */
    @Schema(description = "已选择数据api地址")
    private String selDataApiUrl;

    /** 是否多选 */
    @Schema(description = "是否多选")
    private Integer isMultiple;
}
