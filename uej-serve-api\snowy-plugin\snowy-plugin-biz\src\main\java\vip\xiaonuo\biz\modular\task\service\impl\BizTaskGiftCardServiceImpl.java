package vip.xiaonuo.biz.modular.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import vip.xiaonuo.biz.modular.task.entity.BizTaskGiftCard;
import vip.xiaonuo.biz.modular.task.mapper.BizTaskGiftCardMapper;
import vip.xiaonuo.biz.modular.task.service.BizTaskGiftCardService;

import java.util.List;

/**
 * 任务礼品卡关联表Service实现类
 *
 * <AUTHOR>
 * @date  2024/12/24
 **/
@Service
public class BizTaskGiftCardServiceImpl extends ServiceImpl<BizTaskGiftCardMapper, BizTaskGiftCard> implements BizTaskGiftCardService {

    @Override
    public List<BizTaskGiftCard> getByTaskId(String taskId) {
        return this.list(new LambdaQueryWrapper<BizTaskGiftCard>()
                .eq(BizTaskGiftCard::getTaskId, taskId));
    }

    @Override
    public void saveTaskGiftCards(String taskId, List<BizTaskGiftCard> giftCards) {
        // 先删除任务的所有礼品卡
        this.deleteByTaskId(taskId);
        
        // 重新保存礼品卡列表
        if (giftCards != null && !giftCards.isEmpty()) {
            giftCards.forEach(giftCard -> {
                giftCard.setTaskId(taskId);
                giftCard.setId(null); // 确保ID为空，让数据库自动生成
            });
            this.saveBatch(giftCards);
        }
    }

    @Override
    public void deleteByTaskId(String taskId) {
        this.remove(new LambdaQueryWrapper<BizTaskGiftCard>()
                .eq(BizTaskGiftCard::getTaskId, taskId));
    }
} 