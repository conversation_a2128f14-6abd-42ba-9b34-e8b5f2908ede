<template>
	<DragModal
		:border="false"
		:visible="visible"
		@close="onClose"
		:width="1500"
		title="offeringgroupitem"
	>
		<a-card :bordered="false">
			<s-table
				ref="tableRef"
				:columns="columns"
				:data="loadData"
				:alert="options.alert.show"
				bordered
				:row-key="(record) => record.id"
				:tool-config="toolConfig"
				:row-selection="options.rowSelection"
			>
				<template #operator class="table-operator">
					<a-space>
						<a-button type="primary" @click="()=>{formRef.onOpen(),formRef.setMainId({mainId:searchFormState.mainId})}" v-if="hasPerm('bizOfferingGroupItemAdd')">
							<template #icon><plus-outlined /></template>
							{{ $t('common.addButton') }}
						</a-button>
						<xn-batch-delete
							v-if="hasPerm('bizOfferingGroupItemBatchDelete')"
							:selectedRowKeys="selectedRowKeys"
							@batchDelete="deleteBatchBizOfferingGroupItem"
							:buttonName="$t('common.batchRemoveButton')"
						/>
					</a-space>
				</template>
				<template #bodyCell="{ column, record }">
					<template v-if="column.dataIndex === 'action'">
						<a-space>
							<a @click="formRef.onOpen(record)" v-if="hasPerm('bizOfferingGroupItemEdit')">{{
								$t('common.editButton')
							}}</a>
							<a-divider
								type="vertical"
								v-if="hasPerm(['bizOfferingGroupItemEdit', 'bizOfferingGroupItemDelete'], 'and')"
							/>
							<a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizOfferingGroupItem(record)">
								<a-button type="link" danger size="small" v-if="hasPerm('bizOfferingGroupItemDelete')">{{
									$t('common.removeButton')
								}}</a-button>
							</a-popconfirm>
						</a-space>
					</template>
				</template>
			</s-table>
		</a-card>
	</DragModal>
	<Form ref="formRef" @successful="tableRef.refresh(true)" />
</template>

<script setup>
	import { cloneDeep } from 'lodash-es'
	import Form from '@/views/biz/offeringgroupitem/form.vue'
	import bizOfferingGroupItemApi from '@/api/biz/bizOfferingGroupItemApi'
	const searchFormState = ref({})
	const searchFormRef = ref()
	const tableRef = ref()
	const formRef = ref()
	const visible = ref(false)
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	const columns = [
		{
			title: 'OFFERING_ID',
			dataIndex: 'offeringId'
		},
		{
			title: 'OFFERING_NAME',
			dataIndex: 'offeringName'
		},
		{
			title: 'LIST PRICE',
			dataIndex: 'listPrice'
		},
		{
			title: 'PLANNING HOURS',
			dataIndex: 'planningHours',
			needTotal: true,
		},
		{
			title: 'NUM',
			dataIndex: 'num',
			needTotal: true,
		},
		{
			title: 'TOTAL PRICE',
			dataIndex: 'totalPrice',
			needTotal: true,
		}
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['bizOfferingGroupItemEdit', 'bizOfferingGroupItemDelete'])) {
		columns.push({
			title: 'action',
			dataIndex: 'action',
			align: 'center',
			width: '150px'
		})
	}
	const selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		// columns数字类型字段加入 needTotal: true 可以勾选自动算账
		alert: {
			show: true,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		const searchFormParam = cloneDeep(searchFormState.value)
		return bizOfferingGroupItemApi.bizOfferingGroupItemPage(Object.assign(parameter, searchFormParam)).then((data) => {
			return data
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 删除
	const deleteBizOfferingGroupItem = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		bizOfferingGroupItemApi.bizOfferingGroupItemDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchBizOfferingGroupItem = (params) => {
		bizOfferingGroupItemApi.bizOfferingGroupItemDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}

	const onOpen = (record) => {
		visible.value = true
		searchFormState.value.mainId=record.id
		tableRef.value?.refresh(true)
	}

	const onClose = () => {
		visible.value = false
		searchFormState.value = {}
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>

<style lang="less" scoped></style>
