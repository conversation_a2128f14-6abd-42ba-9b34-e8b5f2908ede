package vip.xiaonuo.biz.modular.workhours.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工时实体
 *
 * <AUTHOR>
 * @date  2024/06/12 17:27
 **/
@Getter
@Setter
@TableName("biz_work_hours")
public class BizWorkHours {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** Name */
    @Schema(description = "Name")
    private String name;

    /** Start Time */
    @Schema(description = "Start Time")
    private String startTime;

    /** End Time */
    @Schema(description = "End Time")
    private String endTime;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 组织id */
    @Schema(description = "组织id")
    private String orgId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String extJson;
}
