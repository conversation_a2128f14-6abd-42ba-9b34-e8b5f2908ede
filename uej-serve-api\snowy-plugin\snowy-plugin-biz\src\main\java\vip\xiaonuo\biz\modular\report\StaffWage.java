package vip.xiaonuo.biz.modular.report;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StaffWage {

    @ExcelProperty({"name"})
    private String name;

    @ExcelProperty({"date"})
    private String date;

    @ExcelProperty({"taskNumber"})
    private Long taskNumber;

    @ExcelProperty({"salary"})
    private BigDecimal salary;

    @ExcelProperty({"tips"})
    private String tips;
}
