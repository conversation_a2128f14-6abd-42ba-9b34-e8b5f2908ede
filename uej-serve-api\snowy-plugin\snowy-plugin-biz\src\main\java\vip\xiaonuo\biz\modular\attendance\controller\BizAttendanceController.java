
package vip.xiaonuo.biz.modular.attendance.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.auth.core.pojo.SaBaseLoginUser;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.biz.modular.org.entity.BizOrg;
import vip.xiaonuo.biz.modular.org.service.BizOrgService;
import vip.xiaonuo.biz.modular.user.entity.BizUser;
import vip.xiaonuo.biz.modular.user.service.BizUserService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.attendance.entity.BizAttendance;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceAddParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceEditParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceIdParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendancePageParam;
import vip.xiaonuo.biz.modular.attendance.service.BizAttendanceService;
import cn.hutool.json.JSONObject;

import java.util.Date;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 考勤记录控制器
 *
 * <AUTHOR>
 * @date  2024/08/01 13:54
 */
@Tag(name = "考勤记录控制器")
@RestController
@Validated
public class BizAttendanceController {

    @Resource
    private BizAttendanceService bizAttendanceService;

    @Resource
    private BizUserService bizUserService;

    @Resource
    private BizOrgService bizOrgService;

    /**
     * 获取考勤记录分页
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    @Operation(summary = "获取考勤记录分页")
    @SaCheckPermission("/biz/attendance/page")
    @GetMapping("/biz/attendance/page")
    public CommonResult<Page<BizAttendance>> page(BizAttendancePageParam bizAttendancePageParam) {
        return CommonResult.data(bizAttendanceService.page(bizAttendancePageParam));
    }

    /**
     * 添加考勤记录
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    @Operation(summary = "添加考勤记录")
    @CommonLog("添加考勤记录")
    @SaCheckPermission("/biz/attendance/add")
    @PostMapping("/biz/attendance/add")
    public CommonResult<String> add(@RequestBody @Valid BizAttendanceAddParam bizAttendanceAddParam) {
        bizAttendanceService.add(bizAttendanceAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑考勤记录
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    @Operation(summary = "编辑考勤记录")
    @CommonLog("编辑考勤记录")
    @SaCheckPermission("/biz/attendance/edit")
    @PostMapping("/biz/attendance/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizAttendanceEditParam bizAttendanceEditParam) {
        bizAttendanceService.edit(bizAttendanceEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除考勤记录
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    @Operation(summary = "删除考勤记录")
    @CommonLog("删除考勤记录")
    @SaCheckPermission("/biz/attendance/delete")
    @PostMapping("/biz/attendance/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizAttendanceIdParam> bizAttendanceIdParamList) {
        bizAttendanceService.delete(bizAttendanceIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取考勤记录详情
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    @Operation(summary = "获取考勤记录详情")
    @SaCheckPermission("/biz/attendance/detail")
    @GetMapping("/biz/attendance/detail")
    public CommonResult<BizAttendance> detail(@Valid BizAttendanceIdParam bizAttendanceIdParam) {
        return CommonResult.data(bizAttendanceService.detail(bizAttendanceIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    @Operation(summary = "获取考勤记录动态字段的配置")
    @SaCheckPermission("/biz/attendance/dynamicFieldConfigList")
    @GetMapping("/biz/attendance/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizAttendanceService.dynamicFieldConfigList(columnName));
    }

    @Operation(summary = "考勤打卡")
    @GetMapping("/biz/attendance/check")
    public CommonResult<String> check(String type, String time) {
        String now = DateUtil.now();
        String day = DateUtil.format(new Date(), "yyyyMMdd");
        if (ObjectUtil.isEmpty(time) || !time.equals(day)) {
            return CommonResult.error("Invalid QR code");
        }
        SaBaseLoginUser staff = StpLoginUserUtil.getLoginUser();
        String staffId = staff.getId();
        if (type.equals("clockIn")) {
            List<BizAttendance> todayAttendances = bizAttendanceService.lambdaQuery()
                    .eq(BizAttendance::getStaffId, staffId)
                    .like(BizAttendance::getCreateTime, DateUtil.format(DateUtil.date(), "yyyy-MM-dd")).list();
            if (!todayAttendances.isEmpty()) {
                return CommonResult.error("You have already checked !");
            }
            BizAttendance todayAttendance = new BizAttendance();
            todayAttendance.setStaffId(staffId);
            todayAttendance.setStaffName(staff.getName());
            todayAttendance.setOrgId(staff.getOrgId());
            BizOrg org = bizOrgService.queryEntity(staff.getOrgId());
            todayAttendance.setOrgName(org.getName());
            todayAttendance.setWorkTime(Convert.toDate(now));
            bizAttendanceService.saveOrUpdate(todayAttendance);
        }
        if (type.equals("clockOut")) {
            List<BizAttendance> todayAttendances = bizAttendanceService.lambdaQuery()
                    .eq(BizAttendance::getStaffId, staffId)
                    .like(BizAttendance::getCreateTime, DateUtil.format(DateUtil.date(), "yyyy-MM-dd")).list();
            if (todayAttendances.isEmpty()) {
                return CommonResult.error("Please clock in first");
            }
            for (BizAttendance todayAttendance : todayAttendances) {
                todayAttendance.setClosingTime(Convert.toDate(now));
            }
            bizAttendanceService.saveOrUpdateBatch(todayAttendances);
        }
        return CommonResult.ok();
    }
}
