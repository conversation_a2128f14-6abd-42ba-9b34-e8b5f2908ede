
package vip.xiaonuo.flw.modular.process.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.flw.modular.task.result.FlwTaskAttachmentResult;
import vip.xiaonuo.flw.modular.task.result.FlwTaskDetailResult;

import java.util.List;

/**
 * 流程详情结果
 *
 * <AUTHOR>
 * @date 2022/8/23 15:09
 **/
@Getter
@Setter
public class FlwProcessDetailResult {

    /** 模型id */
    @Schema(description = "模型id")
    private String modelId;

    /** 流程实例id */
    @Schema(description = "流程实例id")
    private String processInstanceId;

    /** 表单类型 */
    @Schema(description = "表单类型")
    private String formType;

    /** 模型信息 */
    @Schema(description = "模型信息")
    private String initiatorModelJson;

    /** 表单信息 */
    @Schema(description = "表单信息")
    private String initiatorFormJson;

    /** 填写数据 */
    @Schema(description = "填写数据")
    private String initiatorDataJson;

    /** 状态值 */
    @Schema(description = "状态值")
    private String stateText;

    /** 状态码 */
    @Schema(description = "状态码")
    private String stateCode;

    /** 审批记录 */
    @Schema(description = "审批记录")
    private List<FlwTaskDetailResult.FlwProcessComment> commentList;

    /**
     * 审批记录类
     *
     * <AUTHOR>
     * @date 2022/8/23 15:48
     **/
    @Getter
    @Setter
    public static class FlwProcessComment {

        /** 任务id */
        @Schema(description = "任务id")
        private String taskId;

        /** 任务名称 */
        @Schema(description = "任务名称")
        private String taskName;

        /** 用户id */
        @Schema(description = "用户id")
        private String userId;

        /** 用户名称 */
        @Schema(description = "用户名称")
        private String userName;

        /** 审批操作 */
        @Schema(description = "审批操作")
        private String operate;

        /** 审批意见 */
        @Schema(description = "审批意见")
        private String comment;

        /** 审批时间 */
        @Schema(description = "审批时间")
        private String approveTime;

        /** 审批附件 */
        @Schema(description = "审批附件")
        private List<FlwTaskAttachmentResult> attachmentList;
    }
}
