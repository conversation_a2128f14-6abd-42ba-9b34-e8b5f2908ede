package vip.xiaonuo.biz.modular.giftcard.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 礼品卡验证参数
 *
 * <AUTHOR>
 * @date  2024/12/19
 **/
@Getter
@Setter
public class BizGiftCardVerifyParam {

    /** 卡号 */
    @Schema(description = "卡号")
    @NotBlank(message = "卡号不能为空")
    private String cardNo;

    /** PIN码 */
    @Schema(description = "PIN码")
    @NotBlank(message = "PIN码不能为空")
    private String pin;
} 