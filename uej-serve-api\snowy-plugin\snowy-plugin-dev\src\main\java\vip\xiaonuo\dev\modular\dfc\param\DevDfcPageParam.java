
package vip.xiaonuo.dev.modular.dfc.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态字段配置查询参数
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 **/
@Getter
@Setter
public class DevDfcPageParam {

    /** 当前页 */
    @Schema(description = "当前页码")
    private Integer current;

    /** 每页条数 */
    @Schema(description = "每页条数")
    private Integer size;

    /** 排序字段 */
    @Schema(description = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @Schema(description = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @Schema(description = "关键词")
    private String searchKey;

    /** 数据源 */
    @Schema(description = "数据源")
    private String dbsId;

    /** 表名称 */
    @Schema(description = "表名称")
    private String tableName;
    /** 表名称 */
    @Schema(description = "字段名称")
    private String columnName;
}
