
package vip.xiaonuo.biz.modular.user.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 人员授权角色参数
 *
 * <AUTHOR>
 * @date 2022/4/21 16:13
 **/
@Getter
@Setter
public class BizUserGrantRoleParam {

    /** id */
    @Schema(description = "id")
    @NotBlank(message = "id不能为空")
    private String id;

    /** 角色id集合 */
    @Schema(description = "角色id集合")
    @NotNull(message = "roleIdList不能为空")
    private List<String> roleIdList;
}
