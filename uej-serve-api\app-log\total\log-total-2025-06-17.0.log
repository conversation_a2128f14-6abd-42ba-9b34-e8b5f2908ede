2025-06-17T09:12:37.979+08:00  INFO 2212 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 2212 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T09:12:37.984+08:00  INFO 2212 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T09:12:41.981+08:00  INFO 2212 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T09:12:41.990+08:00  INFO 2212 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T09:12:42.356+08:00  INFO 2212 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 320 ms. Found 0 Redis repository interfaces.
2025-06-17T09:12:42.852+08:00  WARN 2212 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T09:12:43.209+08:00  WARN 2212 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T09:12:43.737+08:00  WARN 2212 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T09:12:44.179+08:00  INFO 2212 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T09:12:44.201+08:00  INFO 2212 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T09:12:44.201+08:00  INFO 2212 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T09:12:44.312+08:00  INFO 2212 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T09:12:44.313+08:00  INFO 2212 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6269 ms
2025-06-17T09:12:44.711+08:00  INFO 2212 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T09:12:45.042+08:00  INFO 2212 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T09:12:45.043+08:00  INFO 2212 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T09:12:45.045+08:00  INFO 2212 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T09:12:45.045+08:00  INFO 2212 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T09:12:45.367+08:00  INFO 2212 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T09:12:49.626+08:00  INFO 2212 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T09:12:50.451+08:00  INFO 2212 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T09:12:52.449+08:00  INFO 2212 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@2719139f, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T09:12:52.487+08:00  INFO 2212 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T09:12:52.488+08:00  INFO 2212 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T09:12:52.488+08:00  INFO 2212 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T09:12:52.488+08:00  INFO 2212 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T09:12:52.504+08:00  INFO 2212 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T09:12:52.979+08:00  INFO 2212 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@5ce045e3)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@2fb3c930, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T09:12:55.342+08:00  INFO 2212 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T09:12:55.349+08:00  INFO 2212 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T09:12:55.520+08:00  INFO 2212 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T09:12:59.303+08:00  INFO 2212 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T09:12:59.468+08:00  INFO 2212 --- [main] vip.xiaonuo.Application                  : Started Application in 22.635 seconds (process running for 24.29)
2025-06-17T09:12:59.591+08:00  INFO 2212 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T09:25:50.867+08:00  INFO 2212 --- [http-nio-10082-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T09:25:50.867+08:00  INFO 2212 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T09:25:50.872+08:00  INFO 2212 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-06-17T09:25:51.163+08:00  WARN 2212 --- [http-nio-10082-exec-4] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [108] milliseconds.
2025-06-17T09:25:51.163+08:00  WARN 2212 --- [http-nio-10082-exec-3] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [108] milliseconds.
2025-06-17T09:41:37.884+08:00  WARN 2212 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T09:41:37.893+08:00  WARN 2212 --- [http-nio-10082-exec-6] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T09:41:43.910+08:00  WARN 2212 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T09:41:43.917+08:00  WARN 2212 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T09:42:13.473+08:00  WARN 2212 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T09:42:13.481+08:00  WARN 2212 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T09:46:56.919+08:00  WARN 2212 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T09:46:56.926+08:00  WARN 2212 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T09:47:01.747+08:00  WARN 2212 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T09:47:01.753+08:00  WARN 2212 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T10:03:04.270+08:00 ERROR 2212 --- [http-nio-10082-exec-9] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/report/revenue，具体信息：

java.lang.NullPointerException: Cannot invoke "java.util.Date.getTime()" because "date" is null
	at cn.hutool.core.date.CalendarUtil.calendar(CalendarUtil.java:48)
	at cn.hutool.core.date.DateUtil.beginOfWeek(DateUtil.java:1192)
	at vip.xiaonuo.biz.modular.report.ReportController.getParamDate(ReportController.java:244)
	at vip.xiaonuo.biz.modular.report.ReportController.revenue(ReportController.java:52)
	at jdk.internal.reflect.GeneratedMethodAccessor568.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T10:03:05.312+08:00 ERROR 2212 --- [http-nio-10082-exec-8] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/report/revenue，具体信息：

java.lang.NullPointerException: Cannot invoke "java.util.Date.getTime()" because "date" is null
	at cn.hutool.core.date.CalendarUtil.calendar(CalendarUtil.java:48)
	at cn.hutool.core.date.DateUtil.beginOfMonth(DateUtil.java:1224)
	at vip.xiaonuo.biz.modular.report.ReportController.getParamDate(ReportController.java:253)
	at vip.xiaonuo.biz.modular.report.ReportController.revenue(ReportController.java:52)
	at jdk.internal.reflect.GeneratedMethodAccessor568.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T10:03:06.484+08:00 ERROR 2212 --- [http-nio-10082-exec-2] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/report/revenue，具体信息：

java.lang.NullPointerException: Cannot invoke "java.util.Date.getTime()" because "date" is null
	at cn.hutool.core.date.CalendarUtil.calendar(CalendarUtil.java:48)
	at cn.hutool.core.date.DateUtil.beginOfQuarter(DateUtil.java:1244)
	at vip.xiaonuo.biz.modular.report.ReportController.getParamDate(ReportController.java:258)
	at vip.xiaonuo.biz.modular.report.ReportController.revenue(ReportController.java:52)
	at jdk.internal.reflect.GeneratedMethodAccessor568.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T10:03:07.346+08:00 ERROR 2212 --- [http-nio-10082-exec-6] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/report/revenue，具体信息：

java.lang.NullPointerException: Cannot invoke "java.util.Date.getTime()" because "date" is null
	at cn.hutool.core.date.CalendarUtil.calendar(CalendarUtil.java:48)
	at cn.hutool.core.date.DateUtil.beginOfYear(DateUtil.java:1264)
	at vip.xiaonuo.biz.modular.report.ReportController.getParamDate(ReportController.java:262)
	at vip.xiaonuo.biz.modular.report.ReportController.revenue(ReportController.java:52)
	at jdk.internal.reflect.GeneratedMethodAccessor568.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T10:03:09.094+08:00 ERROR 2212 --- [http-nio-10082-exec-7] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/report/revenue，具体信息：

java.lang.NullPointerException: Cannot invoke "java.util.Date.getTime()" because "date" is null
	at cn.hutool.core.date.CalendarUtil.calendar(CalendarUtil.java:48)
	at cn.hutool.core.date.DateUtil.beginOfWeek(DateUtil.java:1192)
	at vip.xiaonuo.biz.modular.report.ReportController.getParamDate(ReportController.java:244)
	at vip.xiaonuo.biz.modular.report.ReportController.revenue(ReportController.java:52)
	at jdk.internal.reflect.GeneratedMethodAccessor568.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T10:03:17.099+08:00 ERROR 2212 --- [http-nio-10082-exec-10] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/report/revenue，具体信息：

java.lang.NullPointerException: Cannot invoke "java.util.Date.getTime()" because "date" is null
	at cn.hutool.core.date.CalendarUtil.calendar(CalendarUtil.java:48)
	at cn.hutool.core.date.DateUtil.beginOfMonth(DateUtil.java:1224)
	at vip.xiaonuo.biz.modular.report.ReportController.getParamDate(ReportController.java:253)
	at vip.xiaonuo.biz.modular.report.ReportController.revenue(ReportController.java:52)
	at jdk.internal.reflect.GeneratedMethodAccessor568.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T10:03:17.868+08:00 ERROR 2212 --- [http-nio-10082-exec-3] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/report/revenue，具体信息：

java.lang.NullPointerException: Cannot invoke "java.util.Date.getTime()" because "date" is null
	at cn.hutool.core.date.CalendarUtil.calendar(CalendarUtil.java:48)
	at cn.hutool.core.date.DateUtil.beginOfWeek(DateUtil.java:1192)
	at vip.xiaonuo.biz.modular.report.ReportController.getParamDate(ReportController.java:244)
	at vip.xiaonuo.biz.modular.report.ReportController.revenue(ReportController.java:52)
	at jdk.internal.reflect.GeneratedMethodAccessor568.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T11:27:06.325+08:00  INFO 2212 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T11:27:06.382+08:00  INFO 2212 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T11:27:06.397+08:00  INFO 2212 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T11:27:06.431+08:00  INFO 2212 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T11:27:06.432+08:00  INFO 2212 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T11:27:06.433+08:00  INFO 2212 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T11:27:06.441+08:00  INFO 2212 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T11:27:06.442+08:00  INFO 2212 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T11:27:06.442+08:00  INFO 2212 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T11:27:27.814+08:00  INFO 2948 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 2948 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T11:27:27.817+08:00  INFO 2948 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T11:27:32.229+08:00  INFO 2948 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T11:27:32.234+08:00  INFO 2948 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T11:27:32.470+08:00  INFO 2948 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 208 ms. Found 0 Redis repository interfaces.
2025-06-17T11:27:32.851+08:00  WARN 2948 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T11:27:33.169+08:00  WARN 2948 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T11:27:33.530+08:00  WARN 2948 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T11:27:33.933+08:00  INFO 2948 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T11:27:33.949+08:00  INFO 2948 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T11:27:33.950+08:00  INFO 2948 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T11:27:34.050+08:00  INFO 2948 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T11:27:34.050+08:00  INFO 2948 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6176 ms
2025-06-17T11:27:34.427+08:00  INFO 2948 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T11:27:34.831+08:00  INFO 2948 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T11:27:34.832+08:00  INFO 2948 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T11:27:34.834+08:00  INFO 2948 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T11:27:34.834+08:00  INFO 2948 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T11:27:35.191+08:00  INFO 2948 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T11:27:39.429+08:00  INFO 2948 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T11:27:40.349+08:00  INFO 2948 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T11:27:42.490+08:00  INFO 2948 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@154cbb46, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T11:27:42.521+08:00  INFO 2948 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T11:27:42.521+08:00  INFO 2948 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T11:27:42.521+08:00  INFO 2948 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T11:27:42.521+08:00  INFO 2948 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T11:27:42.530+08:00  INFO 2948 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T11:27:42.876+08:00  INFO 2948 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@6cb59aa)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@6e98fd10, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T11:27:45.254+08:00  INFO 2948 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T11:27:45.258+08:00  INFO 2948 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T11:27:45.397+08:00  INFO 2948 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T11:27:48.453+08:00  INFO 2948 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T11:27:48.552+08:00  INFO 2948 --- [main] vip.xiaonuo.Application                  : Started Application in 22.045 seconds (process running for 23.327)
2025-06-17T11:27:48.617+08:00  INFO 2948 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T11:27:51.520+08:00  INFO 2948 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T11:27:51.521+08:00  INFO 2948 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T11:27:51.523+08:00  INFO 2948 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-17T11:43:39.635+08:00  INFO 2948 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T11:43:39.707+08:00  INFO 2948 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T11:43:39.714+08:00  INFO 2948 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T11:43:39.725+08:00  INFO 2948 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T11:43:39.725+08:00  INFO 2948 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T11:43:39.726+08:00  INFO 2948 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T11:43:39.729+08:00  INFO 2948 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T11:43:39.729+08:00  INFO 2948 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T11:43:39.729+08:00  INFO 2948 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T11:44:01.896+08:00  INFO 34684 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 34684 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T11:44:01.900+08:00  INFO 34684 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T11:44:05.298+08:00  INFO 34684 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T11:44:05.301+08:00  INFO 34684 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T11:44:05.487+08:00  INFO 34684 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 168 ms. Found 0 Redis repository interfaces.
2025-06-17T11:44:05.846+08:00  WARN 34684 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T11:44:06.163+08:00  WARN 34684 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T11:44:06.491+08:00  WARN 34684 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T11:44:06.903+08:00  INFO 34684 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T11:44:06.923+08:00  INFO 34684 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T11:44:06.924+08:00  INFO 34684 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T11:44:07.041+08:00  INFO 34684 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T11:44:07.041+08:00  INFO 34684 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5082 ms
2025-06-17T11:44:07.399+08:00  INFO 34684 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T11:44:07.680+08:00  INFO 34684 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T11:44:07.681+08:00  INFO 34684 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T11:44:07.683+08:00  INFO 34684 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T11:44:07.683+08:00  INFO 34684 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T11:44:08.004+08:00  INFO 34684 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T11:44:12.117+08:00  INFO 34684 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T11:44:12.899+08:00  INFO 34684 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T11:44:14.943+08:00  INFO 34684 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@2e778abb, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T11:44:14.974+08:00  INFO 34684 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T11:44:14.974+08:00  INFO 34684 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T11:44:14.974+08:00  INFO 34684 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T11:44:14.974+08:00  INFO 34684 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T11:44:14.983+08:00  INFO 34684 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T11:44:15.370+08:00  INFO 34684 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@1c65740a)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@7421213d, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T11:44:17.625+08:00  INFO 34684 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T11:44:17.630+08:00  INFO 34684 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T11:44:17.763+08:00  INFO 34684 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T11:44:20.497+08:00  INFO 34684 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T11:44:20.599+08:00  INFO 34684 --- [main] vip.xiaonuo.Application                  : Started Application in 19.889 seconds (process running for 21.141)
2025-06-17T11:44:20.674+08:00  INFO 34684 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T11:44:21.537+08:00  INFO 34684 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T11:44:21.538+08:00  INFO 34684 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T11:44:21.542+08:00  INFO 34684 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-06-17T13:33:27.660+08:00  WARN 34684 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T14:20:52.993+08:00  INFO 34684 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T14:20:53.016+08:00  INFO 34684 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T14:20:53.021+08:00  INFO 34684 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T14:20:53.033+08:00  INFO 34684 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T14:20:53.033+08:00  INFO 34684 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T14:20:53.034+08:00  INFO 34684 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T14:20:53.036+08:00  INFO 34684 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T14:20:53.036+08:00  INFO 34684 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T14:20:53.036+08:00  INFO 34684 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T14:21:19.075+08:00  INFO 39584 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 39584 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T14:21:19.077+08:00  INFO 39584 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T14:21:22.051+08:00  INFO 39584 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T14:21:22.055+08:00  INFO 39584 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T14:21:22.291+08:00  INFO 39584 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 217 ms. Found 0 Redis repository interfaces.
2025-06-17T14:21:22.694+08:00  WARN 39584 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T14:21:23.030+08:00  WARN 39584 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T14:21:23.419+08:00  WARN 39584 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T14:21:23.916+08:00  INFO 39584 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T14:21:23.940+08:00  INFO 39584 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T14:21:23.940+08:00  INFO 39584 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T14:21:24.062+08:00  INFO 39584 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T14:21:24.063+08:00  INFO 39584 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4940 ms
2025-06-17T14:21:24.492+08:00  INFO 39584 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T14:21:24.945+08:00  INFO 39584 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T14:21:24.946+08:00  INFO 39584 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T14:21:24.948+08:00  INFO 39584 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T14:21:24.948+08:00  INFO 39584 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T14:21:25.298+08:00  INFO 39584 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T14:21:30.075+08:00  INFO 39584 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T14:21:31.001+08:00  INFO 39584 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T14:21:33.786+08:00  INFO 39584 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@2719139f, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T14:21:33.859+08:00  INFO 39584 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T14:21:33.859+08:00  INFO 39584 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T14:21:33.859+08:00  INFO 39584 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T14:21:33.860+08:00  INFO 39584 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T14:21:33.882+08:00  INFO 39584 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T14:21:34.502+08:00  INFO 39584 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@5ce045e3)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@2fb3c930, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T14:21:37.179+08:00  INFO 39584 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T14:21:37.185+08:00  INFO 39584 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T14:21:37.368+08:00  INFO 39584 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T14:21:40.914+08:00  INFO 39584 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T14:21:41.016+08:00  INFO 39584 --- [main] vip.xiaonuo.Application                  : Started Application in 23.165 seconds (process running for 24.65)
2025-06-17T14:21:41.089+08:00  INFO 39584 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T14:21:44.868+08:00  INFO 39584 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T14:21:44.868+08:00  INFO 39584 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T14:21:44.871+08:00  INFO 39584 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-17T14:30:10.667+08:00  WARN 39584 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T14:30:10.674+08:00  WARN 39584 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T14:37:40.464+08:00  WARN 39584 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T14:37:40.472+08:00  WARN 39584 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T14:37:46.200+08:00  WARN 39584 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T14:37:46.206+08:00  WARN 39584 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T15:05:29.269+08:00 ERROR 39584 --- [http-nio-10082-exec-9] v.x.core.handler.GlobalExceptionUtil     : >>> 服务器未知异常，请求地址：http://localhost:10082/biz/dailybalance/checkTodaySubmitted，具体信息：

org.springframework.web.servlet.resource.NoResourceFoundException: No static resource biz/dailybalance/checkTodaySubmitted.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at vip.xiaonuo.ten.core.filter.TenResolveFilter.doFilter(TenResolveFilter.java:65)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:842)

2025-06-17T15:07:17.771+08:00  INFO 39584 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T15:07:17.798+08:00  INFO 39584 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T15:07:17.806+08:00  INFO 39584 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T15:07:17.819+08:00  INFO 39584 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T15:07:17.819+08:00  INFO 39584 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T15:07:17.819+08:00  INFO 39584 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T15:07:17.825+08:00  INFO 39584 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T15:07:17.826+08:00  INFO 39584 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T15:07:17.826+08:00  INFO 39584 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T15:07:39.330+08:00  INFO 12924 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 12924 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T15:07:39.332+08:00  INFO 12924 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T15:07:42.423+08:00  INFO 12924 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T15:07:42.428+08:00  INFO 12924 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T15:07:42.681+08:00  INFO 12924 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 234 ms. Found 0 Redis repository interfaces.
2025-06-17T15:07:43.114+08:00  WARN 12924 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T15:07:43.524+08:00  WARN 12924 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T15:07:43.978+08:00  WARN 12924 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T15:07:44.400+08:00  INFO 12924 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T15:07:44.420+08:00  INFO 12924 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:07:44.421+08:00  INFO 12924 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T15:07:44.537+08:00  INFO 12924 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T15:07:44.538+08:00  INFO 12924 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5153 ms
2025-06-17T15:07:44.967+08:00  INFO 12924 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T15:07:45.327+08:00  INFO 12924 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T15:07:45.328+08:00  INFO 12924 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T15:07:45.330+08:00  INFO 12924 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T15:07:45.330+08:00  INFO 12924 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T15:07:45.675+08:00  INFO 12924 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T15:07:50.268+08:00  INFO 12924 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T15:07:51.079+08:00  INFO 12924 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T15:07:53.132+08:00  INFO 12924 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@2719139f, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T15:07:53.161+08:00  INFO 12924 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T15:07:53.161+08:00  INFO 12924 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T15:07:53.161+08:00  INFO 12924 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T15:07:53.162+08:00  INFO 12924 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T15:07:53.170+08:00  INFO 12924 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T15:07:53.516+08:00  INFO 12924 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@5ce045e3)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@2fb3c930, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T15:07:55.833+08:00  INFO 12924 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T15:07:55.837+08:00  INFO 12924 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T15:07:55.974+08:00  INFO 12924 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T15:07:58.638+08:00  INFO 12924 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T15:07:58.730+08:00  INFO 12924 --- [main] vip.xiaonuo.Application                  : Started Application in 20.794 seconds (process running for 22.269)
2025-06-17T15:07:58.796+08:00  INFO 12924 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T15:07:59.399+08:00  INFO 12924 --- [http-nio-10082-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T15:07:59.400+08:00  INFO 12924 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T15:07:59.402+08:00  INFO 12924 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-17T15:24:59.930+08:00  INFO 12924 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T15:24:59.955+08:00  INFO 12924 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T15:24:59.961+08:00  INFO 12924 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T15:24:59.970+08:00  INFO 12924 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T15:24:59.971+08:00  INFO 12924 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T15:24:59.971+08:00  INFO 12924 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T15:24:59.975+08:00  INFO 12924 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T15:24:59.975+08:00  INFO 12924 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T15:24:59.975+08:00  INFO 12924 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T15:25:22.040+08:00  INFO 37468 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 37468 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T15:25:22.043+08:00  INFO 37468 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T15:25:24.977+08:00  INFO 37468 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T15:25:24.983+08:00  INFO 37468 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T15:25:25.201+08:00  INFO 37468 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 201 ms. Found 0 Redis repository interfaces.
2025-06-17T15:25:25.558+08:00  WARN 37468 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T15:25:25.902+08:00  WARN 37468 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T15:25:26.270+08:00  WARN 37468 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T15:25:26.678+08:00  INFO 37468 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T15:25:26.696+08:00  INFO 37468 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:25:26.696+08:00  INFO 37468 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T15:25:26.810+08:00  INFO 37468 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T15:25:26.811+08:00  INFO 37468 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4706 ms
2025-06-17T15:25:27.220+08:00  INFO 37468 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T15:25:27.550+08:00  INFO 37468 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T15:25:27.551+08:00  INFO 37468 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T15:25:27.552+08:00  INFO 37468 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T15:25:27.553+08:00  INFO 37468 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T15:25:27.908+08:00  INFO 37468 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T15:25:32.251+08:00  INFO 37468 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T15:25:33.100+08:00  INFO 37468 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T15:25:35.144+08:00  INFO 37468 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@7f065f9e, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T15:25:35.184+08:00  INFO 37468 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T15:25:35.185+08:00  INFO 37468 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T15:25:35.185+08:00  INFO 37468 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T15:25:35.185+08:00  INFO 37468 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T15:25:35.196+08:00  INFO 37468 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T15:25:35.583+08:00  INFO 37468 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@5bafe1f1)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@7955b4d4, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T15:25:37.789+08:00  INFO 37468 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T15:25:37.796+08:00  INFO 37468 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T15:25:37.928+08:00  INFO 37468 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T15:25:40.718+08:00  INFO 37468 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T15:25:40.834+08:00  INFO 37468 --- [main] vip.xiaonuo.Application                  : Started Application in 19.908 seconds (process running for 21.395)
2025-06-17T15:25:40.839+08:00  INFO 37468 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T15:25:40.839+08:00  INFO 37468 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T15:25:40.841+08:00  INFO 37468 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-17T15:25:40.906+08:00  INFO 37468 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T15:27:22.545+08:00  INFO 37468 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T15:27:22.567+08:00  INFO 37468 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T15:27:22.574+08:00  INFO 37468 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T15:27:22.584+08:00  INFO 37468 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T15:27:22.584+08:00  INFO 37468 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T15:27:22.585+08:00  INFO 37468 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T15:27:22.590+08:00  INFO 37468 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T15:27:22.591+08:00  INFO 37468 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T15:27:22.591+08:00  INFO 37468 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T15:27:43.606+08:00  INFO 37396 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 37396 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T15:27:43.610+08:00  INFO 37396 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T15:27:47.528+08:00  INFO 37396 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T15:27:47.535+08:00  INFO 37396 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T15:27:47.829+08:00  INFO 37396 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 261 ms. Found 0 Redis repository interfaces.
2025-06-17T15:27:48.321+08:00  WARN 37396 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T15:27:48.773+08:00  WARN 37396 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T15:27:49.356+08:00  WARN 37396 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T15:27:49.748+08:00  INFO 37396 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T15:27:49.771+08:00  INFO 37396 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:27:49.771+08:00  INFO 37396 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T15:27:49.893+08:00  INFO 37396 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T15:27:49.894+08:00  INFO 37396 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6210 ms
2025-06-17T15:27:50.283+08:00  INFO 37396 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T15:27:50.568+08:00  INFO 37396 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T15:27:50.569+08:00  INFO 37396 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T15:27:50.570+08:00  INFO 37396 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T15:27:50.571+08:00  INFO 37396 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T15:27:50.908+08:00  INFO 37396 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T15:27:55.254+08:00  INFO 37396 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T15:27:56.131+08:00  INFO 37396 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T15:27:58.131+08:00  INFO 37396 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@7ef34eb3, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T15:27:58.160+08:00  INFO 37396 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T15:27:58.160+08:00  INFO 37396 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T15:27:58.160+08:00  INFO 37396 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T15:27:58.160+08:00  INFO 37396 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T15:27:58.170+08:00  INFO 37396 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T15:27:58.523+08:00  INFO 37396 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@3796ff4)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@7c1e5d14, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T15:28:00.954+08:00  INFO 37396 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T15:28:00.958+08:00  INFO 37396 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T15:28:01.101+08:00  INFO 37396 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T15:28:03.711+08:00  INFO 37396 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T15:28:03.818+08:00  INFO 37396 --- [main] vip.xiaonuo.Application                  : Started Application in 21.694 seconds (process running for 23.114)
2025-06-17T15:28:03.892+08:00  INFO 37396 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T15:28:10.086+08:00  INFO 37396 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T15:28:10.087+08:00  INFO 37396 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T15:28:10.090+08:00  INFO 37396 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-06-17T15:29:04.625+08:00  INFO 37396 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T15:29:04.647+08:00  INFO 37396 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T15:29:04.652+08:00  INFO 37396 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T15:29:04.661+08:00  INFO 37396 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T15:29:04.661+08:00  INFO 37396 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T15:29:04.661+08:00  INFO 37396 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T15:29:04.664+08:00  INFO 37396 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T15:29:04.665+08:00  INFO 37396 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T15:29:04.665+08:00  INFO 37396 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T15:29:25.159+08:00  INFO 32332 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 32332 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T15:29:25.161+08:00  INFO 32332 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T15:29:28.585+08:00  INFO 32332 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T15:29:28.595+08:00  INFO 32332 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T15:29:28.890+08:00  INFO 32332 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 269 ms. Found 0 Redis repository interfaces.
2025-06-17T15:29:29.438+08:00  WARN 32332 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T15:29:29.830+08:00  WARN 32332 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T15:29:30.521+08:00  WARN 32332 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T15:29:31.089+08:00  INFO 32332 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T15:29:31.115+08:00  INFO 32332 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:29:31.116+08:00  INFO 32332 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T15:29:31.345+08:00  INFO 32332 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T15:29:31.346+08:00  INFO 32332 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6123 ms
2025-06-17T15:29:31.930+08:00  INFO 32332 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T15:29:32.336+08:00  INFO 32332 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T15:29:32.338+08:00  INFO 32332 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T15:29:32.340+08:00  INFO 32332 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T15:29:32.341+08:00  INFO 32332 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T15:29:32.870+08:00  INFO 32332 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T15:29:38.322+08:00  INFO 32332 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T15:29:39.173+08:00  INFO 32332 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T15:29:41.154+08:00  INFO 32332 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@4d86f330, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T15:29:41.183+08:00  INFO 32332 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T15:29:41.183+08:00  INFO 32332 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T15:29:41.183+08:00  INFO 32332 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T15:29:41.183+08:00  INFO 32332 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T15:29:41.193+08:00  INFO 32332 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T15:29:41.591+08:00  INFO 32332 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@2ed9e59b)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@25d5327, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T15:29:44.086+08:00  INFO 32332 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T15:29:44.090+08:00  INFO 32332 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T15:29:44.250+08:00  INFO 32332 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T15:29:47.486+08:00  INFO 32332 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T15:29:47.645+08:00  INFO 32332 --- [main] vip.xiaonuo.Application                  : Started Application in 23.727 seconds (process running for 25.08)
2025-06-17T15:29:47.756+08:00  INFO 32332 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T15:29:59.409+08:00  INFO 32332 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T15:29:59.409+08:00  INFO 32332 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T15:29:59.411+08:00  INFO 32332 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-17T15:39:19.161+08:00  WARN 32332 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T15:39:19.170+08:00  WARN 32332 --- [http-nio-10082-exec-10] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T15:39:25.362+08:00  WARN 32332 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T15:39:25.368+08:00  WARN 32332 --- [http-nio-10082-exec-3] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T15:42:21.649+08:00  WARN 32332 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T15:42:21.658+08:00  WARN 32332 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T15:42:26.812+08:00  WARN 32332 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T15:42:26.819+08:00  WARN 32332 --- [http-nio-10082-exec-9] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T16:09:38.721+08:00  INFO 32332 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T16:09:38.752+08:00  INFO 32332 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T16:09:38.760+08:00  INFO 32332 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T16:09:38.770+08:00  INFO 32332 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T16:09:38.770+08:00  INFO 32332 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T16:09:38.770+08:00  INFO 32332 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T16:09:38.774+08:00  INFO 32332 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T16:09:38.774+08:00  INFO 32332 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T16:09:38.774+08:00  INFO 32332 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T16:10:03.278+08:00  INFO 10992 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 10992 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T16:10:03.280+08:00  INFO 10992 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T16:10:07.344+08:00  INFO 10992 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T16:10:07.351+08:00  INFO 10992 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T16:10:07.602+08:00  INFO 10992 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 230 ms. Found 0 Redis repository interfaces.
2025-06-17T16:10:08.160+08:00  WARN 10992 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T16:10:08.580+08:00  WARN 10992 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T16:10:09.310+08:00  WARN 10992 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T16:10:10.066+08:00  INFO 10992 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T16:10:10.089+08:00  INFO 10992 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T16:10:10.090+08:00  INFO 10992 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T16:10:10.226+08:00  INFO 10992 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T16:10:10.227+08:00  INFO 10992 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6882 ms
2025-06-17T16:10:10.799+08:00  INFO 10992 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T16:10:11.216+08:00  INFO 10992 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T16:10:11.218+08:00  INFO 10992 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T16:10:11.220+08:00  INFO 10992 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T16:10:11.220+08:00  INFO 10992 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T16:10:11.657+08:00  INFO 10992 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T16:10:18.052+08:00  INFO 10992 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T16:10:19.291+08:00  INFO 10992 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T16:10:22.177+08:00  INFO 10992 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@6e2e11ee, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T16:10:22.213+08:00  INFO 10992 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T16:10:22.213+08:00  INFO 10992 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T16:10:22.213+08:00  INFO 10992 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T16:10:22.214+08:00  INFO 10992 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T16:10:22.227+08:00  INFO 10992 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T16:10:22.778+08:00  INFO 10992 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@2cf6dd4d)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@293988dc, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T16:10:26.057+08:00  INFO 10992 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T16:10:26.065+08:00  INFO 10992 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T16:10:26.290+08:00  INFO 10992 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T16:10:30.074+08:00  INFO 10992 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T16:10:30.200+08:00  INFO 10992 --- [main] vip.xiaonuo.Application                  : Started Application in 28.622 seconds (process running for 30.204)
2025-06-17T16:10:30.295+08:00  INFO 10992 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T16:10:30.685+08:00  INFO 10992 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T16:10:30.686+08:00  INFO 10992 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T16:10:30.689+08:00  INFO 10992 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-06-17T16:10:30.990+08:00  WARN 10992 --- [http-nio-10082-exec-2] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [143] milliseconds.
2025-06-17T16:30:17.120+08:00  WARN 10992 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T16:30:17.130+08:00  WARN 10992 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T16:30:22.620+08:00  WARN 10992 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T16:30:22.626+08:00  WARN 10992 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T16:33:42.916+08:00  WARN 10992 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T16:33:42.924+08:00  WARN 10992 --- [http-nio-10082-exec-4] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T16:33:48.820+08:00  WARN 10992 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T16:33:48.828+08:00  WARN 10992 --- [http-nio-10082-exec-7] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T16:40:21.034+08:00  WARN 10992 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T16:40:21.042+08:00  WARN 10992 --- [http-nio-10082-exec-5] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T16:40:30.692+08:00  WARN 10992 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.RpcTransService翻译未命中数据:com.fhs.core.trans.vo.TransPojo_-1
2025-06-17T16:40:30.698+08:00  WARN 10992 --- [http-nio-10082-exec-1] c.f.t.service.impl.SimpleTransService    : com.fhs.trans.service.impl.SimpleTransService翻译未命中数据:vip.xiaonuo.sys.modular.position.entity.SysPosition_1543899639134019591
2025-06-17T16:46:31.025+08:00  INFO 10992 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T16:46:31.087+08:00  INFO 10992 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T16:46:31.102+08:00  INFO 10992 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T16:46:31.121+08:00  INFO 10992 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T16:46:31.121+08:00  INFO 10992 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T16:46:31.122+08:00  INFO 10992 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T16:46:31.129+08:00  INFO 10992 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T16:46:31.129+08:00  INFO 10992 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T16:46:31.129+08:00  INFO 10992 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T16:46:40.761+08:00  INFO 32824 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 32824 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T16:46:40.763+08:00  INFO 32824 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T16:46:43.830+08:00  INFO 32824 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T16:46:43.836+08:00  INFO 32824 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T16:46:44.077+08:00  INFO 32824 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 226 ms. Found 0 Redis repository interfaces.
2025-06-17T16:46:44.504+08:00  WARN 32824 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T16:46:44.842+08:00  WARN 32824 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T16:46:45.282+08:00  WARN 32824 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T16:46:45.696+08:00  INFO 32824 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T16:46:45.713+08:00  INFO 32824 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T16:46:45.714+08:00  INFO 32824 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T16:46:45.818+08:00  INFO 32824 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T16:46:45.819+08:00  INFO 32824 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5000 ms
2025-06-17T16:46:46.245+08:00  INFO 32824 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T16:46:46.556+08:00  INFO 32824 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T16:46:46.557+08:00  INFO 32824 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T16:46:46.559+08:00  INFO 32824 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T16:46:46.559+08:00  INFO 32824 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T16:46:46.919+08:00  INFO 32824 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T16:46:51.337+08:00  INFO 32824 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T16:46:52.186+08:00  INFO 32824 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T16:46:54.457+08:00  INFO 32824 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@2257590f, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T16:46:54.485+08:00  INFO 32824 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T16:46:54.485+08:00  INFO 32824 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T16:46:54.485+08:00  INFO 32824 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T16:46:54.485+08:00  INFO 32824 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T16:46:54.495+08:00  INFO 32824 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T16:46:54.851+08:00  INFO 32824 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@3ce2c454)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@56a63c28, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T16:46:57.143+08:00  INFO 32824 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T16:46:57.148+08:00  INFO 32824 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T16:46:57.297+08:00  INFO 32824 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T16:47:00.245+08:00  INFO 32824 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T16:47:00.350+08:00  INFO 32824 --- [main] vip.xiaonuo.Application                  : Started Application in 20.859 seconds (process running for 22.22)
2025-06-17T16:47:00.424+08:00  INFO 32824 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T16:47:01.112+08:00  INFO 32824 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T16:47:01.113+08:00  INFO 32824 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T16:47:01.120+08:00  INFO 32824 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-06-17T16:54:59.493+08:00  INFO 32824 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T16:54:59.525+08:00  INFO 32824 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T16:54:59.534+08:00  INFO 32824 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T16:54:59.561+08:00  INFO 32824 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T16:54:59.562+08:00  INFO 32824 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T16:54:59.562+08:00  INFO 32824 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T16:54:59.567+08:00  INFO 32824 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T16:54:59.568+08:00  INFO 32824 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T16:54:59.568+08:00  INFO 32824 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T16:55:07.184+08:00  INFO 29304 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 29304 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T16:55:07.188+08:00  INFO 29304 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T16:55:10.601+08:00  INFO 29304 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T16:55:10.605+08:00  INFO 29304 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T16:55:10.801+08:00  INFO 29304 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 179 ms. Found 0 Redis repository interfaces.
2025-06-17T16:55:11.230+08:00  WARN 29304 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T16:55:11.594+08:00  WARN 29304 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T16:55:11.951+08:00  WARN 29304 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T16:55:12.332+08:00  INFO 29304 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T16:55:12.351+08:00  INFO 29304 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T16:55:12.351+08:00  INFO 29304 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T16:55:12.471+08:00  INFO 29304 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T16:55:12.471+08:00  INFO 29304 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5196 ms
2025-06-17T16:55:12.852+08:00  INFO 29304 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T16:55:13.188+08:00  INFO 29304 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T16:55:13.189+08:00  INFO 29304 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T16:55:13.190+08:00  INFO 29304 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T16:55:13.190+08:00  INFO 29304 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T16:55:13.526+08:00  INFO 29304 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T16:55:18.098+08:00  INFO 29304 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T16:55:18.915+08:00  INFO 29304 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T16:55:21.068+08:00  INFO 29304 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@2257590f, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T16:55:21.095+08:00  INFO 29304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T16:55:21.095+08:00  INFO 29304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T16:55:21.095+08:00  INFO 29304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T16:55:21.095+08:00  INFO 29304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T16:55:21.104+08:00  INFO 29304 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T16:55:21.496+08:00  INFO 29304 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@3ce2c454)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@56a63c28, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T16:55:23.731+08:00  INFO 29304 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T16:55:23.735+08:00  INFO 29304 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T16:55:23.867+08:00  INFO 29304 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T16:55:26.608+08:00  INFO 29304 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T16:55:26.705+08:00  INFO 29304 --- [main] vip.xiaonuo.Application                  : Started Application in 20.833 seconds (process running for 22.239)
2025-06-17T16:55:26.778+08:00  INFO 29304 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T16:55:26.854+08:00  INFO 29304 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T16:55:26.855+08:00  INFO 29304 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T16:55:26.857+08:00  INFO 29304 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-17T16:55:27.077+08:00  WARN 29304 --- [http-nio-10082-exec-2] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [102] milliseconds.
2025-06-17T17:10:54.013+08:00  INFO 29304 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T17:10:54.045+08:00  INFO 29304 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T17:10:54.055+08:00  INFO 29304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T17:10:54.069+08:00  INFO 29304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T17:10:54.069+08:00  INFO 29304 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T17:10:54.069+08:00  INFO 29304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T17:10:54.073+08:00  INFO 29304 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T17:10:54.073+08:00  INFO 29304 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T17:10:54.074+08:00  INFO 29304 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T17:11:03.771+08:00  INFO 26596 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 26596 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T17:11:03.775+08:00  INFO 26596 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T17:11:07.522+08:00  INFO 26596 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T17:11:07.526+08:00  INFO 26596 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T17:11:07.726+08:00  INFO 26596 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 183 ms. Found 0 Redis repository interfaces.
2025-06-17T17:11:08.199+08:00  WARN 26596 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T17:11:08.568+08:00  WARN 26596 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T17:11:09.028+08:00  WARN 26596 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T17:11:09.630+08:00  INFO 26596 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T17:11:09.654+08:00  INFO 26596 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T17:11:09.655+08:00  INFO 26596 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T17:11:09.767+08:00  INFO 26596 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T17:11:09.768+08:00  INFO 26596 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5921 ms
2025-06-17T17:11:10.223+08:00  INFO 26596 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T17:11:10.596+08:00  INFO 26596 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T17:11:10.598+08:00  INFO 26596 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T17:11:10.599+08:00  INFO 26596 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T17:11:10.599+08:00  INFO 26596 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T17:11:10.964+08:00  INFO 26596 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T17:11:16.784+08:00  INFO 26596 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T17:11:17.777+08:00  INFO 26596 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T17:11:20.220+08:00  INFO 26596 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@6e2e11ee, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T17:11:20.250+08:00  INFO 26596 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T17:11:20.252+08:00  INFO 26596 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T17:11:20.252+08:00  INFO 26596 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T17:11:20.252+08:00  INFO 26596 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T17:11:20.262+08:00  INFO 26596 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T17:11:20.682+08:00  INFO 26596 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@2cf6dd4d)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@293988dc, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T17:11:23.416+08:00  INFO 26596 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T17:11:23.421+08:00  INFO 26596 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T17:11:23.570+08:00  INFO 26596 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T17:11:27.001+08:00  INFO 26596 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T17:11:27.177+08:00  INFO 26596 --- [main] vip.xiaonuo.Application                  : Started Application in 24.743 seconds (process running for 26.242)
2025-06-17T17:11:27.202+08:00  INFO 26596 --- [http-nio-10082-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T17:11:27.202+08:00  INFO 26596 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T17:11:27.208+08:00  INFO 26596 --- [http-nio-10082-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-06-17T17:11:27.293+08:00  INFO 26596 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T17:11:27.484+08:00  WARN 26596 --- [http-nio-10082-exec-2] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [102] milliseconds.
2025-06-17T17:15:15.854+08:00  INFO 26596 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T17:15:15.886+08:00  INFO 26596 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T17:15:15.896+08:00  INFO 26596 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T17:15:15.912+08:00  INFO 26596 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T17:15:15.912+08:00  INFO 26596 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T17:15:15.913+08:00  INFO 26596 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T17:15:15.918+08:00  INFO 26596 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T17:15:15.918+08:00  INFO 26596 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T17:15:15.918+08:00  INFO 26596 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
2025-06-17T17:15:37.624+08:00  INFO 10868 --- [main] vip.xiaonuo.Application                  : Starting Application using Java 17.0.14 with PID 10868 (D:\project-code\waibao\homework\web\服务大师\uej-serve-api\snowy-web-app\target\classes started by 75476 in D:\project-code\waibao\homework\web\服务大师\uej-serve-api)
2025-06-17T17:15:37.626+08:00  INFO 10868 --- [main] vip.xiaonuo.Application                  : The following 1 profile is active: "prod"
2025-06-17T17:15:42.550+08:00  INFO 10868 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17T17:15:42.554+08:00  INFO 10868 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17T17:15:42.787+08:00  INFO 10868 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 214 ms. Found 0 Redis repository interfaces.
2025-06-17T17:15:43.161+08:00  WARN 10868 --- [main] o.s.c.a.ConfigurationClassPostProcessor  : Cannot enhance @Configuration bean definition 'UReportAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static' and/or marking the containing configuration class as 'proxyBeanMethods=false'.
2025-06-17T17:15:43.561+08:00  WARN 10868 --- [main] o.s.b.c.p.PropertySourcesDeducer         : Multiple PropertySourcesPlaceholderConfigurer beans registered [propertySourceLoader, ureport.propertyPlaceholderConfigurer], falling back to Environment
2025-06-17T17:15:43.973+08:00  WARN 10868 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [projectingArgumentResolverBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
2025-06-17T17:15:44.410+08:00  INFO 10868 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 10082 (http)
2025-06-17T17:15:44.434+08:00  INFO 10868 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T17:15:44.434+08:00  INFO 10868 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-06-17T17:15:44.569+08:00  INFO 10868 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T17:15:44.570+08:00  INFO 10868 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6866 ms
2025-06-17T17:15:44.941+08:00  INFO 10868 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1,master} inited
2025-06-17T17:15:45.291+08:00  INFO 10868 --- [main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2,Store1} inited
2025-06-17T17:15:45.292+08:00  INFO 10868 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [Store1] success
2025-06-17T17:15:45.294+08:00  INFO 10868 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource - add a datasource named [master] success
2025-06-17T17:15:45.294+08:00  INFO 10868 --- [main] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-17T17:15:45.669+08:00  INFO 10868 --- [main] c.g.y.a.MybatisPlusJoinAutoConfiguration : MPJSqlInjector init
2025-06-17T17:15:50.597+08:00  INFO 10868 --- [main] f.a.AutowiredAnnotationBeanPostProcessor : Autowired annotation should only be used on methods with parameters: public void vip.xiaonuo.auth.core.config.AuthConfigure.rewriteSaStrategy()
2025-06-17T17:15:51.646+08:00  INFO 10868 --- [main] c.fhs.common.spring.SpringContextUtil    : ------SpringContextUtil setApplicationContext-------
2025-06-17T17:15:54.026+08:00  INFO 10868 --- [main] org.camunda.bpm.engine.cfg               : ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[vip.xiaonuo.flw.core.config.FlwConfigure@********, genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-06-17T17:15:54.068+08:00  INFO 10868 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-06-17T17:15:54.068+08:00  INFO 10868 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-003: Task events will be published as Spring Events.
2025-06-17T17:15:54.068+08:00  INFO 10868 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-005: Execution events will be published as Spring Events.
2025-06-17T17:15:54.068+08:00  INFO 10868 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-06-17T17:15:54.082+08:00  INFO 10868 --- [main] o.c.b.s.b.s.event.EventPublisherPlugin   : EVENTING-008: History eventing is disabled via property.
2025-06-17T17:15:54.494+08:00  INFO 10868 --- [main] org.camunda.feel.FeelEngine              : Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@7e34e466)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@6860c000, clock: SystemClock, configuration: Configuration(false)]
2025-06-17T17:15:57.046+08:00  INFO 10868 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-06-17T17:15:57.053+08:00  INFO 10868 --- [main] org.camunda.bpm.connect                  : CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-06-17T17:15:57.204+08:00  INFO 10868 --- [main] org.camunda.bpm.engine                   : ENGINE-00001 Process Engine default created.
2025-06-17T17:16:01.090+08:00  INFO 10868 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 10082 (http) with context path ''
2025-06-17T17:16:01.282+08:00  INFO 10868 --- [main] vip.xiaonuo.Application                  : Started Application in 25.429 seconds (process running for 27.252)
2025-06-17T17:16:01.367+08:00  INFO 10868 --- [main] vip.xiaonuo.Application                  : 
----------------------------------------------------------
Application is running! Access URLs:
Local:    http://localhost:10082
Doc:      http://localhost:10082/doc.html
----------------------------------------------------------
2025-06-17T17:16:01.375+08:00  INFO 10868 --- [http-nio-10082-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T17:16:01.376+08:00  INFO 10868 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T17:16:01.380+08:00  INFO 10868 --- [http-nio-10082-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-06-17T17:16:01.664+08:00  WARN 10868 --- [http-nio-10082-exec-1] o.a.c.util.SessionIdGeneratorBase        : Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [109] milliseconds.
2025-06-17T17:26:10.423+08:00  INFO 10868 --- [SpringApplicationShutdownHook] org.camunda.bpm.engine                   : ENGINE-00007 Process Engine default closed
2025-06-17T17:26:10.446+08:00  INFO 10868 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource start closing ....
2025-06-17T17:26:10.449+08:00  INFO 10868 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closing ...
2025-06-17T17:26:10.456+08:00  INFO 10868 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-2} closed
2025-06-17T17:26:10.456+08:00  INFO 10868 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [Store1] success,
2025-06-17T17:26:10.456+08:00  INFO 10868 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-06-17T17:26:10.457+08:00  INFO 10868 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-06-17T17:26:10.458+08:00  INFO 10868 --- [SpringApplicationShutdownHook] c.b.d.d.d.DefaultDataSourceDestroyer     : dynamic-datasource close the datasource named [master] success,
2025-06-17T17:26:10.458+08:00  INFO 10868 --- [SpringApplicationShutdownHook] c.b.d.d.DynamicRoutingDataSource         : dynamic-datasource all closed success,bye
