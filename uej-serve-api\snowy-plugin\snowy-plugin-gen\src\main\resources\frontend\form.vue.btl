<template>
    <xn-form-container
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needTableId) { %>
        :title="formData.${configList[i].fieldNameCamelCase} ? '编辑${functionName}' : '增加${functionName}'"
        <% } %>
        <% } %>
        :width="700"
        v-model:open="open"
        :destroy-on-close="true"
        @close="onClose"
    >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="${formLayout}">
        <% if(gridWhether) { %>
            <a-row :gutter="16">
            <% for(var i = 0; i < configList.~size; i++) { %>
            <% if(!configList[i].needTableId && configList[i].whetherAddUpdate && configList[i].fieldNameCamelCase != 'tenantId') { %>
                <a-col :span="12">
                    <a-form-item label="${configList[i].fieldRemark}：" name="${configList[i].fieldNameCamelCase}">
                        <% if(configList[i].effectType == 'input') { %>
                        <a-input v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please enter ${configList[i].fieldRemark}" allow-clear />
                        <% } else if (configList[i].effectType == 'textarea') {%>
                        <a-textarea v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please enter ${configList[i].fieldRemark}" :auto-size="{ minRows: 3, maxRows: 5 }" />
                        <% } else if (configList[i].effectType == 'select') {%>
                        <a-select v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please select${configList[i].fieldRemark}" :options="${configList[i].fieldNameCamelCase}Options" />
                        <% } else if (configList[i].effectType == 'radio') {%>
                        <a-radio-group v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please select${configList[i].fieldRemark}" :options="${configList[i].fieldNameCamelCase}Options" />
                        <% } else if (configList[i].effectType == 'checkbox') {%>
                        <a-checkbox-group v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please select${configList[i].fieldRemark}" :options="${configList[i].fieldNameCamelCase}Options" />
                        <% } else if (configList[i].effectType == 'datepicker') {%>
                        <a-date-picker v-model:value="formData.${configList[i].fieldNameCamelCase}" value-format="YYYY-MM-DD HH:mm:ss" show-time placeholder="Please select${configList[i].fieldRemark}" style="width: 100%" />
                        <% } else if (configList[i].effectType == 'timepicker') {%>
                        <a-time-picker v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please select${configList[i].fieldRemark}" style="width: 100%" />
                        <% } else if (configList[i].effectType == 'inputNumber') {%>
                        <a-input-number v-model:value="formData.${configList[i].fieldNameCamelCase}" :min="1" :max="10000" style="width: 100%" />
                        <% } else if (configList[i].effectType == 'slider') {%>
                        <a-slider v-model:value="formData.${configList[i].fieldNameCamelCase}" :max="1000" style="width: 100%" />
						<% } else if (configList[i].effectType == 'fileUpload') {%>
						<xn-upload v-model:value="formData.${configList[i].fieldNameCamelCase}" />
						<% } else if (configList[i].effectType == 'imageUpload') {%>
						<xn-upload v-model:value="formData.${configList[i].fieldNameCamelCase}" uploadMode="image" />
						<% } else if (configList[i].effectType == 'editor') {%>
						<xn-editor v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please enter ${configList[i].fieldRemark}" />
                        <% } %>
                    </a-form-item>
                </a-col>
            <% } %>
            <% } %>
            </a-row>
        <% } else { %>
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId && configList[i].whetherAddUpdate && configList[i].fieldNameCamelCase != 'tenantId') { %>
            <a-form-item label="${configList[i].fieldRemark}：" name="${configList[i].fieldNameCamelCase}">
                <% if(configList[i].effectType == 'input') { %>
                <a-input v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please enter ${configList[i].fieldRemark}" allow-clear />
                <% } else if (configList[i].effectType == 'textarea') {%>
                <a-textarea v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please enter ${configList[i].fieldRemark}" :auto-size="{ minRows: 3, maxRows: 5 }" />
                <% } else if (configList[i].effectType == 'select') {%>
                <a-select v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please select${configList[i].fieldRemark}" :options="${configList[i].fieldNameCamelCase}Options" />
                <% } else if (configList[i].effectType == 'radio') {%>
                <a-radio-group v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please select${configList[i].fieldRemark}" :options="${configList[i].fieldNameCamelCase}Options" />
                <% } else if (configList[i].effectType == 'checkbox') {%>
                <a-checkbox-group v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please select${configList[i].fieldRemark}" :options="${configList[i].fieldNameCamelCase}Options" />
                <% } else if (configList[i].effectType == 'datepicker') {%>
                <a-date-picker v-model:value="formData.${configList[i].fieldNameCamelCase}" value-format="YYYY-MM-DD HH:mm:ss" show-time placeholder="Please select${configList[i].fieldRemark}" style="width: 100%" />
                <% } else if (configList[i].effectType == 'timepicker') {%>
                <a-time-picker v-model:value="formData.${configList[i].fieldNameCamelCase}" value-format="YYYY-MM-DD HH:mm:ss" show-time placeholder="Please select${configList[i].fieldRemark}" style="width: 100%" />
                <% } else if (configList[i].effectType == 'inputNumber') {%>
                <a-input-number v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please enter ${configList[i].fieldRemark}" :min="1" :max="10000" style="width: 100%" />
                <% } else if (configList[i].effectType == 'slider') {%>
                <a-slider v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="请滑动${configList[i].fieldRemark}" :max="1000" style="width: 100%" />
				<% } else if (configList[i].effectType == 'fileUpload') {%>
				<xn-upload v-model:value="formData.${configList[i].fieldNameCamelCase}" />
				<% } else if (configList[i].effectType == 'imageUpload') {%>
				<xn-upload v-model:value="formData.${configList[i].fieldNameCamelCase}" uploadMode="image" />
				<% } else if (configList[i].effectType == 'editor') {%>
				<xn-editor v-model:value="formData.${configList[i].fieldNameCamelCase}" placeholder="Please enter ${configList[i].fieldRemark}" />
                <% } %>
            </a-form-item>
        <% } %>
        <% } %>
        <% } %>
        </a-form>
        <% if (dfcWhether == 'Y') { %>
        <a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
            <a-row :gutter="16">
                <a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
                    <xn-form-item :fieldConfig="item" :formData="dynamicFormData"/>
                </a-col>
            </a-row>
        </a-form>
        <% } %>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
        </template>
    </xn-form-container>
</template>

<script setup name="${classNameFirstLower}Form">
    <%
    var iptTool = 0;
    for(var i = 0; i < configList.~size; i++) {
        if(!configList[i].needTableId) {
        if(configList[i].effectType == 'select' || configList[i].effectType == 'radio' || configList[i].effectType == 'checkbox') {
            iptTool++;
        }
        }
    }
    %>
    <% if(iptTool > 0) { %>
    import tool from '@/utils/tool'
    <% } %>
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import ${classNameFirstLower}Api from '@/api/${moduleName}/${classNameFirstLower}Api'
    // 抽屉状态
    const open = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    <% for(var i = 0; i < configList.~size; i++) { %>
    <% if(!configList[i].needTableId) { %>
    <% if(configList[i].effectType == 'select' || configList[i].effectType == 'radio' || configList[i].effectType == 'checkbox') { %>
    const ${configList[i].fieldNameCamelCase}Options = ref([])
    <% } %>
    <% } %>
    <% } %>
    <% if (dfcWhether == 'Y') { %>
    // 动态表单
    const dynamicFormRef = ref()
    const dynamicFieldConfigList = ref([])
    const dynamicFormData = ref({})
    <% } %>

    // 打开抽屉
    const onOpen = (record) => {
        open.value = true
        <% if (dfcWhether == 'Y') { %>
        ${classNameFirstLower}Api.${classNameFirstLower}DynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
            dynamicFieldConfigList.value = data
        })
        <% } %>
        if (record) {
            let recordData = cloneDeep(record)
            <% for(var i = 0; i < configList.~size; i++) { %>
            <% if(!configList[i].needTableId && configList[i].whetherAddUpdate && configList[i].effectType == 'checkbox') { %>
            recordData.${configList[i].fieldNameCamelCase} = JSON.parse(recordData.${configList[i].fieldNameCamelCase})
            <% } %>
            <% } %>
            formData.value = Object.assign({}, recordData)
            <% if (dfcWhether == 'Y') { %>
            dynamicFormData.value = JSON.parse(formData.value.extJson) || {}
            <% } %>
        }
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId) { %>
        <% if(configList[i].effectType == 'select' || configList[i].effectType == 'radio' || configList[i].effectType == 'checkbox') { %>
        ${configList[i].fieldNameCamelCase}Options.value = tool.dictList('${configList[i].dictTypeCode}')
        <% } %>
        <% } %>
        <% } %>
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        <% if (dfcWhether == 'Y') { %>
        dynamicFormData.value = {}
        <% } %>
        open.value = false
    }
    // 默认要校验的
    const formRules = {
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId) { %>
        <% if(configList[i].required) { %>
        ${configList[i].fieldNameCamelCase}: [required('Please enter ${configList[i].fieldRemark}')],
        <% } %>
        <% } %>
        <% } %>
    }
    // 验证并提交数据
    const onSubmit = () => {
        <% if (dfcWhether == 'N') { %>
        formRef.value.validate().then(() => {
            submitLoading.value = true
            const formDataParam = cloneDeep(formData.value)
            <% for(var i = 0; i < configList.~size; i++) { %>
            <% if(configList[i].whetherAddUpdate && configList[i].effectType == 'checkbox') { %>
            formDataParam.${configList[i].fieldNameCamelCase} = JSON.stringify(formDataParam.${configList[i].fieldNameCamelCase})
            <% } %>
            <% } %>
            ${classNameFirstLower}Api
                <% for(var i = 0; i < configList.~size; i++) { %>
                <% if(configList[i].needTableId) { %>
                .${classNameFirstLower}SubmitForm(formDataParam, formDataParam.${configList[i].fieldNameCamelCase})
                <% } %>
                <% } %>
                .then(() => {
                    onClose()
                    emit('successful')
                })
                .finally(() => {
                    submitLoading.value = false
                })
        })
        <% } %>
        <% if (dfcWhether == 'Y') { %>
        const promiseList = []
        promiseList.push(
            new Promise((resolve, reject) => {
                formRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        promiseList.push(
            new Promise((resolve, reject) => {
                dynamicFormRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        submitLoading.value = true
        Promise.all(promiseList)
            .then(() => {
            const formDataParam = cloneDeep(formData.value)
            <% for(var i = 0; i < configList.~size; i++) { %>
            <% if(configList[i].whetherAddUpdate && configList[i].effectType == 'checkbox') { %>
            formDataParam.${configList[i].fieldNameCamelCase} = JSON.stringify(formDataParam.${configList[i].fieldNameCamelCase})
            <% } %>
            <% } %>
            formDataParam.extJson = JSON.stringify(dynamicFormData.value)
            ${classNameFirstLower}Api
                <% for(var i = 0; i < configList.~size; i++) { %>
                <% if(configList[i].needTableId) { %>
                .${classNameFirstLower}SubmitForm(formDataParam, formDataParam.${configList[i].fieldNameCamelCase})
                <% } %>
                <% } %>
                .then(() => {
                    onClose()
                    emit('successful')
                })
                .finally(() => {
                    submitLoading.value = false
                })
            })
            .catch(() => {})
        <% } %>
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
