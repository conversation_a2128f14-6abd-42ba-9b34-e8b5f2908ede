
package vip.xiaonuo.flw.core.enums;

import lombok.Getter;
import vip.xiaonuo.common.exception.CommonException;

/**
 * 审批操作类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/11 13:26
 **/
@Getter
public enum NodeApproveOperateTypeEnum {

    /**
     * 发起申请
     */
    START("START"),

    /**
     * 重新提交
     */
    RESTART("RESTART"),

    /**
     * 自动通过
     */
    AUTO_COMPLETE("AUTO_COMPLETE"),

    /**
     * 自动拒绝
     */
    AUTO_REJECT("AUTO_REJECT"),

    /**
     * 终止
     */
    END("END"),

    /**
     * 撤回
     */
    REVOKE("REVOKE"),

    /**
     * 同意
     */
    PASS("PASS"),

    /**
     * 拒绝
     */
    REJECT("REJECT"),

    /**
     * 退回
     */
    BACK("BACK"),

    /**
     * 转办
     */
    TURN("TURN"),

    /**
     * 跳转
     */
    JUMP("JUMP"),

    /**
     * 加签
     */
    ADD_SIGN("ADD_SIGN");

    private final String value;

    NodeApproveOperateTypeEnum(String value) {
        this.value = value;
    }

    public static void validate(String value) {
        boolean flag = START.getValue().equals(value) || RESTART.getValue().equals(value) ||
                AUTO_COMPLETE.getValue().equals(value) || AUTO_REJECT.getValue().equals(value) ||
                END.getValue().equals(value) || REVOKE.getValue().equals(value) ||
                PASS.getValue().equals(value) || REJECT.getValue().equals(value) ||
                BACK.getValue().equals(value) || TURN.getValue().equals(value) ||
                JUMP.getValue().equals(value) || ADD_SIGN.getValue().equals(value);
        if(!flag) {
            throw new CommonException("不支持的审批操作类型：{}", value);
        }
    }
}
