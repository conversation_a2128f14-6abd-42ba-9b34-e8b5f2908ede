
package vip.xiaonuo.flw.modular.process.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.flw.modular.process.param.*;
import vip.xiaonuo.flw.modular.process.result.*;
import vip.xiaonuo.flw.modular.process.service.FlwProcessService;

import javax.validation.Valid;
import java.util.List;

/**
 * 流程监控控制器
 *
 * <AUTHOR>
 * @date 2022/5/11 15:50
 **/
@Tag(name = "流程控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 5)
@RestController
@Validated
public class FlwProcessMonitorController {

    @Resource
    private FlwProcessService flwProcessService;

    /**
     * 获取所有流程分页
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取所有流程分页")
    @SaCheckPermission("/flw/process/monitor/monitorPage")
    @GetMapping("/flw/process/monitor/monitorPage")
    public CommonResult<Page<FlwProcessResult>> monitorPage(FlwProcessMonitorPageParam flwProcessMonitorPageParam) {
        return CommonResult.data(flwProcessService.monitorPage(flwProcessMonitorPageParam));
    }

    /**
     * 删除流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 2)
    @Operation(summary = "删除流程")
    @CommonLog("删除流程")
    @SaCheckPermission("/flw/process/monitor/delete")
    @PostMapping("/flw/process/monitor/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<FlwProcessIdParam> flwProcessIdParamList) {
        flwProcessService.delete(flwProcessIdParamList);
        return CommonResult.ok();
    }

    /**
     * 终止流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 3)
    @Operation(summary = "终止流程")
    @CommonLog("终止流程")
    @SaCheckPermission("/flw/process/monitor/end")
    @PostMapping("/flw/process/monitor/end")
    public CommonResult<String> end(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                               List<FlwProcessIdParam> flwProcessIdParamList) {
        flwProcessService.end(flwProcessIdParamList);
        return CommonResult.ok();
    }

    /**
     * 撤回流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 4)
    @Operation(summary = "撤回流程")
    @CommonLog("撤回流程")
    @SaCheckPermission("/flw/process/monitor/revoke")
    @PostMapping("/flw/process/monitor/revoke")
    public CommonResult<String> revoke(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                            List<FlwProcessIdParam> flwProcessIdParamList) {
        flwProcessService.revoke(flwProcessIdParamList);
        return CommonResult.ok();
    }

    /**
     * 挂起流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 5)
    @Operation(summary = "挂起流程")
    @CommonLog("挂起流程")
    @SaCheckPermission("/flw/process/monitor/suspend")
    @PostMapping("/flw/process/monitor/suspend")
    public CommonResult<String> suspend(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                               List<FlwProcessIdParam> flwProcessIdParamList) {
        flwProcessService.suspend(flwProcessIdParamList);
        return CommonResult.ok();
    }

    /**
     * 激活流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 6)
    @Operation(summary = "激活流程")
    @CommonLog("激活流程")
    @SaCheckPermission("/flw/process/monitor/active")
    @PostMapping("/flw/process/monitor/active")
    public CommonResult<String> active(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                List<FlwProcessIdParam> flwProcessIdParamList) {
        flwProcessService.active(flwProcessIdParamList);
        return CommonResult.ok();
    }

    /**
     * 转办流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:47
     */
    @ApiOperationSupport(order = 7)
    @Operation(summary = "转办流程")
    @CommonLog("转办流程")
    @SaCheckPermission("/flw/process/monitor/turn")
    @PostMapping("/flw/process/monitor/turn")
    public CommonResult<String> turn(@RequestBody @Valid FlwProcessTurnParam flwProcessTurnParam) {
        flwProcessService.turn(flwProcessTurnParam);
        return CommonResult.ok();
    }

    /**
     * 跳转流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:47
     */
    @ApiOperationSupport(order = 7)
    @Operation(summary = "跳转流程")
    @CommonLog("跳转流程")
    @SaCheckPermission("/flw/process/monitor/jump")
    @PostMapping("/flw/process/monitor/jump")
    public CommonResult<String> jump(@RequestBody @Valid FlwProcessJumpParam flwProcessJumpParam) {
        flwProcessService.jump(flwProcessJumpParam);
        return CommonResult.ok();
    }

    /**
     * 复活流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:47
     */
    @ApiOperationSupport(order = 8)
    @Operation(summary = "复活流程")
    @CommonLog("复活流程")
    @SaCheckPermission("/flw/process/monitor/restart")
    @PostMapping("/flw/process/monitor/restart")
    public CommonResult<String> restart(@RequestBody @Valid FlwProcessRestartParam flwProcessRestartParam) {
        flwProcessService.restart(flwProcessRestartParam);
        return CommonResult.ok();
    }

    /**
     * 迁移流程
     *
     * <AUTHOR>
     * @date 2022/4/24 20:47
     */
    @ApiOperationSupport(order = 9)
    @Operation(summary = "迁移流程")
    @CommonLog("迁移流程")
    @SaCheckPermission("/flw/process/monitor/migrate")
    @PostMapping("/flw/process/monitor/migrate")
    public CommonResult<String> migrate(@RequestBody @Valid FlwProcessMigrateParam flwProcessMigrateParam) {
        flwProcessService.migrate(flwProcessMigrateParam);
        return CommonResult.ok();
    }

    /**
     * 获取流程变量分页
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 10)
    @Operation(summary = "获取流程变量分页")
    @SaCheckPermission("/flw/process/monitor/variablePage")
    @GetMapping("/flw/process/monitor/variablePage")
    public CommonResult<Page<FlwProcessVariableResult>> variablePage(@Valid FlwProcessIdParam flwProcessIdParam) {
        return CommonResult.data(flwProcessService.variablePage(flwProcessIdParam));
    }

    /**
     * 批量编辑流程变量
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 11)
    @Operation(summary = "批量编辑流程变量")
    @SaCheckPermission("/flw/process/monitor/variableUpdateBatch")
    @PostMapping("/flw/process/monitor/variableUpdateBatch")
    public CommonResult<String> variableUpdateBatch(@RequestBody @Valid FlwProcessVariableUpdateParam flwProcessVariableUpdateParam) {
        flwProcessService.variableUpdateBatch(flwProcessVariableUpdateParam);
        return CommonResult.ok();
    }

    /**
     * 获取流程详情
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 12)
    @Operation(summary = "获取流程详情")
    @SaCheckPermission("/flw/process/monitor/detail")
    @GetMapping("/flw/process/monitor/detail")
    public CommonResult<FlwProcessDetailResult> detail(@Valid FlwProcessIdParam flwProcessIdParam) {
        return CommonResult.data(flwProcessService.detail(flwProcessIdParam));
    }

    /**
     * 获取可跳转节点列表
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 13)
    @Operation(summary = "获取可跳转节点列表")
    @SaCheckPermission("/flw/process/monitor/getCanJumpNodeInfoList")
    @GetMapping("/flw/process/monitor/getCanJumpNodeInfoList")
    public CommonResult<List<FlwProcessJumpNodeResult>> getCanJumpNodeInfoList(@Valid FlwProcessIdParam flwProcessIdParam) {
        return CommonResult.data(flwProcessService.getCanJumpNodeInfoList(flwProcessIdParam));
    }

    /**
     * 获取可复活到节点列表
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 14)
    @Operation(summary = "获取可复活到节点列表")
    @SaCheckPermission("/flw/process/monitor/getCanRestartNodeInfoList")
    @GetMapping("/flw/process/monitor/getCanRestartNodeInfoList")
    public CommonResult<List<FlwProcessRestartNodeResult>> getCanRestartNodeInfoList(@Valid FlwProcessIdParam flwProcessIdParam) {
        return CommonResult.data(flwProcessService.getCanRestartNodeInfoList(flwProcessIdParam));
    }

    /**
     * 获取可迁移到节点列表
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 15)
    @Operation(summary = "获取可迁移到节点列表")
    @SaCheckPermission("/flw/process/monitor/getCanMigrateNodeInfoList")
    @GetMapping("/flw/process/monitor/getCanMigrateNodeInfoList")
    public CommonResult<List<FlwProcessRestartNodeResult>> getCanMigrateNodeInfoList(@Valid FlwProcessIdParam flwProcessIdParam) {
        return CommonResult.data(flwProcessService.getCanMigrateNodeInfoList(flwProcessIdParam));
    }

    /**
     * 获取组织树选择器
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 16)
    @Operation(summary = "获取组织树选择器")
    @SaCheckPermission("/flw/process/monitor/orgTreeSelector")
    @GetMapping("/flw/process/monitor/orgTreeSelector")
    public CommonResult<List<Tree<String>>> orgTreeSelector() {
        return CommonResult.data(flwProcessService.orgTreeSelector());
    }

    /**
     * 获取用户选择器
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperationSupport(order = 17)
    @Operation(summary = "获取用户选择器")
    @SaCheckPermission("/flw/process/monitor/userSelector")
    @GetMapping("/flw/process/monitor/userSelector")
    public CommonResult<Page<FlwProcessUserResult>> userSelector(FlwProcessSelectorUserParam flwProcessSelectorUserParam) {
        return CommonResult.data(flwProcessService.userSelector(flwProcessSelectorUserParam));
    }
}
