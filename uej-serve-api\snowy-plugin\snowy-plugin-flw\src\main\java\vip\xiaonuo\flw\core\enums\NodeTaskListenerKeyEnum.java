
package vip.xiaonuo.flw.core.enums;

import lombok.Getter;
import vip.xiaonuo.common.exception.CommonException;

/**
 * 任务监听器类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/11 13:26
 **/
@Getter
public enum NodeTaskListenerKeyEnum {

    /**
     * 创建
     */
    CREATE("CREATE"),

    /**
     * 分配
     */
    ASSIGNMENT("ASSIGNMENT"),

    /**
     * 完成
     */
    COMPLETE("COMPLETE"),

    /**
     * 删除
     */
    DELETE("DELETE"),

    /**
     * 更新
     */
    UPDATE("UPDATE"),

    /**
     * 超时
     */
    TIMEOUT("TIMEOUT");

    private final String value;

    NodeTaskListenerKeyEnum(String value) {
        this.value = value;
    }

    public static void validate(String value) {
        boolean flag = CREATE.getValue().equals(value) || ASSIGNMENT.getValue().equals(value)
                || COMPLETE.getValue().equals(value) || DELETE.getValue().equals(value)
                || UPDATE.getValue().equals(value) || TIMEOUT.getValue().equals(value);
        if(!flag) {
            throw new CommonException("不支持的任务监听器类型：{}", value);
        }
    }
}
