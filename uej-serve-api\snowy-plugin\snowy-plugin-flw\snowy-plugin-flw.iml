<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="rebelXmlGenerationInvariantToken" value="PGFwcGxpY2F0aW9uIGdlbmVyYXRlZC1ieT0iaW50ZWxsaWoiPjxpZD5zbm93eS1wbHVnaW4tZmx3PC9pZD48Y2xhc3NwYXRoPjxkaXIgbmFtZT0iRDovbG9jYWwvdWVqLXNlcnZlLWFwaS9zbm93eS1wbHVnaW4vc25vd3ktcGx1Z2luLWZsdy90YXJnZXQvY2xhc3NlcyI+PC9kaXI+PC9jbGFzc3BhdGg+PC9hcHBsaWNhdGlvbj4=" />
          </map>
        </option>
        <option name="version" value="5" />
      </configuration>
    </facet>
  </component>
</module>