package vip.xiaonuo.biz.modular.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ReportParam {

    @Schema(description = "type")
    private String type;

    @Schema(description = "date")
    private String date;

    @Schema(description = "orgId")
    private String orgId;

    private List<StaffEfficiency> efficiency;

    private List<StaffWage> wage;
}
