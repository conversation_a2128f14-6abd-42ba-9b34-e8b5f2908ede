import 'dayjs/locale/zh-cn'

export default {
	common: {
		searchButton: '查询',
		search: '搜索',
		resetButton: '重置',
		addButton: '增加',
		editButton: '编辑',
		removeButton: '删除',
		batchRemoveButton: '批量删除',
		detailButton: '详情',
		searchKey: '关键词',
		imports: '导入',
		more: '更多',
		export: '导出',
		cancel: '取消',
		save: '保存',
		submit: '提交',
		send: '发送',
		copy: '复制',
		clockButton: '打卡',
		columnVisible: '列展示',
		packUp: '收起',
		unfold: '展开',
		refresh: '刷新',
		density: '密度',
		default: '默认',
		middle: '中等',
		small: '紧凑',
		tips: '提示',
		log: '记录',
		clearing: '正在清理中...',
		styleSetting: '整体风格设置',
		layoutSetting: '整体界面布局',
		themeColor: '主题色',
		topBarThemeColor: '顶栏应用主题色',
		topBarThemeColorBar: '顶栏主题色通栏',
		moduleDock: '模块坞',
		crumbs: '面包屑',
		multiLabel: '多标签',
		collapsibleMenu: '折叠菜单',
		fixedWidth: '固定宽度',
		menuExpandsExclusively: '菜单排他展开',
		loginWatermark: '登录用户水印',
		footerCopyrightInformation: '页脚版权信息',
		roundedCornerStyle: '圆角风格',
		formStyle: '表单风格',
		maximize: '最大化',
		restore: '还原',
		clear: '清空',
		selected: '已选择',
		total: '总计',
		addCurrentData: '添加当前数据',
		toBeSelectedList: '待选择列表',
		record: '条',
		pause: '暂停',
		continue: '继续',
		complete: '完成',
		queue: '排活队列',
		progress: '进度',
		legend: '图例',
		close: '关闭',
		closeOtherTabs: '关闭其他标签',
		newWindowOpens: '新窗口打开',
		addPersonnel: '添加人员',
		searchPage: '搜索页面（支持拼音检索）',
		openSearchPanel: '打开搜索面板',
		select: '选择',
		verify: '确认',
		revocation: '撤回',
		Terminate: '终止',
		Activate: '激活',
		hangUp: '挂起',
		variable: '变量',
		turnTo: '转办',
		skip: '跳转',
		Revive: '复活',
		transfer: '迁移',
		DictionaryName: '字典名称',
		sublevel: '子级',
		processVariable: '流程变量',
		StationMessage: '站内信',
		opLog: '操作记录',
		addSchedule: '增加日程',
		date: '时间',
		dateRange: '日期范围',
		shortcut: '快捷方式',
		visLog: '访问记录',
		操作成功: '操作成功',
		autoCopySchedule: '自动复制排班表',
		autoCopyScheduleMessage: '排班表将在每周五凌晨自动复制到下周。',
		404: '对不起，您访问的页面不存在。',
		403: '对不起，您没有访问此页面的权限。',
		服务器异常: '服务器异常',
		accredit: '授权',
		AuthorizedResource: '授权资源',
		AuthorizeMobileResources: '授权移动端资源',
		authorization: '授权权限',
		AuthorizedUser: '授权用户',
		switching: '转交',
		loading: '加载中...',
		noScript: '该浏览器不支持消息功能',
		connectError: '发生错误，消息实时获取已断开与服务器的连接',
		rightClickTip: '右键左侧甘特图目录可进行快速操作',
		batchButton: '批量操作',
		电子签名: '电子签名',
		画笔粗细: '画笔粗细',
		是否裁剪: '是否裁剪',
		预览: '预览',
		清屏: '清屏',
		确定: '确定',
		无任何签字: '无任何签字',
		categoryName: '类目名称',
		categoryCode: '类目编码',
		parentCategory: '上级类目',
		sortOrder: '排序',
		level: '层级',
		parentLevel: '上级',
		childLevel: '子级',
		topLevel: '顶级',
		editProductCategory: '编辑产品类目',
		addProductCategory: '增加产品类目',
		deleteCategoryConfirm: '删除此类目与下级类目吗？',
		pleaseInputCategoryName: '请输入类目名称',
		pleaseInputCategoryCode: '请输入类目编码',
		pleaseSelectParentCategory: '请选择上级类目',
		operation: '操作',
		'Product Category Management Test Page': '产品类目管理测试页面',
		'Test API': '测试API'
	},
	model: {
		user: '用户',
		org: '机构',
		pos: '职位',
		role: '角色',
		bizUser: '人员'
	},
	login: {
		signInTitle: '用户登录',
		forgetPassword: '忘记密码',
		signIn: '登录',
		signInOther: '其他登录方式',
		accountPlaceholder: '请输入账号',
		accountError: '请输入账号',
		PWPlaceholder: '请输入密码',
		PWError: '请输入密码',
		validLaceholder: '请输入验证码',
		validError: '请输入验证码',
		accountPassword: '账号密码',
		phoneSms: '手机号登录',
		phonePlaceholder: '请输入手机号',
		smsCodePlaceholder: '请输入短信验证码',
		getSmsCode: '获取验证码',
		machineValidation: '机器验证',
		sendingSmsMessage: '短信发送中',
		newPwdPlaceholder: '请输入新密码',
		backLogin: '返回登录',
		restPassword: '重置密码',
		emailPlaceholder: '请输入邮箱号',
		emailCodePlaceholder: '请输入邮件验证码',
		restPhoneType: '手机号找回',
		restEmailType: '邮箱找回',
		loginOut: '退出登录',
		popconfirmLoginOut: '确认退出当前用户？',
		popconfirmClearCache: '确认清理所有缓存？',
		quitting: '退出中...',
		loginSuccessful: '登录成功',
		backLogin: '返回首页',
		backGoOne: '返回上一页'
	},
	user: {
		workSpace: '个人中心',
		userStatus: '用户状态',
		resetPassword: '重置密码',
		role: '角色',
		batchExportButton: '批量导出',
		grantRole: '授权角色',
		grantResource: '授权资源',
		grantPermission: '授权权限',
		exportUserInfo: '导出信息',
		placeholderNameAndSearchKey: '请输入姓名或关键词',
		placeholderUserStatus: '请选择状态',
		popconfirmDeleteUser: '确定要删除吗？',
		popconfirmResatUserPwd: '确定要重置吗？',
		popconfirmDeleteOrg: '删除此组织与下级组织吗？',
		popconfirmDeleteJob: '确定删除此职位？',
		popconfirmDeleteRole: '确认删除此角色？',
		clearCache: '清理缓存',
		batchProcessButton: '批量处理此信息？',
		batchRemoveButton: '删除此信息？',
		userSelection: '用户选择',
		popconfirmDeleteProcess: '确认删除此流程吗？',
		transferMessage: '迁移到节点后，将自动迁移到本模型最新版本',
		processVariableMessage: '变量功能仅支持在审批中的状态下进行，非专业人员禁止修改！',
	},
	menu: {
		流程计时: '流程计时',
		排班表: '排班表',
		任务信息: '任务信息',
		服务或产品: '服务或产品',
		服务或产品组合: '服务或产品组',
		礼品卡信息: '礼品卡信息',
		代金券信息: '代金券信息',
		外部会员: '外部会员',
		工时: '工时',
		任务子表: '任务子表',
		使用记录管理: '使用记录管理',
		套餐明细管理: '套餐明细管理',
		任务数据统计管理: '任务数据统计管理',
		排班管理: '排班管理',
		排班表当日属性管理: '排班表当日属性管理',
		机构管理: '机构管理',
		人员管理: '人员管理',
		岗位管理: '岗位管理',
		发起申请: '发起申请',
		已发申请: '已发申请',
		待办事宜: '待办事宜',
		已办事宜: '已办事宜',
		抄送事宜: '抄送事宜',
		流程监控: '流程监控',
		业务字典: '业务字典',
		系统首页: '系统首页',
		组织架构: '组织架构',
		组织管理: '组织管理',
		用户管理: '用户管理',
		职位管理: '职位管理',
		角色管理: '角色管理',
		模块管理: '模块管理',
		菜单管理: '菜单管理',
		基础工具: '基础工具',
		文件管理: '文件管理',
		邮件推送: '邮件推送',
		短信发送: '短信发送',
		站内信息: '站内信息',
		系统运维: '系统运维',
		三方用户: '三方用户',
		数据字典: '数据字典',
		系统配置: '系统配置',
		会话管理: '会话管理',
		系统监控: '系统监控',
		工作流程: '工作流程',
		日志审计: '日志审计',
		访问日志: '访问日志',
		操作日志: '操作日志',
		流水序号: '流水序号',
		打印模板: '打印模板',
		流程设计: '流程设计',
		SAAS管理: 'SAAS管理',
		多数据源: '多数据源',
		动态租户: '动态租户',
		公司架构: '公司架构',
		在线办公: '在线办公',
		支付体验: '支付体验',
		支付示例: '支付示例',
		订单管理: '订单管理',
		在线开发: '在线开发',
		代码生成: '代码生成',
		数据建模: '数据建模',
		动态字段: '动态字段',
		开发示例: '开发示例',
		常见示例: '常见示例',
		图标选择: '图标选择',
		地图取点: '地图取点',
		数据导入: '数据导入',
		二维码生成: '二维码生成',
		条码生成: '条码生成',
		页面打印: '页面打印',
		模板导出: '模板导出',
		文本编辑: '文本编辑',
		跳转路由: '跳转路由',
		高德地图: '高德地图',
		百度地图: '百度地图',
		统计图表: '统计图表',
		ECK线图: 'ECK线图',
		EC仪表图: 'EC仪表图',
		EC散点图: 'EC散点图',
		EC柱状图: 'EC柱状图',
		EC树形图: 'EC树形图',
		EC漏斗图: 'EC漏斗图',
		EC线形图: 'EC线形图',
		EC饼状图: 'EC饼状图',
		G2进度图: 'G2进度图',
		G2子弹图: 'G2子弹图',
		G2散点图: 'G2散点图',
		G2柱状图: 'G2柱状图',
		G2漏斗图: 'G2漏斗图',
		G2折线图: 'G2折线图',
		G2词云图: 'G2词云图',
		G2面积图: 'G2面积图',
		G2饼状图: 'G2饼状图',
		G2条形图: 'G2条形图',
		移动端管理: '移动端管理',
		模块管理: '模块管理',
		菜单管理: '菜单管理',
		报表插件: '报表插件',
		报表管理: '报表管理',
		系统: '系统',
		权限管控: '权限管控',
		连接监控: '连接监控',
		接口文档: '接口文档',
		任务调度: '任务调度',
		运营: '运营',
		业务: '业务',
		系统: '系统',
		个人中心: '个人中心',
		排班表编辑: '排班表编辑',
		更多: '更多',
		找回密码: '找回密码',
		登录: '登录',
		礼品卡记录管理: '礼品卡记录',
		业务配置管理: '业务配置',
		设置: '设置',
		产品: '产品',
		考勤记录管理: '考勤记录',
		打卡: '打卡',
		保险公司管理: '保险公司',
		电子签名: '电子签名',
		报表: '报表',
		运营: '运营',
		业务: '业务',
		设置: '设置',
		系统: '系统',
		管理: '管理',
		配置: '配置',
		排班编辑:'排班编辑',
		日扎帐表:'日扎帐表',
		每日报表:'每日报表',
		项目结款记录表管理:'结款记录表',
		产品类目维护: '产品类目维护',
		邮箱配置管理: '邮箱配置管理'
	},
	table: {
		头像: '头像',
		账号: '账号',
		姓名: '姓名',
		性别: '性别',
		手机: '手机',
		机构: '机构',
		职位: '职位',
		状态: '状态',
		action: '操作',
		操作: '操作',
		机构名称: '机构名称',
		分类: '分类',
		排序: '排序',
		岗位名称: '岗位名称',
		流程名称: '流程名称',
		创建时间: '创建时间',
		标题: '标题',
		流水号: '流水号',
		定义名称: '定义名称',
		定义版本: '定义版本',
		发起组织: '发起组织',
		发起职位: '发起职位',
		耗时: '耗时',
		当前节点: '当前节点',
		状态: '状态',
		发起时间: '发起时间',
		结束时间: '结束时间',
		摘要: '摘要',
		办理人: '办理人',
		发起人: '发起人',
		发起人组织: '发起人组织',
		发起人职位: '发起人职位',
		办理节点: '办理节点',
		办理时间: '办理时间',
		待阅流程: '待阅流程',
		已阅流程: '已阅流程',
		当前办理人: '当前办理人',
		字典名称: '字典名称',
		字典值: '字典值',
		TherapistQueue: '排活队列',
		Finish: '下活',
		Free: '空闲',
		Next: '下一个',
		组织名称: '组织名称',
		职位名称: '职位名称',
		角色名称: '角色名称',
		颜色: '颜色',
		签名: '签名'
	},
	flw: {
		newProcessTask: '发起流程',
		myDraft: '我的草稿'
	},
	searchFormData: {
		process: '流程名称',
		processHolder: '请输入流程名称',
		pleaseEnter: '请输入'
	},
	setting: {
		uej: '服务大师',
		modal: '对话框',
		drawer: '抽屉',
		暗色主题风格: '暗色主题风格',
		亮色主题风格: '亮色主题风格',
		暗黑模式: '暗黑模式',
		经典: '经典',
		双排菜单: '双排菜单',
		顶部菜单: '顶部菜单',
		alert: '以上配置可实时预览，开发者可在 config/index.js 中配置默认值，不建议在生产环境下开放布局设置'
	},
	dict: {
		new: '新建',
		stop: '暂停',
		progress: '已开始',
		finish: '已结束',
		paid: '已支付',
		cancelled: '已取消',
		remind: '提醒'
	},
	'营收趋势':'营收趋势',
	'周':'周',
	'月':'月',
	'年':'年',
	'季度':'季度',
	'今日排班':'今日排班',
	'今年排班':'今年排班',
	'全部排班':'全部排班',
	'今日预约':'今日预约',
	'今年预约':'今年预约',
	'预约总数':'预约总数',
	'今日营业额':'今日营业额',
	'今年营业额':'今年营业额',
	'全部营业额':'全部营业额',
	'今日礼品卡金额':'今日礼品卡金额',
	'今年礼品卡金额':'今年礼品卡金额',
	'全部礼品卡金额':'全部礼品卡金额'
}
