package vip.xiaonuo.biz.modular.dailybalance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.dailybalance.entity.BizDailyBalance;
import vip.xiaonuo.biz.modular.dailybalance.mapper.BizDailyBalanceMapper;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceAddParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceEditParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceIdParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalancePageParam;
import vip.xiaonuo.biz.modular.dailybalance.service.BizDailyBalanceService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 项目结款记录表Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/12/02 13:50
 **/
@Service
public class BizDailyBalanceServiceImpl extends ServiceImpl<BizDailyBalanceMapper, BizDailyBalance> implements BizDailyBalanceService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizDailyBalance> page(BizDailyBalancePageParam bizDailyBalancePageParam) {
        QueryWrapper<BizDailyBalance> queryWrapper = new QueryWrapper<BizDailyBalance>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizDailyBalancePageParam.getRemark())) {
            queryWrapper.lambda().like(BizDailyBalance::getRemark, bizDailyBalancePageParam.getRemark());
        }
        if(ObjectUtil.isNotEmpty(bizDailyBalancePageParam.getOrgName())) {
            queryWrapper.lambda().like(BizDailyBalance::getOrgName, bizDailyBalancePageParam.getOrgName());
        }
        if(ObjectUtil.isAllNotEmpty(bizDailyBalancePageParam.getSortField(), bizDailyBalancePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizDailyBalancePageParam.getSortOrder());
            queryWrapper.orderBy(true, bizDailyBalancePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizDailyBalancePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BizDailyBalance::getCreateTime);
        }
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizDailyBalance::getOrgId, loginUserDataScope);
        } else {
            // 无权限时只能查看自己创建的日常余额记录
            queryWrapper.lambda().eq(BizDailyBalance::getCreateUser, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizDailyBalanceAddParam bizDailyBalanceAddParam) {
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizDailyBalanceAddParam.getOrgId())) {
                throw new CommonException("您没有权限在该机构下添加日常余额记录，机构id：{}", bizDailyBalanceAddParam.getOrgId());
            }
        } else {
            throw new CommonException("您没有权限在该机构下添加日常余额记录，机构id：{}", bizDailyBalanceAddParam.getOrgId());
        }
        
        BizDailyBalance bizDailyBalance = BeanUtil.toBean(bizDailyBalanceAddParam, BizDailyBalance.class);
        this.save(bizDailyBalance);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizDailyBalanceEditParam bizDailyBalanceEditParam) {
        BizDailyBalance bizDailyBalance = this.queryEntity(bizDailyBalanceEditParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizDailyBalance.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的日常余额记录，机构id：{}", bizDailyBalance.getOrgId());
            }
        } else {
            if(!bizDailyBalance.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该日常余额记录，记录项目：{}", bizDailyBalance.getItem());
            }
        }
        
        BeanUtil.copyProperties(bizDailyBalanceEditParam, bizDailyBalance);
        this.updateById(bizDailyBalance);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizDailyBalanceIdParam> bizDailyBalanceIdParamList) {
        List<String> dailyBalanceIdList = CollStreamUtil.toList(bizDailyBalanceIdParamList, BizDailyBalanceIdParam::getId);
        if(ObjectUtil.isNotEmpty(dailyBalanceIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            List<BizDailyBalance> dailyBalanceList = this.listByIds(dailyBalanceIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 检查机构权限
                for(BizDailyBalance dailyBalance : dailyBalanceList) {
                    if(!loginUserDataScope.contains(dailyBalance.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的日常余额记录，机构id：{}", dailyBalance.getOrgId());
                    }
                }
            } else {
                // 检查创建者权限
                for(BizDailyBalance dailyBalance : dailyBalanceList) {
                    if(!dailyBalance.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该日常余额记录，记录项目：{}", dailyBalance.getItem());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(dailyBalanceIdList);
    }

    @Override
    public BizDailyBalance detail(BizDailyBalanceIdParam bizDailyBalanceIdParam) {
        BizDailyBalance bizDailyBalance = this.queryEntity(bizDailyBalanceIdParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizDailyBalance.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的日常余额记录，机构id：{}", bizDailyBalance.getOrgId());
            }
        } else {
            if(!bizDailyBalance.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该日常余额记录，记录项目：{}", bizDailyBalance.getItem());
            }
        }
        
        return bizDailyBalance;
    }

    @Override
    public BizDailyBalance queryEntity(String id) {
        BizDailyBalance bizDailyBalance = this.getById(id);
        if(ObjectUtil.isEmpty(bizDailyBalance)) {
            throw new CommonException("项目结款记录表不存在，id值为：{}", id);
        }
        return bizDailyBalance;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizDailyBalanceServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizDailyBalance.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }

    @Override
    public Boolean checkTodaySubmitted(String orgId, String date) {
        QueryWrapper<BizDailyBalance> queryWrapper = new QueryWrapper<BizDailyBalance>().checkSqlInjection();
        queryWrapper.lambda().eq(BizDailyBalance::getOrgId, orgId);
        
        // 根据创建时间过滤当天的记录
        String targetDate = ObjectUtil.isEmpty(date) ? DateUtil.format(DateUtil.date(), "yyyy-MM-dd") : date;
        queryWrapper.apply("DATE(create_time) = {0}", targetDate);
        
        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unlockDailyBalance(String orgId, String date) {
        QueryWrapper<BizDailyBalance> queryWrapper = new QueryWrapper<BizDailyBalance>().checkSqlInjection();
        queryWrapper.lambda().eq(BizDailyBalance::getOrgId, orgId);
        
        // 根据创建时间过滤当天的记录
        String targetDate = ObjectUtil.isEmpty(date) ? DateUtil.format(DateUtil.date(), "yyyy-MM-dd") : date;
        queryWrapper.apply("DATE(create_time) = {0}", targetDate);
        
        // 删除当天的记录，允许重新提交
        this.remove(queryWrapper);
    }
}
