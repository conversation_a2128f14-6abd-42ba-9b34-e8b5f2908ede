
package vip.xiaonuo.flw.core.listener;

import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.springframework.stereotype.Component;
import vip.xiaonuo.common.exception.CommonException;

/**
 * 测试任务监听器
 *
 * <AUTHOR>
 * @date 2023/5/22 21:41
 */
@Slf4j
@Component
public class FlwTestTaskListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        try {
            log.info(">>> 任务监听器：{}事件触发，流程id为：{}，当前活动节点为：{}",
                    delegateTask.getEventName(), delegateTask.getProcessInstanceId(), delegateTask.getName());
        } catch (Exception e) {
            throw new CommonException("任务监听器{}出现异常：{}", FlwTestTaskListener.class.getName(), e.getMessage());
        }
    }
}
