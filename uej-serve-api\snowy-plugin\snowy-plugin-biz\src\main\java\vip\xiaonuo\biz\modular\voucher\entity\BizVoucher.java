
package vip.xiaonuo.biz.modular.voucher.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 代金券信息实体
 *
 * <AUTHOR>
 * @date  2024/06/12 17:17
 **/
@Getter
@Setter
@TableName("biz_voucher")
public class BizVoucher {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    private String pid;

    private String orgId;

    /** Voucher Number */
    @Schema(description = "Voucher Number")
    private String voucherNumber;

    /** Create Date */
    @Schema(description = "Create Date")
    private String createDate;

    /** Used Date */
    @Schema(description = "Used Date")
    private String usedDate;

    /** Value */
    @Schema(description = "Value")
    private String value;

    private String validityDate;

    private String threshold;

    private String status;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String extJson;

    private String taskId;

    @TableField(exist = false)
    private int num;
}
