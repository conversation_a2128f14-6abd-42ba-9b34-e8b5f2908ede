package vip.xiaonuo.biz.modular.taskitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;
import vip.xiaonuo.biz.modular.taskitem.mapper.BizTaskItemMapper;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemAddParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemEditParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemIdParam;
import vip.xiaonuo.biz.modular.taskitem.param.BizTaskItemPageParam;
import vip.xiaonuo.biz.modular.taskitem.service.BizTaskItemService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 任务子表Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 17:34
 **/
@Service
public class BizTaskItemServiceImpl extends ServiceImpl<BizTaskItemMapper, BizTaskItem> implements BizTaskItemService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizTaskItem> page(BizTaskItemPageParam bizTaskItemPageParam) {
        QueryWrapper<BizTaskItem> queryWrapper = new QueryWrapper<BizTaskItem>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizTaskItemPageParam.getProductName())) {
            queryWrapper.lambda().like(BizTaskItem::getProductName, bizTaskItemPageParam.getProductName());
        }
        if(ObjectUtil.isAllNotEmpty(bizTaskItemPageParam.getSortField(), bizTaskItemPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizTaskItemPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizTaskItemPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizTaskItemPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BizTaskItem::getCreateTime);
        }
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizTaskItem::getOrgId, loginUserDataScope);
        } else {
            // 无权限时只能查看自己创建的任务项目
            queryWrapper.lambda().eq(BizTaskItem::getCreateUser, StpLoginUserUtil.getLoginUser().getId());
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizTaskItemAddParam bizTaskItemAddParam) {
        // 任务项目添加不需要特殊的权限校验，因为任何用户都可以添加任务项目
        BizTaskItem bizTaskItem = BeanUtil.toBean(bizTaskItemAddParam, BizTaskItem.class);
        this.save(bizTaskItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizTaskItemEditParam bizTaskItemEditParam) {
        BizTaskItem bizTaskItem = this.queryEntity(bizTaskItemEditParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizTaskItem.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的任务项目，机构id：{}", bizTaskItem.getOrgId());
            }
        } else {
            if(!bizTaskItem.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该任务项目，项目名称：{}", bizTaskItem.getProductName());
            }
        }
        
        BeanUtil.copyProperties(bizTaskItemEditParam, bizTaskItem);
        this.updateById(bizTaskItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizTaskItemIdParam> bizTaskItemIdParamList) {
        List<String> taskItemIdList = CollStreamUtil.toList(bizTaskItemIdParamList, BizTaskItemIdParam::getId);
        if(ObjectUtil.isNotEmpty(taskItemIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            List<BizTaskItem> taskItemList = this.listByIds(taskItemIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 检查机构权限
                for(BizTaskItem taskItem : taskItemList) {
                    if(!loginUserDataScope.contains(taskItem.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的任务项目，机构id：{}", taskItem.getOrgId());
                    }
                }
            } else {
                // 检查创建者权限
                for(BizTaskItem taskItem : taskItemList) {
                    if(!taskItem.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该任务项目，项目名称：{}", taskItem.getProductName());
                    }
                }
            }
        }
        
        // 执行删除
        this.removeByIds(taskItemIdList);
    }

    @Override
    public BizTaskItem detail(BizTaskItemIdParam bizTaskItemIdParam) {
        BizTaskItem bizTaskItem = this.queryEntity(bizTaskItemIdParam.getId());
        
        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizTaskItem.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的任务项目，机构id：{}", bizTaskItem.getOrgId());
            }
        } else {
            if(!bizTaskItem.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该任务项目，项目名称：{}", bizTaskItem.getProductName());
            }
        }
        
        return bizTaskItem;
    }

    @Override
    public BizTaskItem queryEntity(String id) {
        BizTaskItem bizTaskItem = this.getById(id);
        if(ObjectUtil.isEmpty(bizTaskItem)) {
            throw new CommonException("任务子表不存在，id值为：{}", id);
        }
        return bizTaskItem;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizTaskItemServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizTaskItem.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}
