<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :xs="24" class="mb-4 qrcode-logo"> </a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Staff Name" name="staffName">
						<a-input v-model:value="searchFormState.staffName" placeholder="Please enter Staff Name" />
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-form-item label="Org Name" name="orgName">
						<a-input v-model:value="searchFormState.orgName" placeholder="Please enter Org Name" />
					</a-form-item>
				</a-col>
				<a-col :xs="24" :sm="12" :md="8" :xl="6">
					<a-button type="primary" @click="tableRef.refresh(true)">{{ $t('common.searchButton') }}</a-button>
					<a-button style="margin: 0 8px" @click="reset">{{ $t('common.resetButton') }}</a-button>
				</a-col>
			</a-row>
		</a-form>
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
			:scroll="{ x: 'max-content' }"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('bizAttendanceAdd')">
						<template #icon><plus-outlined /></template>
						{{ $t('common.addButton') }}
					</a-button>
					<xn-batch-delete
						v-if="hasPerm('bizAttendanceBatchDelete')"
						:selectedRowKeys="selectedRowKeys"
						@batchDelete="deleteBatchBizAttendance"
						:buttonName="$t('common.batchRemoveButton')"
					/>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a @click="formRef.onOpen(record)" v-if="hasPerm('bizAttendanceEdit')">{{ $t('common.editButton') }}</a>
						<a-divider type="vertical" v-if="hasPerm(['bizAttendanceEdit', 'bizAttendanceDelete'], 'and')" />
						<a-popconfirm :title="$t('user.popconfirmDeleteUser')" @confirm="deleteBizAttendance(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('bizAttendanceDelete')">{{
								$t('common.removeButton')
							}}</a-button>
						</a-popconfirm>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh(true)" />
</template>

<script setup name="attendance">
	import VueQrcode from '@chenfengyuan/vue-qrcode'
	import { h, render } from 'vue'
	import dayjs from 'dayjs'
	import { cloneDeep } from 'lodash-es'
	import Form from './form.vue'
	import bizAttendanceApi from '@/api/biz/bizAttendanceApi'
	const searchFormState = ref({})
	const searchFormRef = ref()
	const tableRef = ref()
	const timer = ref()
	const text = ref(Math.round(Math.random() * 100))
	const formRef = ref()
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	const columns = [
		{
			title: 'Staff ID',
			dataIndex: 'staffId'
		},
		{
			title: 'Staff Name',
			dataIndex: 'staffName'
		},
		{
			title: 'Org ID',
			dataIndex: 'orgId'
		},
		{
			title: 'Org Name',
			dataIndex: 'orgName'
		},
		{
			title: 'Clock_In Time',
			dataIndex: 'workTime'
		},
		{
			title: 'Clock_Out Time',
			dataIndex: 'closingTime'
		}
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['bizAttendanceEdit', 'bizAttendanceDelete'])) {
		columns.push({
			title: 'action',
			dataIndex: 'action',
			align: 'center',
			width: '150px'
		})
	}
	const selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		// columns数字类型字段加入 needTotal: true 可以勾选自动算账
		alert: {
			show: true,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		const searchFormParam = cloneDeep(searchFormState.value)
		return bizAttendanceApi.bizAttendancePage(Object.assign(parameter, searchFormParam)).then((data) => {
			return data
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 删除
	const deleteBizAttendance = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		bizAttendanceApi.bizAttendanceDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchBizAttendance = (params) => {
		bizAttendanceApi.bizAttendanceDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}

	onMounted(() => {
		updateQRCode()
	})

	onActivated(() => {
		if (!timer.value) {
			timer.value = setInterval(updateQRCode, 20 * 1000)
		}
	})

	function updateQRCode() {
		text.value = Math.round(Math.random() * 100)
		const rq = document.querySelector('.qrcode-logo')
		if (rq) {
			render(h(VueQrcode, { value: window.location.origin + '/biz/clockCheck?code=' + text.value +'&date='+dayjs().format('YYYYMMDD') }), rq)
		}
	}

	onUnmounted(() => {
		timer.value && clearInterval(timer.value)
		timer.value = null
	})

</script>
