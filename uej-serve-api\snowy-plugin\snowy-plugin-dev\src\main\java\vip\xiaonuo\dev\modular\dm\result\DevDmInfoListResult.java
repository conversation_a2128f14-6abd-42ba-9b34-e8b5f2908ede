package vip.xiaonuo.dev.modular.dm.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DevDmInfoListResult {

    /** id */
    @Schema(description = "id")
    private String id;

    /** 名称 */
    @Schema(description = "名称")
    private String poolName;

    /** 驱动名称 */
    @Schema(description = "驱动名称")
    private String driverName;
}
