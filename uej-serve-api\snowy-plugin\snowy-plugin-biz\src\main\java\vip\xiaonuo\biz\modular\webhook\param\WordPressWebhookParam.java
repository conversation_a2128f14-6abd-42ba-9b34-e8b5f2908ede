package vip.xiaonuo.biz.modular.webhook.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * WordPress Webhook参数
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Getter
@Setter
public class WordPressWebhookParam {

    /** 操作类型：create, update, cancel */
    @Schema(description = "操作类型")
    private String action;

    /** WordPress预约ID */
    @Schema(description = "WordPress预约ID")
    private String appointmentId;

    /** 已有任务ID（修改时使用） */
    @Schema(description = "已有任务ID")
    private String taskId;

    /** 预约状态 */
    @Schema(description = "预约状态")
    private String status;

    /** 客户信息 */
    @Schema(description = "客户信息")
    private CustomerInfo customer;

    /** 员工信息 */
    @Schema(description = "员工信息")
    private StaffInfo staff;

    /** 服务信息 */
    @Schema(description = "服务信息")
    private List<ServiceInfo> services;

    /** 预约时间信息 */
    @Schema(description = "预约时间信息")
    private AppointmentTime appointmentTime;

    /** 付款信息 */
    @Schema(description = "付款信息")
    private PaymentInfo payment;

    /** 备注信息 */
    @Schema(description = "备注信息")
    private String notes;

    /** webhook签名（用于验证） */
    @Schema(description = "webhook签名")
    private String signature;

    /** 时间戳 */
    @Schema(description = "时间戳")
    private Long timestamp;

    /** 扩展数据 */
    @Schema(description = "扩展数据")
    private Map<String, Object> metadata;

    /**
     * 客户信息
     */
    @Getter
    @Setter
    public static class CustomerInfo {
        /** 客户ID */
        private String id;
        /** 客户姓名 */
        private String name;
        /** 客户邮箱 */
        private String email;
        /** 客户电话 */
        private String phone;
        /** 客户地址 */
        private String address;
    }

    /**
     * 员工信息
     */
    @Getter
    @Setter
    public static class StaffInfo {
        /** 员工ID */
        private String id;
        /** 员工姓名 */
        private String name;
        /** 员工邮箱 */
        private String email;
        /** 部门ID */
        private String departmentId;
        /** 部门名称 */
        private String departmentName;
    }

    /**
     * 服务信息
     */
    @Getter
    @Setter
    public static class ServiceInfo {
        /** 服务ID */
        private String id;
        /** 服务名称 */
        private String name;
        /** 服务类型 */
        private String type;
        /** 服务价格 */
        private Double price;
        /** 成本价格 */
        private Double costPrice;
        /** 预计时长（小时） */
        private Double duration;
        /** 服务数量 */
        private Integer quantity;
    }

    /**
     * 预约时间信息
     */
    @Getter
    @Setter
    public static class AppointmentTime {
        /** 开始时间 */
        private String startTime;
        /** 结束时间 */
        private String endTime;
        /** 时区 */
        private String timezone;
        /** 预计时长（小时） */
        private Double duration;
    }

    /**
     * 付款信息
     */
    @Getter
    @Setter
    public static class PaymentInfo {
        /** 总金额 */
        private Double totalAmount;
        /** 现金支付 */
        private Double cash;
        /** 刷卡支付 */
        private Double card;
        /** 手续费 */
        private Double surchargeFee;
        /** 礼品卡支付 */
        private Double giftCard;
        /** 优惠券折扣 */
        private Double voucher;
        /** 保险金额 */
        private Double insurance;
        /** 小费 */
        private Double tip;
        /** 礼品卡列表 */
        private List<GiftCardInfo> giftCards;
        /** 优惠券号码 */
        private String voucherCode;
        /** 付款状态 */
        private String paymentStatus;
    }

    /**
     * 礼品卡信息
     */
    @Getter
    @Setter
    public static class GiftCardInfo {
        /** 卡号 */
        private String cardNo;
        /** 卡密 */
        private String pin;
        /** 使用金额 */
        private Double amount;
        /** 余额 */
        private Double balance;
        /** 可用余额 */
        private Double availableBalance;
        /** 是否已验证 */
        private Boolean verified;
    }
} 