<template>
	<xn-form-container
		:title="formData.id ? 'edit job' : 'add job'"
		:width="550"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-form-item label="Affiliated organization：" name="orgId">
				<a-tree-select
					v-model:value="formData.orgId"
					style="width: 100%"
					:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
					placeholder="Please select organization"
					allow-clear
					tree-default-expand-all
					:tree-data="treeData"
					:field-names="{
						children: 'children',
						label: 'name',
						value: 'id'
					}"
					selectable="false"
					tree-line
				></a-tree-select>
			</a-form-item>
			<a-form-item label="Job title：" name="name">
				<a-input v-model:value="formData.name" placeholder="Please enter the job title" allow-clear />
			</a-form-item>
			<a-form-item label="Job classification：" name="category">
				<a-select
					v-model:value="formData.category"
					:options="positionCategoryOptions"
					style="width: 100%"
					placeholder="Please select a job category"
				>
				</a-select>
			</a-form-item>
			<a-form-item label="Color：" name="color">
				<color-picker v-model:value="formData.color" />
			</a-form-item>
			<a-form-item label="sort：" name="sortCode">
				<a-input-number style="width: 100%" v-model:value="formData.sortCode" :max="100" />
			</a-form-item>
		</a-form>
		<a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
			<xn-form-item
				v-for="(item, index) in dynamicFieldConfigList"
				:key="index"
				:index="index"
				:fieldConfig="item"
				:formData="dynamicFormData"
			/>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
			<a-button type="primary" :loading="submitLoading" @click="onSubmit">{{ $t('common.save') }}</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="bizPositionForm">
	import { required } from '@/utils/formRules'
	import bizPositionApi from '@/api/biz/bizPositionApi'
	import XnFormItem from '@/components/XnFormItem/index.vue'
	import tool from '@/utils/tool'

	// 定义emit事件
	const emit = defineEmits({ successful: null })
	// 默认是关闭状态
	const visible = ref(false)
	const formRef = ref()
	// 表单数据，也就是默认给一些数据
	const formData = ref({})
	// 定义机构元素
	const treeData = ref([])
	const submitLoading = ref(false)
	// 动态表单
	const dynamicFormRef = ref()
	const dynamicFieldConfigList = ref([])
	const dynamicFormData = ref([])
	// 打开抽屉
	const onOpen = (record, orgId) => {
		visible.value = true
		formData.value = {
			sortCode: 99,
			color:'#1677FF'
		}
		dynamicFormData.value = {}
		bizPositionApi
			.positionDynamicFieldConfigList({
				columnName: 'EXT_JSON'
			})
			.then((data) => {
				dynamicFieldConfigList.value = data
			})
		if (orgId) {
			formData.value.orgId = orgId
		}
		if (record) {
			formData.value = Object.assign({},formData.value, record)

			dynamicFormData.value = JSON.parse(formData.value.extJson) || {}
		}
		// 获取机构树
		bizPositionApi.positionOrgTreeSelector().then((res) => {
			treeData.value = res
		})
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
	}
	// 默认要校验的
	const formRules = {
		orgId: [required('请选择所属组织')],
		name: [required('请输入岗位名称')],
		category: [required('请选择岗位分类')],
		sortCode: [required('请选择排序')]
	}
	const positionCategoryOptions = tool.dictList('POSITION_CATEGORY')
	// 验证并提交数据
	const onSubmit = () => {
		const promiseList = []
		promiseList.push(
			new Promise((resolve, reject) => {
				formRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		promiseList.push(
			new Promise((resolve, reject) => {
				dynamicFormRef.value
					.validate()
					.then((result) => {
						resolve(result)
					})
					.catch((err) => {
						reject(err)
					})
			})
		)
		Promise.all(promiseList).then(() => {
			submitLoading.value = true
			formData.value.extJson = JSON.stringify(dynamicFormData.value)
			bizPositionApi
				.submitForm(formData.value, formData.value.id)
				.then(() => {
					visible.value = false
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
