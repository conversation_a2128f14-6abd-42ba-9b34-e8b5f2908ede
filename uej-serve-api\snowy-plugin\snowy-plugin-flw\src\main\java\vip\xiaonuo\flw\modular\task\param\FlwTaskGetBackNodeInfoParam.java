
package vip.xiaonuo.flw.modular.task.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取退回节点信息参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:46
 */
@Getter
@Setter
public class FlwTaskGetBackNodeInfoParam {

    /** 流程实例id */
    @Schema(description = "流程实例id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "processInstanceId不能为空")
    private String processInstanceId;

    /** 当前节点id */
    @Schema(description = "当前节点id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "currentActivityId不能为空")
    private String currentActivityId;
}
