package vip.xiaonuo.dev.modular.dm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.db.Db;
import cn.hutool.db.meta.MetaUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.stereotype.Service;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.core.prop.DomProperties;
import vip.xiaonuo.dev.modular.dfc.entity.DevDfc;
import vip.xiaonuo.dev.modular.dfc.result.DevDfcDbsSelectorResult;
import vip.xiaonuo.dev.modular.dm.param.*;
import vip.xiaonuo.dev.modular.dm.result.DevDmColumnsResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmConnectResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmInfoListResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmTablesResult;
import vip.xiaonuo.dev.modular.dm.service.DevDmService;
import vip.xiaonuo.ten.api.TenApi;

import javax.sql.DataSource;
import java.io.*;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class DevDmServiceImpl implements DevDmService {
    private static final String DB_URL_KEY = "spring.datasource.dynamic.datasource.master.url";

    private static final String DB_USERNAME_KEY = "spring.datasource.dynamic.datasource.master.username";

    private static final String DB_PASSWORD_KEY = "spring.datasource.dynamic.datasource.master.password";

    private static final String DB_DRIVER_CLASS_NAME_KEY = "spring.datasource.dynamic.datasource.master.driver-class-name";

    @Resource
    private Environment environment;

    @Resource
    private DbsApi dbsApi;

    @Resource
    private TenApi tenApi;

    @Resource
    private DomProperties dbProperties;

    @Override
    public DevDmConnectResult dbConnect(String dbsId) {
        String url = environment.getProperty(DB_URL_KEY);
        String userName = environment.getProperty(DB_USERNAME_KEY);
        String password = environment.getProperty(DB_PASSWORD_KEY);
        if (!"master".equals(dbsId)) {
            JSONObject jsonObject = dbsApi.dbsDetail(dbsId);
            url = jsonObject.getStr("url");
            userName = jsonObject.getStr("username");
            password = jsonObject.getStr("password");
        }
        if (ObjectUtil.hasEmpty(url, userName, password)) {
            throw new CommonException("当前数据源配置信息不完整");
        }
        return new DevDmConnectResult()
                .setUrl(url)
                .setUsername(userName)
                .setPassword(password);
    }

    @Override
    public List<DevDmInfoListResult> dbInfoList() {
        ArrayList<DevDmInfoListResult> devDmInfoListResults = CollectionUtil.newArrayList();
        // 只有主租户才有数据源的管理
        if (tenApi.getCurrentTenId().equals(tenApi.getDefaultTenId())) {
            devDmInfoListResults.add(
                    new DevDmInfoListResult()
                            // .setId(dbsApi.getCurrentDataSourceId())
                            // .setPoolName(dbsApi.getCurrentDataSourceName())
                            // .setDriverName(dbsApi.getCurrentDataSource().getConnection().getMetaData().getDriverName())
                            .setId("master")
                            .setPoolName("master")
                            .setDriverName(environment.getProperty(DB_DRIVER_CLASS_NAME_KEY))
            );
            devDmInfoListResults.addAll(
                    dbsApi.tenDbsSelector()
                            .stream()
                            .map(jsonObject ->
                                    JSONUtil.toBean(
                                            jsonObject,
                                            DevDmInfoListResult.class
                                    )
                            )
                            .toList()
            );
            return devDmInfoListResults;
        }
        return devDmInfoListResults;
    }

    @Override
    public List<DevDmTablesResult> tables(DevDmTablesParam devDmTablesParam) {
        String dbsId = devDmTablesParam.getDbsId();
        if (StrUtil.isEmpty(dbsId)){
            return CollectionUtil.newArrayList();
        }
        DevDmConnectResult devDmConnectResult = this.dbConnect(dbsId);
        return this.queryTables(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword()
        );
    }

    @Override
    public List<DevDmTablesResult> queryTables(String url, String userName, String password) {
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, userName, password);
            DatabaseMetaData metaData = conn.getMetaData();
            String schema = null;
            if (metaData.getURL().toLowerCase().contains("jdbc:oracle")) {
                schema = metaData.getUserName();
            }
            List<DevDmTablesResult> tables = new ArrayList<>();
            rs = metaData.getTables(null, schema, "%", new String[]{"TABLE"});

            // 授权的数据库表前缀
            String authTableNamePrefixes = dbProperties.getAuthTableNamePrefixes();
            if (StrUtil.isEmpty(authTableNamePrefixes)) {
                return tables;
            }
            String[] authTableNamePrefixesArr = authTableNamePrefixes.split(",");

            while (rs.next()) {
                String tableName = rs.getString("TABLE_NAME");
                // 授权的数据库表前缀才允许访问
                for (String authTableNamePrefix : authTableNamePrefixesArr) {
                    if (StrUtil.startWithIgnoreCase(tableName, authTableNamePrefix)) {
                        DevDmTablesResult genBasicTableResult = new DevDmTablesResult();
                        genBasicTableResult.setTableName(tableName);
                        String remarks = rs.getString("REMARKS");
                        if (ObjectUtil.isEmpty(remarks)) {
                            genBasicTableResult.setTableRemark(tableName);
                        } else {
                            genBasicTableResult.setTableRemark(remarks);
                        }
                        tables.add(genBasicTableResult);

                    }
                }
            }
            return tables;
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("获取表失败！");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }

    @Override
    public void addTable(DevDmAddTableParam devDmAddTableParam) {
        DevDmConnectResult devDmConnectResult = this.dbConnect(devDmAddTableParam.getDbsId());
        this.addTableData(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                devDmAddTableParam.getTableName(),
                devDmAddTableParam.getTableRemark()
        );
    }


    @Override
    public void addTableData(String url, String userName, String password, String tableName, String tableRemark) {
        this.tableNameIsAuth(tableName);
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, userName, password);

            DatabaseMetaData metaData = conn.getMetaData();
            String schema = null;
            if (metaData.getURL().toLowerCase().contains("jdbc:oracle")) {
                schema = metaData.getUserName();
            }
            rs = metaData.getTables(null, schema, "%", new String[]{"TABLE"});
            boolean isExeSql = true;
            while (rs.next()) {
                if (tableName.equals(rs.getString("TABLE_NAME"))) {
                    isExeSql = false;
                }
            }
            if (!isExeSql) {
                conn.close();
                throw new CommonException(StrUtil.format("表名称【{}】重复", tableName));
            }

            PreparedStatement preparedStatement = null;
            if (url.toLowerCase().contains("jdbc:mysql")) {
                String sql = "CREATE TABLE `{}` (" +
                        "`ID` varchar(20) NOT NULL COMMENT '主键'," +
                        "`TENANT_ID` varchar(20) DEFAULT NULL COMMENT '租户id'," +
                        "`EXT_JSON` longtext COMMENT '扩展信息'," +
                        "`DELETE_FLAG` varchar(255) DEFAULT NULL COMMENT '删除标志'," +
                        "`CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间'," +
                        "`CREATE_USER` varchar(20) DEFAULT NULL COMMENT '创建用户'," +
                        "`UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间'," +
                        "`UPDATE_USER` varchar(20) DEFAULT NULL COMMENT '更新用户'," +
                        "PRIMARY KEY (`ID`) USING BTREE" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='{}'";
                preparedStatement = conn.prepareStatement(
                        StrUtil.format(
                                sql,
                                tableName,
                                tableRemark
                        )
                );
            } else {
                throw new CommonException("当前数据库暂不支持！");
            }
            preparedStatement.executeUpdate();
            preparedStatement.close();
            conn.close();
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("添加表失败！");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }




    @Override
    public void editTable(DevDmEditTableParam devDmEditTableParam) {
        DevDmConnectResult devDmConnectResult = this.dbConnect(devDmEditTableParam.getDbsId());
        this.editTableData(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                devDmEditTableParam.getTableName(),
                devDmEditTableParam.getNewTableName(),
                devDmEditTableParam.getTableRemark()
        );
    }

    @Override
    public void editTableData(String url, String userName, String password, String tableName, String newTableName, String tableRemark) {
        this.tableNameIsAuth(tableName);
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, userName, password);

            DatabaseMetaData metaData = conn.getMetaData();
            String schema = null;
            if (metaData.getURL().toLowerCase().contains("jdbc:oracle")) {
                schema = metaData.getUserName();
            }
            rs = metaData.getTables(null, schema, "%", new String[]{"TABLE"});
            boolean isExeSql = true;
            while (rs.next()) {
                if (!tableName.equals(newTableName) && newTableName.equals(rs.getString("TABLE_NAME"))) {
                    isExeSql = false;
                }
            }
            if (!isExeSql) {
                conn.close();
                throw new CommonException(StrUtil.format("表名称【{}】重复", newTableName));
            }

            PreparedStatement editTableNamePreparedStatement = null;
            PreparedStatement editTableRemarkPreparedStatement = null;
            if (url.toLowerCase().contains("jdbc:mysql")) {
                String editTableNameSql = "ALTER TABLE {} RENAME TO {};";
                editTableNamePreparedStatement = conn.prepareStatement(
                        StrUtil.format(
                                editTableNameSql,
                                tableName,
                                newTableName
                        )
                );
                String editTableRemarkSql = "ALTER TABLE {} COMMENT '{}';";
                editTableRemarkPreparedStatement = conn.prepareStatement(
                        StrUtil.format(
                                editTableRemarkSql,
                                newTableName,
                                tableRemark
                        )
                );
            } else {
                throw new CommonException("当前数据库暂不支持！");
            }
            editTableNamePreparedStatement.execute();
            editTableNamePreparedStatement.close();
            editTableRemarkPreparedStatement.execute();
            editTableRemarkPreparedStatement.close();
            conn.close();
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("编辑表失败！");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }

    @Override
    public void deleteTable(DevDmDeleteTableParam devDmDeleteTableParam) {
        DevDmConnectResult devDmConnectResult = this.dbConnect(devDmDeleteTableParam.getDbsId());
        this.deleteTableData(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                devDmDeleteTableParam.getTableName()
        );
    }

    @Override
    public void deleteTableData(String url, String username, String password, String tableName) {
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, username, password);

            boolean executeUpdateSql = false;
            PreparedStatement preparedStatement = conn.prepareStatement(
                    StrUtil.format(
                            "SELECT COUNT(*) FROM {}",
                            tableName
                    )
            );
            ResultSet resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                if (resultSet.getInt(1) <= 0) {
                    executeUpdateSql = true;
                }
            }
            if (!executeUpdateSql) {
                preparedStatement.close();
                conn.close();
                throw new CommonException("数据库表存在数据，终止操作！");
            }

            if (url.toLowerCase().contains("jdbc:mysql")) {
                preparedStatement = conn.prepareStatement(
                        StrUtil.format(
                                "DROP TABLE {}",
                                tableName
                        )
                );
            }else {
                throw new CommonException("当前数据库暂不支持！");
            }
            preparedStatement.executeUpdate();
            preparedStatement.close();
            conn.close();
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("删除表失败！");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }


    @Override
    public List<DevDmColumnsResult> columns(DevDmColumnsParam devDmColumnsParam) {
        String dbsId = devDmColumnsParam.getDbsId();
        String tableName = devDmColumnsParam.getTableName();
        if (StrUtil.hasEmpty(dbsId, tableName)){
            return CollectionUtil.newArrayList();
        }
        DevDmConnectResult devDmConnectResult = this.dbConnect(dbsId);
        return this.queryTableColumns(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                tableName
        );
    }

    @Override
    public List<DevDmColumnsResult> queryTableColumns(String url, String userName, String password, String tableName) {
        this.tableNameIsAuth(tableName);
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, userName, password);
            DatabaseMetaData metaData = conn.getMetaData();
            String schema = null;
            if (metaData.getURL().toLowerCase().contains("jdbc:oracle")) {
                schema = metaData.getUserName();
            }
            List<DevDmColumnsResult> columns = new ArrayList<>();
            rs = metaData.getColumns(null, schema, tableName, "%");
            if(!rs.next()) {
                rs = metaData.getColumns(null, schema, tableName.toLowerCase(), "%");
            }
            while (rs.next()) {
                DevDmColumnsResult devDfcColumnsResult = new DevDmColumnsResult();

                String columnName = rs.getString("COLUMN_NAME").toUpperCase();
                devDfcColumnsResult.setColumnName(columnName);

                String remarks = rs.getString("REMARKS");
                if (ObjectUtil.isEmpty(remarks)) {
                    devDfcColumnsResult.setColumnRemark(columnName);
                } else {
                    devDfcColumnsResult.setColumnRemark(remarks);
                }

                String typeName = rs.getString("TYPE_NAME").toUpperCase();
                if (ObjectUtil.isNotEmpty(typeName)) {
                    devDfcColumnsResult.setColumnType(typeName);
                }

                int columnSize = rs.getInt("COLUMN_SIZE");
                if (ObjectUtil.isNotEmpty(columnSize)) {
                    devDfcColumnsResult.setColumnLength(columnSize);
                } else {
                    devDfcColumnsResult.setColumnLength(0);
                }

                columns.add(devDfcColumnsResult);
            }
            return columns;
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("获取数据库表字段失败，表名称：{}", tableName);
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }

    @Override
    public void addColumn(DevDmAddColumnParam devDmAddColumnParam) {
        DevDmConnectResult devDmConnectResult = this.dbConnect(devDmAddColumnParam.getDbsId());
        this.addColumnData(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                devDmAddColumnParam
        );
    }

    /**
     * 添加数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     **/
    @Override
    public void addColumnData(String url, String userName, String password, DevDmAddColumnParam devDmAddColumnParam) {
        this.tableNameIsAuth(devDmAddColumnParam.getTableName());
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, userName, password);

            DatabaseMetaData metaData = conn.getMetaData();
            String schema = null;
            if (metaData.getURL().toLowerCase().contains("jdbc:oracle")) {
                schema = metaData.getUserName();
            }
            rs = metaData.getColumns(null, schema, devDmAddColumnParam.getTableName(), "%");
            if(!rs.next()) {
                rs = metaData.getColumns(null, schema, devDmAddColumnParam.getTableName().toLowerCase(), "%");
            }
            boolean isExeSql = true;
            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME").toUpperCase();
                if (devDmAddColumnParam.getColumnName().equals(columnName)) {
                    isExeSql = false;
                }
            }
            if (!isExeSql) {
                conn.close();
                throw new CommonException(StrUtil.format("字段名称【{}】重复", devDmAddColumnParam.getColumnName()));
            }

            PreparedStatement preparedStatement = null;
            if (url.toLowerCase().contains("jdbc:mysql")) {
                String sql = StrUtil.format(
                        "ALTER TABLE {} ADD COLUMN {} {} NULL COMMENT '{}' {}",
                        devDmAddColumnParam.getTableName(),
                        devDmAddColumnParam.getColumnName(),
                        this.getColumnTypeStr(devDmAddColumnParam.getColumnType(), devDmAddColumnParam.getColumnLength()),
                        devDmAddColumnParam.getColumnRemark(),
                        this.getColumnPositionStr(devDmAddColumnParam.getAfterColumnName())
                );
                preparedStatement = conn.prepareStatement(sql);
            } else {
                throw new CommonException("当前数据库暂不支持！");
            }
            preparedStatement.executeUpdate();
            preparedStatement.close();
            conn.close();
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("添加字段失败！");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }

    @Override
    public void editColumn(DevDmEditColumnParam devDmEditColumnParam) {
        DevDmConnectResult devDmConnectResult = this.dbConnect(devDmEditColumnParam.getDbsId());
        this.editColumnData(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                devDmEditColumnParam
        );
    }

    @Override
    public void editColumnData(String url, String username, String password, DevDmEditColumnParam devDmEditColumnParam) {
        this.tableNameIsAuth(devDmEditColumnParam.getTableName());
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, username, password);
            boolean executeUpdateSql = false;
            PreparedStatement preparedStatement = conn.prepareStatement(
                    StrUtil.format(
                            "SELECT COUNT(*) FROM {} WHERE {} IS NOT NULL",
                            devDmEditColumnParam.getTableName(),
                            devDmEditColumnParam.getColumnName()
                    )
            );
            ResultSet resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                if (resultSet.getInt(1) <= 0) {
                    executeUpdateSql = true;
                }
            }
            if (!executeUpdateSql) {
                preparedStatement.close();
                conn.close();
                throw new CommonException("目标字段存在数据，终止操作！");
            }

            if (url.toLowerCase().contains("jdbc:mysql")) {
                String sql = StrUtil.format(
                        "ALTER TABLE `{}` CHANGE COLUMN `{}` `{}` {} COMMENT '{}' {}",
                        devDmEditColumnParam.getTableName(),
                        devDmEditColumnParam.getColumnName(),
                        devDmEditColumnParam.getNewColumnName(),
                        this.getColumnTypeStr(devDmEditColumnParam.getColumnType(), devDmEditColumnParam.getColumnLength()),
                        devDmEditColumnParam.getColumnRemark(),
                        this.getColumnPositionStr(devDmEditColumnParam.getAfterColumnName())
                );
                preparedStatement = conn.prepareStatement(sql);
            }else {
                throw new CommonException("当前数据库暂不支持！");
            }
            preparedStatement.executeUpdate();
            preparedStatement.close();
            conn.close();
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("编辑字段失败！");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }

    /**
     * 获取字段类型字符串
     *
     * @param columnType 字段类型
     * @param columnLength 字段长度
     * @return 字段类型字符串
     */
    private String getColumnTypeStr(String columnType, Integer columnLength) {
        String columnTypeStr = StrUtil.format("{}({})", columnType, columnLength);
        if (
                "TEXT".equals(columnType) ||
                "LONGTEXT".equals(columnType) ||
                "TIMESTAMP".equals(columnType) ||
                "DATE".equals(columnType) ||
                "TIME".equals(columnType) ||
                "DATETIME".equals(columnType) ||
                "YEAR".equals(columnType) ||
                "JSON".equals(columnType)
        ){
            columnTypeStr = columnType;
        }
        return columnTypeStr;
    }

    private String getColumnPositionStr(String afterColumnName) {
        if (ObjectUtil.isNotEmpty(afterColumnName)){
            return StrUtil.format("AFTER `{}`", afterColumnName);
        }
        return "FIRST";
    }

    @Override
    public void deleteColumn(DevDmDeleteColumnParam devDmDeleteColumnParam) {
        DevDmConnectResult devDmConnectResult = this.dbConnect(devDmDeleteColumnParam.getDbsId());
        this.deleteColumnData(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                devDmDeleteColumnParam.getTableName(),
                devDmDeleteColumnParam.getColumnName()
        );
    }

    @Override
    public void deleteColumnData(String url, String username, String password, String tableName, String columnName) {
        this.tableNameIsAuth(columnName);
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, username, password);
            boolean executeUpdateSql = false;
            PreparedStatement preparedStatement = conn.prepareStatement(
                    StrUtil.format(
                            "SELECT COUNT(*) FROM {} WHERE {} IS NOT NULL",
                            tableName,
                            columnName
                    )
            );
            ResultSet resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                if (resultSet.getInt(1) <= 0) {
                    executeUpdateSql = true;
                }
            }
            if (!executeUpdateSql) {
                preparedStatement.close();
                conn.close();
                throw new CommonException("目标字段存在数据，终止操作！");
            }
            if (url.toLowerCase().contains("jdbc:mysql")) {
                preparedStatement = conn.prepareStatement(
                        StrUtil.format(
                                "ALTER TABLE `{}` DROP COLUMN `{}`;",
                                tableName,
                                columnName
                        )
                );
            }else {
                throw new CommonException("当前数据库暂不支持！");
            }
            preparedStatement.executeUpdate();
            preparedStatement.close();
            conn.close();
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("删除数据失败！");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }

    /**
     * 判断表名称是否授权
     * @param tableName
     */
    private void tableNameIsAuth(String tableName) {
        if (StrUtil.isEmpty(tableName)){
            throw new CommonException("tableName空异常！");
        }
        boolean isAuthTable = false;
        // 授权的数据库表前缀
        String authTableNamePrefixes = dbProperties.getAuthTableNamePrefixes();
        if (StrUtil.isEmpty(authTableNamePrefixes)) {
            throw new CommonException(StrUtil.format("表名称【{}】未授权", tableName));
        }
        String[] authTableNamePrefixesArr = authTableNamePrefixes.split(",");
        // 授权的数据库表前缀才允许访问
        for (String authTableNamePrefix : authTableNamePrefixesArr) {
            if (StrUtil.startWithIgnoreCase(tableName, authTableNamePrefix)) {
                isAuthTable = true;
            }
        }
        if (!isAuthTable) {
            throw new CommonException(StrUtil.format("表名称【{}】未授权", tableName));
        }
    }
}
