
package vip.xiaonuo.biz.modular.offeringgroup.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import vip.xiaonuo.biz.modular.offering.entity.BizOffering;

import java.util.List;

/**
 * 服务或产品组合Id参数
 *
 * <AUTHOR>
 * @date  2024/06/12 16:57
 **/
@Getter
@Setter
public class BizOfferingGroupIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;

    private List<BizOffering> offerings;
}
