package vip.xiaonuo.biz.modular.giftcard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.giftcard.entity.BizGiftCard;
import vip.xiaonuo.biz.modular.giftcard.mapper.BizGiftCardMapper;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardAddParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardEditParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardIdParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardPageParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardVerifyParam;
import vip.xiaonuo.biz.modular.giftcard.param.BizGiftCardVerifyResult;
import vip.xiaonuo.biz.modular.giftcarddetail.entity.BizGiftCardDetail;
import vip.xiaonuo.biz.modular.giftcarddetail.service.BizGiftCardDetailService;
import vip.xiaonuo.biz.modular.giftcard.service.BizGiftCardService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 礼品卡信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/12 17:07
 **/
@Service
public class BizGiftCardServiceImpl extends ServiceImpl<BizGiftCardMapper, BizGiftCard> implements BizGiftCardService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Resource
    private BizGiftCardDetailService bizGiftCardDetailService;

    @Override
    public Page<BizGiftCard> page(BizGiftCardPageParam bizGiftCardPageParam) {
        QueryWrapper<BizGiftCard> queryWrapper = new QueryWrapper<BizGiftCard>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizGiftCardPageParam.getCardNumber())) {
            queryWrapper.lambda().eq(BizGiftCard::getCardNumber, bizGiftCardPageParam.getCardNumber());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardPageParam.getActived())) {
            queryWrapper.lambda().eq(BizGiftCard::getActived, bizGiftCardPageParam.getActived());
        }
        if(ObjectUtil.isNotEmpty(bizGiftCardPageParam.getOrgId())) {
            queryWrapper.lambda().eq(BizGiftCard::getOrgId, bizGiftCardPageParam.getOrgId());
        }
        if(ObjectUtil.isAllNotEmpty(bizGiftCardPageParam.getSortField(), bizGiftCardPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizGiftCardPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizGiftCardPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizGiftCardPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BizGiftCard::getCreateTime);
        }

        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            queryWrapper.lambda().in(BizGiftCard::getOrgId, loginUserDataScope);
        } else {
            // 如果没有数据范围权限，只能查看自己创建的礼品卡
            queryWrapper.lambda().eq(BizGiftCard::getCreateUser, StpLoginUserUtil.getLoginUser().getId());
        }

        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizGiftCardAddParam bizGiftCardAddParam) {
        // 礼品卡添加不需要特殊的权限校验，因为任何用户都可以添加礼品卡
        BizGiftCard bizGiftCard = BeanUtil.toBean(bizGiftCardAddParam, BizGiftCard.class);

        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizGiftCard.getOrgId())) {
            String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
            if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                bizGiftCard.setOrgId(currentUserOrgId);
            }
        }

        this.save(bizGiftCard);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizGiftCardEditParam bizGiftCardEditParam) {
        BizGiftCard bizGiftCard = this.queryEntity(bizGiftCardEditParam.getId());

        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizGiftCard.getOrgId())) {
                throw new CommonException("您没有权限编辑该机构下的礼品卡，机构id：{}", bizGiftCard.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能编辑自己创建的礼品卡
            if(!bizGiftCard.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限编辑该礼品卡，卡片id：{}", bizGiftCard.getId());
            }
        }

        BeanUtil.copyProperties(bizGiftCardEditParam, bizGiftCard);
        this.updateById(bizGiftCard);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizGiftCardIdParam> bizGiftCardIdParamList) {
        List<String> giftCardIdList = CollStreamUtil.toList(bizGiftCardIdParamList, BizGiftCardIdParam::getId);
        if(ObjectUtil.isNotEmpty(giftCardIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                List<BizGiftCard> giftCardList = this.listByIds(giftCardIdList);
                for(BizGiftCard giftCard : giftCardList) {
                    if(!loginUserDataScope.contains(giftCard.getOrgId())) {
                        throw new CommonException("您没有权限删除该机构下的礼品卡，机构id：{}", giftCard.getOrgId());
                    }
                }
            } else {
                List<BizGiftCard> giftCardList = this.listByIds(giftCardIdList);
                for(BizGiftCard giftCard : giftCardList) {
                    // 如果没有数据范围权限，只能删除自己创建的礼品卡
                    if(!giftCard.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                        throw new CommonException("您没有权限删除该礼品卡，卡片id：{}", giftCard.getId());
                    }
                }
            }
        }

        // 执行删除
        this.removeByIds(giftCardIdList);
    }

    @Override
    public BizGiftCard detail(BizGiftCardIdParam bizGiftCardIdParam) {
        BizGiftCard bizGiftCard = this.queryEntity(bizGiftCardIdParam.getId());

        // 校验数据范围 - 基于orgId进行权限控制
        List<String> loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            if(!loginUserDataScope.contains(bizGiftCard.getOrgId())) {
                throw new CommonException("您没有权限查看该机构下的礼品卡，机构id：{}", bizGiftCard.getOrgId());
            }
        } else {
            // 如果没有数据范围权限，只能查看自己创建的礼品卡
            if(!bizGiftCard.getCreateUser().equals(StpLoginUserUtil.getLoginUser().getId())) {
                throw new CommonException("您没有权限查看该礼品卡，卡片id：{}", bizGiftCard.getId());
            }
        }

        return bizGiftCard;
    }

    @Override
    public BizGiftCard queryEntity(String id) {
        BizGiftCard bizGiftCard = this.getById(id);
        if(ObjectUtil.isEmpty(bizGiftCard)) {
            throw new CommonException("Gift card information does not exist, ID value is {}", id);
        }
        return bizGiftCard;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizGiftCardServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizGiftCard.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }

    @Override
    public BizGiftCardVerifyResult verify(BizGiftCardVerifyParam bizGiftCardVerifyParam) {
        BizGiftCardVerifyResult result = new BizGiftCardVerifyResult();

        try {
            // 根据卡号查询礼品卡详情
            QueryWrapper<BizGiftCardDetail> queryWrapper = new QueryWrapper<BizGiftCardDetail>()
                    .checkSqlInjection()
                    .eq("card_number", bizGiftCardVerifyParam.getCardNo());

            BizGiftCardDetail giftCardDetail = bizGiftCardDetailService.getOne(queryWrapper);

            if (ObjectUtil.isEmpty(giftCardDetail)) {
                result.setSuccess(false);
                result.setMessage("Gift card does not exist.");
                result.setBalance(BigDecimal.ZERO);
                result.setAvailableBalance(BigDecimal.ZERO);
                return result;
            }

            // 校验数据权限 - 检查礼品卡是否属于当前用户有权限的组织
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
                result.setSuccess(false);
                result.setMessage("Unable to verify user permissions.");
                result.setBalance(BigDecimal.ZERO);
                result.setAvailableBalance(BigDecimal.ZERO);
                return result;
            }

            // 获取礼品卡的机构信息
            BizGiftCard giftCard = this.getById(giftCardDetail.getMainId());
            if (ObjectUtil.isEmpty(giftCard)) {
                result.setSuccess(false);
                result.setMessage("Gift card information not found.");
                result.setBalance(BigDecimal.ZERO);
                result.setAvailableBalance(BigDecimal.ZERO);
                return result;
            }

            // 检查权限
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查礼品卡所属机构是否在权限范围内
                if(ObjectUtil.isNotEmpty(giftCard.getOrgId()) && !loginUserDataScope.contains(giftCard.getOrgId())) {
                    result.setSuccess(false);
                    result.setMessage("Gift Card is not found.");
                    result.setBalance(BigDecimal.ZERO);
                    result.setAvailableBalance(BigDecimal.ZERO);
                    return result;
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    if(!giftCard.getCreateUser().equals(userId)) {
                        result.setSuccess(false);
                        result.setMessage("Gift Card is not found.");
                        result.setBalance(BigDecimal.ZERO);
                        result.setAvailableBalance(BigDecimal.ZERO);
                        return result;
                    }
                } catch (Exception e) {
                    result.setSuccess(false);
                    result.setMessage("Unable to verify user permissions.");
                    result.setBalance(BigDecimal.ZERO);
                    result.setAvailableBalance(BigDecimal.ZERO);
                    return result;
                }
            }

            // 验证PIN码
            if (!bizGiftCardVerifyParam.getPin().equals(giftCardDetail.getPin())) {
                result.setSuccess(false);
                result.setMessage("Incorrect PIN code.");
                result.setBalance(BigDecimal.ZERO);
                result.setAvailableBalance(BigDecimal.ZERO);
                return result;
            }

            // 检查是否激活
            if (!"1".equals(giftCardDetail.getActived())) {
                result.setSuccess(false);
                result.setMessage("Gift card not activated.");
                result.setBalance(BigDecimal.ZERO);
                result.setAvailableBalance(BigDecimal.ZERO);
                result.setActivated(false);
                return result;
            }

            // 检查是否过期
            boolean expired = false;
            if (StrUtil.isNotBlank(giftCardDetail.getExpDate())) {
                try {
                    LocalDate expDate = LocalDate.parse(giftCardDetail.getExpDate().substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    if (expDate.isBefore(LocalDate.now())) {
                        expired = true;
                    }
                } catch (Exception e) {
                    // 日期解析失败，忽略过期检查
                }
            }

            if (expired) {
                result.setSuccess(false);
                result.setMessage("Gift card has expired.");
                result.setBalance(BigDecimal.ZERO);
                result.setAvailableBalance(BigDecimal.ZERO);
                result.setExpired(true);
                return result;
            }

            // 验证成功，返回余额信息
            BigDecimal balance = BigDecimal.ZERO;
            BigDecimal availableBalance = BigDecimal.ZERO;

            try {
                if (StrUtil.isNotBlank(giftCardDetail.getValue())) {
                    balance = new BigDecimal(giftCardDetail.getValue());
                }
                if (StrUtil.isNotBlank(giftCardDetail.getRestValue())) {
                    availableBalance = new BigDecimal(giftCardDetail.getRestValue());
                } else {
                    availableBalance = balance;
                }
            } catch (NumberFormatException e) {
                // 数字格式错误，使用默认值
            }

            result.setSuccess(true);
            result.setMessage("Verification successful.");
            result.setBalance(balance);
            result.setAvailableBalance(availableBalance);
            result.setGiftCardId(giftCardDetail.getId());
            result.setActivated(true);
            result.setExpired(false);

            return result;

        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("An error occurred during verification: " + e.getMessage());
            result.setBalance(BigDecimal.ZERO);
            result.setAvailableBalance(BigDecimal.ZERO);
            return result;
        }
    }
}
