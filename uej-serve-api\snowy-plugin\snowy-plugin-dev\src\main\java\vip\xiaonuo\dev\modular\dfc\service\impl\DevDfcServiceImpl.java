
package vip.xiaonuo.dev.modular.dfc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.jdbc.support.JdbcUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.modular.dfc.entity.DevDfc;
import vip.xiaonuo.dev.modular.dfc.enums.DevDfcEnum;
import vip.xiaonuo.dev.modular.dfc.mapper.DevDfcMapper;
import vip.xiaonuo.dev.modular.dfc.param.*;
import vip.xiaonuo.dev.modular.dfc.result.DevDfcDbsSelectorResult;
import vip.xiaonuo.dev.modular.dfc.service.DevDfcService;
import vip.xiaonuo.dev.modular.dm.param.DevDmColumnsParam;
import vip.xiaonuo.dev.modular.dm.param.DevDmTablesParam;
import vip.xiaonuo.dev.modular.dm.result.DevDmConnectResult;
import vip.xiaonuo.dev.modular.dm.service.DevDmService;
import vip.xiaonuo.ten.api.TenApi;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 动态字段配置Service接口实现类
 *
 * <AUTHOR>
 * @date 2023/08/04 08:18
 **/
@Service
public class DevDfcServiceImpl extends ServiceImpl<DevDfcMapper, DevDfc> implements DevDfcService {

    @Resource
    private DevDmService devDmService;

    @Resource
    private DbsApi dbsApi;

    @Resource
    private TenApi tenApi;

    @Override
    public Page<DevDfc> page(DevDfcPageParam devDfcPageParam) {
        QueryWrapper<DevDfc> queryWrapper = new QueryWrapper<DevDfc>().checkSqlInjection();
        if (ObjectUtil.isNotEmpty(devDfcPageParam.getDbsId())) {
            queryWrapper.lambda().eq(DevDfc::getDbsId, devDfcPageParam.getDbsId());
        }
        if (ObjectUtil.isNotEmpty(devDfcPageParam.getTableName())) {
            queryWrapper.lambda().eq(DevDfc::getTableName, devDfcPageParam.getTableName());
        }
        if (ObjectUtil.isNotEmpty(devDfcPageParam.getColumnName())) {
            queryWrapper.lambda().eq(DevDfc::getColumnName, devDfcPageParam.getColumnName());
        }
        if (ObjectUtil.isAllNotEmpty(devDfcPageParam.getSortField(), devDfcPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(devDfcPageParam.getSortOrder());
            queryWrapper.orderBy(true, devDfcPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(devDfcPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(DevDfc::getDbsId).orderByAsc(DevDfc::getTableName).orderByAsc(DevDfc::getSortCode);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(DevDfcAddParam devDfcAddParam) {
        DevDfc devDfc = BeanUtil.toBean(devDfcAddParam, DevDfc.class);
        boolean repeatName = this.count(new LambdaQueryWrapper<DevDfc>()
                .eq(DevDfc::getDbsId, devDfc.getDbsId())
                .eq(DevDfc::getTableName, devDfc.getTableName())
                .eq(DevDfc::getName, devDfc.getName())) > 0;
        if (repeatName) {
            throw new CommonException("存在重复的表单域属性名，表单域属性名为：{}", devDfc.getName());
        }
        devDfc.setStatus(DevDfcEnum.ENABLE.getValue());
        this.save(devDfc);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(DevDfcEditParam devDfcEditParam) {
        DevDfc devDfc = this.queryEntity(devDfcEditParam.getId());
        BeanUtil.copyProperties(devDfcEditParam, devDfc);
        boolean repeatName = this.count(new LambdaQueryWrapper<DevDfc>()
                .eq(DevDfc::getDbsId, devDfc.getDbsId())
                .eq(DevDfc::getTableName, devDfc.getTableName())
                .eq(DevDfc::getName, devDfc.getName())
                .ne(DevDfc::getId, devDfc.getId())) > 0;
        if (repeatName) {
            throw new CommonException("存在重复的表单域属性名，表单域属性名为：{}", devDfc.getName());
        }
        this.updateById(devDfc);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<DevDfcIdParam> devDfcIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(devDfcIdParamList, DevDfcIdParam::getId));
    }

    @Override
    public DevDfc detail(DevDfcIdParam devDfcIdParam) {
        return this.queryEntity(devDfcIdParam.getId());
    }

    @Override
    public DevDfc queryEntity(String id) {
        DevDfc devDfc = this.getById(id);
        if (ObjectUtil.isEmpty(devDfc)) {
            throw new CommonException("动态字段配置不存在，id值为：{}", id);
        }
        return devDfc;
    }

    @Override
    public List<JSONObject> getList(String dbsId, String tableName, String columnName) {
        QueryWrapper<DevDfc> queryWrapper = new QueryWrapper<DevDfc>().checkSqlInjection();
        if (ObjectUtil.isNotEmpty(dbsId)) {
            queryWrapper.lambda().eq(DevDfc::getDbsId, dbsId);
        }
        if (ObjectUtil.isNotEmpty(tableName)) {
            queryWrapper.lambda().eq(DevDfc::getTableName, tableName);
        }
        if (ObjectUtil.isNotEmpty(columnName)) {
            queryWrapper.lambda().eq(DevDfc::getColumnName, columnName);
        }
        queryWrapper.lambda().eq(DevDfc::getStatus, DevDfcEnum.ENABLE.getValue());
        queryWrapper.lambda().orderByAsc(DevDfc::getSortCode);
        return this.list(queryWrapper)
                .stream()
                .map(item -> JSONUtil.createObj()
                        .set("name", item.getName())
                        .set("label", item.getLabel())
                        .set("type", item.getType())
                        .set("required", item.getRequired())
                        .set("placeholder", item.getPlaceholder())
                        .set("selectOptionType", item.getSelectOptionType())
                        .set("dictTypeCode", item.getDictTypeCode())
                        .set("selOptionApiUrl", item.getSelOptionApiUrl())
                        .set("selDataApiUrl", item.getSelDataApiUrl())
                        .set("isMultiple", item.getIsMultiple())
                )
                .collect(Collectors.toList());
    }

    @Override
    public void migrate(DevDfcMigrateParam devDfcMigrateParam) {
        DevDmConnectResult devDmConnectResult = devDmService.dbConnect(devDfcMigrateParam.getDbsId());
        this.migrateData(
                devDmConnectResult.getUrl(),
                devDmConnectResult.getUsername(),
                devDmConnectResult.getPassword(),
                devDfcMigrateParam
        );
    }

    /**
     * 迁移数据
     *
     * <AUTHOR>
     * @date 2023/2/1 10:31
     **/
    private void migrateData(String url, String userName, String password, DevDfcMigrateParam devDfcMigrateParam) {
        Connection conn = null;
        ResultSet rs = null;
        try {
            conn = DriverManager.getConnection(url, userName, password);
            DevDfc devDfc = this.getById(devDfcMigrateParam.getId());

            boolean executeUpdateSql = false;
            PreparedStatement preparedStatement = conn.prepareStatement(
                    StrUtil.format(
                            "SELECT COUNT(*) FROM {} WHERE {} IS NOT NULL",
                            devDfc.getTableName(),
                            devDfcMigrateParam.getMigrateTargetColumnName()
                    )
            );
            ResultSet resultSet = preparedStatement.executeQuery();
            while (resultSet.next()) {
                if (resultSet.getInt(1) <= 0) {
                    executeUpdateSql = true;
                }
            }
            if (!executeUpdateSql) {
                preparedStatement.close();
                conn.close();
                throw new CommonException("目标字段存在数据，终止数据迁移");
            }

            if (url.toLowerCase().contains("jdbc:mysql")) {
                preparedStatement = conn.prepareStatement(
                        StrUtil.format(
                                "UPDATE {} SET {} = JSON_UNQUOTE(JSON_EXTRACT({}, '$.{}'))",
                                devDfc.getTableName(),
                                devDfcMigrateParam.getDfDataStoreColumnName(),
                                devDfcMigrateParam.getMigrateTargetColumnName(),
                                devDfc.getName()
                        )
                );
            }else {
                throw new CommonException("当前数据库暂不支持数据迁移！");
            }
//            if (!url.toLowerCase().contains("jdbc:mysql")) {
//                preparedStatement = conn.prepareStatement(
//                        StrUtil.format(
//                                "UPDATE {} SET {} = JSON_VALUE({}, '$.{}')",
//                                devDfc.getTableName(),
//                                devDfcMigrateParam.getDfDataStoreColumnName(),
//                                devDfcMigrateParam.getMigrateTargetColumnName(),
//                                devDfc.getName()
//                        )
//                );
//            }
            preparedStatement.executeUpdate();
            preparedStatement.close();
            conn.close();
        } catch (SQLException sqlException) {
            sqlException.printStackTrace();
            throw new CommonException("迁移数据失败");
        } finally {
            JdbcUtils.closeResultSet(rs);
            JdbcUtils.closeConnection(conn);
        }
    }

    /* ====动态字段部分所需要用到的选择器==== */
    @Override
    public List<DevDfcDbsSelectorResult> dbsSelector() {
        ArrayList<DevDfcDbsSelectorResult> devDbInfoListResults = CollectionUtil.newArrayList();
        // 判断是否是主租户(过滤掉id隔离的租户)
        if (tenApi.getCurrentTenId().equals(tenApi.getDefaultTenId())) {
            // 当前的数据源id等于MASTER 主租户才会有这种情况(过滤掉db隔离的子租户)
            if (dbsApi.getCurrentDataSourceId().equals(dbsApi.getDefaultDataSourceName())){
                // 主租户
                // 自己的数据源
                devDbInfoListResults.add(
                        new DevDfcDbsSelectorResult()
                                .setId(dbsApi.getCurrentDataSourceId())
                                .setPoolName(dbsApi.getCurrentDataSourceName())
                );
                // 主租户除了能选择自己之外，还能选择主租户类型的数据源
                devDbInfoListResults.addAll(
                        dbsApi.masterDbsSelector()
                                .stream()
                                .map(jsonObject ->
                                        JSONUtil.toBean(
                                                jsonObject,
                                                DevDfcDbsSelectorResult.class
                                        )
                                ).collect(Collectors.toList()));
            } else {
                // db隔离子租户
                // 子租户只能选择自己的数据源
                devDbInfoListResults.add(
                        new DevDfcDbsSelectorResult()
                                .setId(dbsApi.getCurrentDataSourceId())
                                .setPoolName(dbsApi.getCurrentDataSourceName())
                );
            }
        } else {
            // id隔离子租户 只能选择主数据源
            // 自己的数据源
            devDbInfoListResults.add(
                    new DevDfcDbsSelectorResult()
                            .setId(dbsApi.getCurrentDataSourceId())
                            .setPoolName(dbsApi.getCurrentDataSourceName())
            );
        }

        return devDbInfoListResults;
    }

    @Override
    public List<JSONObject> dbTableSelector(DevDfcDbTableSelectorParam devDfcDbTableSelectorParam) {
        DevDmTablesParam devDmTablesParam = BeanUtil.copyProperties(devDfcDbTableSelectorParam, DevDmTablesParam.class);
        return devDmService.tables(devDmTablesParam)
                .stream()
                .map(item -> JSONUtil.createObj()
                        .set("tableName", item.getTableName())
                        .set("tableRemark", item.getTableRemark())
                ).collect(Collectors.toList());
    }

    @Override
    public List<JSONObject> dbColumnSelector(DevDfcDbColumnSelectorParam dbsTableColumnParam) {
        DevDmColumnsParam devDmColumnsParam = BeanUtil.copyProperties(dbsTableColumnParam, DevDmColumnsParam.class);
        return devDmService.columns(devDmColumnsParam)
                .stream()
                .map(item -> JSONUtil.createObj()
                        .set("columnName", item.getColumnName())
                        .set("typeName", item.getColumnType())
                        .set("columnRemark", item.getColumnRemark())
                ).collect(Collectors.toList());
    }


}
