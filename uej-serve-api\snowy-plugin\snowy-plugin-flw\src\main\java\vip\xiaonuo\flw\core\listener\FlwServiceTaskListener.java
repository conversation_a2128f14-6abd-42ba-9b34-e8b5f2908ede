
package vip.xiaonuo.flw.core.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import vip.xiaonuo.flw.core.util.NodeRuntimeUtil;
import vip.xiaonuo.flw.modular.process.service.FlwProcessService;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 服务任务（抄送）节点监听器
 *
 * <AUTHOR>
 * @date 2022/5/26 14:15
 **/
public class FlwServiceTaskListener implements JavaDelegate {

    @Override
    public void execute(DelegateExecution delegateExecution) {
        AtomicReference<String> participateInfo = new AtomicReference<>();
        List<String> participateUserIdList = CollectionUtil.newArrayList();
        delegateExecution.getBpmnModelElementInstance().getChildElementsByType(ExtensionElements.class).forEach(extensionElements ->
                extensionElements.getChildElementsByType(CamundaProperties.class).forEach(camundaProperties ->
                        camundaProperties.getChildElementsByType(CamundaProperty.class).forEach(camundaProperty -> {
                            if (camundaProperty.getCamundaName().equals("participateInfo")) {
                                participateInfo.set(camundaProperty.getCamundaValue());
                            }
                        })));
        if (ObjectUtil.isNotEmpty(participateInfo.get())) {
            participateUserIdList = NodeRuntimeUtil.handleUserTaskParticipateInfo(delegateExecution.getProcessInstanceId(), participateInfo.get());
        }
        SpringUtil.getBean(FlwProcessService.class).doCopy(delegateExecution.getProcessInstanceId(), participateUserIdList);
        // 发送抄送通知
        NodeRuntimeUtil.handleProcessCopyNotice(delegateExecution);
    }
}
