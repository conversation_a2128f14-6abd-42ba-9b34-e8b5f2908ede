package vip.xiaonuo.biz.modular.productcategory.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.productcategory.entity.BizProductCategory;
import vip.xiaonuo.biz.modular.productcategory.param.*;
import vip.xiaonuo.biz.modular.productcategory.service.BizProductCategoryService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 产品类目控制器
 *
 * <AUTHOR>
 * @date 2024/12/19
 **/
@Tag(name = "产品类目控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 6)
@RestController
@Validated
public class BizProductCategoryController {

    @Resource
    private BizProductCategoryService bizProductCategoryService;

    /**
     * 获取产品类目分页
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取产品类目分页")
    @SaCheckPermission("/biz/productCategory/page")
    @GetMapping("/biz/productCategory/page")
    public CommonResult<Page<BizProductCategory>> page(BizProductCategoryPageParam bizProductCategoryPageParam) {
        return CommonResult.data(bizProductCategoryService.page(bizProductCategoryPageParam));
    }

    /**
     * 获取产品类目树
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    @ApiOperationSupport(order = 2)
    @Operation(summary = "获取产品类目树")
    @SaCheckPermission("/biz/productCategory/tree")
    @GetMapping("/biz/productCategory/tree")
    public CommonResult<List<Tree<String>>> tree() {
        return CommonResult.data(bizProductCategoryService.tree());
    }

    /**
     * 添加产品类目
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    @ApiOperationSupport(order = 3)
    @Operation(summary = "添加产品类目")
    @CommonLog("添加产品类目")
    @SaCheckPermission("/biz/productCategory/add")
    @PostMapping("/biz/productCategory/add")
    public CommonResult<String> add(@RequestBody @Valid BizProductCategoryAddParam bizProductCategoryAddParam) {
        bizProductCategoryService.add(bizProductCategoryAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑产品类目
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    @ApiOperationSupport(order = 4)
    @Operation(summary = "编辑产品类目")
    @CommonLog("编辑产品类目")
    @SaCheckPermission("/biz/productCategory/edit")
    @PostMapping("/biz/productCategory/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizProductCategoryEditParam bizProductCategoryEditParam) {
        bizProductCategoryService.edit(bizProductCategoryEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除产品类目
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    @ApiOperationSupport(order = 5)
    @Operation(summary = "删除产品类目")
    @CommonLog("删除产品类目")
    @SaCheckPermission("/biz/productCategory/delete")
    @PostMapping("/biz/productCategory/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "List cannot be empty")
                                           List<BizProductCategoryIdParam> bizProductCategoryIdParamList) {
        bizProductCategoryService.delete(bizProductCategoryIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取产品类目详情
     *
     * <AUTHOR>
     * @date 2024/12/19
     */
    @ApiOperationSupport(order = 6)
    @Operation(summary = "获取产品类目详情")
    @SaCheckPermission("/biz/productCategory/detail")
    @GetMapping("/biz/productCategory/detail")
    public CommonResult<BizProductCategory> detail(@Valid BizProductCategoryIdParam bizProductCategoryIdParam) {
        return CommonResult.data(bizProductCategoryService.detail(bizProductCategoryIdParam));
    }
}