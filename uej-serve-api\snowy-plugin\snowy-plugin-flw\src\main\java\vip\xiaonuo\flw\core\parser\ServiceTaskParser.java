
package vip.xiaonuo.flw.core.parser;

import cn.hutool.core.util.ObjectUtil;
import org.camunda.bpm.model.bpmn.builder.*;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.flw.core.listener.FlwServiceTaskListener;
import vip.xiaonuo.flw.core.node.FlwNode;
import vip.xiaonuo.flw.core.util.NodeInfoUtil;
import vip.xiaonuo.flw.core.util.NodePropertyUtil;

public class ServiceTaskParser {

    /**
     * 构建单个服务任务节点
     *
     * <AUTHOR>
     * @date 2022/3/24 9:24
     **/
    @SuppressWarnings("ALL")
    public static ServiceTaskBuilder buildServiceTaskSingle(AbstractFlowNodeBuilder flowNodeBuilder, FlwNode flwNode) {
        NodeInfoUtil.validFlwNode(flwNode);
        ServiceTaskBuilder serviceTaskBuilder = flowNodeBuilder.serviceTask(flwNode.getId()).name(flwNode.getTitle())
                .camundaClass(FlwServiceTaskListener.class);
        NodePropertyUtil.parseProperties(serviceTaskBuilder, flwNode);
        return serviceTaskBuilder;
    }

    /**
     * 用户任务节点->服务任务节点
     *
     * <AUTHOR>
     * @date 2022/3/18 16:40
     **/
    @SuppressWarnings("ALL")
    public static AbstractFlowNodeBuilder buildServiceTask(UserTaskBuilder userTaskBuilder, FlwNode flwNode) {
        ServiceTaskBuilder serviceTaskBuilderNew = buildServiceTaskSingle(userTaskBuilder, flwNode);
        // 执行递归
        return executeRecursion(serviceTaskBuilderNew, flwNode);
    }

    /**
     * 服务任务节点->服务任务节点
     *
     * <AUTHOR>
     * @date 2022/3/18 16:40
     **/
    @SuppressWarnings("ALL")
    public static AbstractFlowNodeBuilder buildServiceTask(ServiceTaskBuilder serviceTaskBuilder, FlwNode flwNode) {
        ServiceTaskBuilder serviceTaskBuilderNew = buildServiceTaskSingle(serviceTaskBuilder, flwNode);
        // 执行递归
        return executeRecursion(serviceTaskBuilderNew, flwNode);
    }

    /**
     * 条件连接线->服务任务节点
     *
     * <AUTHOR>
     * @date 2022/3/18 16:40
     **/
    @SuppressWarnings("ALL")
    public static AbstractFlowNodeBuilder buildServiceTask(ExclusiveGatewayBuilder exclusiveGatewayBuilder, FlwNode flwNode) {
        ServiceTaskBuilder serviceTaskBuilderNew = buildServiceTaskSingle(exclusiveGatewayBuilder, flwNode);
        // 执行递归
        return executeRecursion(serviceTaskBuilderNew, flwNode);
    }

    /**
     * 并行网关->服务任务节点
     *
     * <AUTHOR>
     * @date 2022/3/18 16:40
     **/
    @SuppressWarnings("ALL")
    public static AbstractFlowNodeBuilder buildServiceTask(ParallelGatewayBuilder parallelGatewayBuilder, FlwNode flwNode) {
        ServiceTaskBuilder serviceTaskBuilderNew = buildServiceTaskSingle(parallelGatewayBuilder, flwNode);
        // 执行递归
        return executeRecursion(serviceTaskBuilderNew, flwNode);
    }

    /**
     * 抽象节点->服务任务节点
     *
     * <AUTHOR>
     * @date 2022/3/18 16:40
     **/
    @SuppressWarnings("ALL")
    public static AbstractFlowNodeBuilder buildServiceTask(AbstractFlowNodeBuilder abstractFlowNodeBuilder, FlwNode flwNode) {
        ServiceTaskBuilder serviceTaskBuilderNew = buildServiceTaskSingle(abstractFlowNodeBuilder, flwNode);
        // 执行递归
        return executeRecursion(serviceTaskBuilderNew, flwNode);
    }

    /**
     * 执行递归
     *
     * <AUTHOR>
     * @date 2022/3/21 23:21
     */
    @SuppressWarnings("ALL")
    public static AbstractFlowNodeBuilder executeRecursion(ServiceTaskBuilder serviceTaskBuilder, FlwNode flwNode) {
        // 获取子节点
        FlwNode childNode = flwNode.getChildNode();
        // 如果子节点为空则结束
        if(ObjectUtil.isEmpty(childNode) || ObjectUtil.isEmpty(childNode.getId())) {
            return serviceTaskBuilder;
        } else {
            if(NodeInfoUtil.isUserTask(childNode)) {
                // 如果子节点是用户任务
                return UserTaskParser.buildUserTask(serviceTaskBuilder, childNode);
            } else if(NodeInfoUtil.isServiceTask(childNode)) {
                // 如果子节点是服务任务
                return ServiceTaskParser.buildServiceTask(serviceTaskBuilder, childNode);
            } else if(NodeInfoUtil.isExclusiveGateway(childNode)) {
                // 如果子节点是排他网关
                return ExclusiveGatewayParser.buildExclusiveGateway(serviceTaskBuilder, childNode);
            } else if(NodeInfoUtil.isParallelGateway(childNode)) {
                // 如果子节点是并行网关
                return ParallelGatewayParser.buildParallelGateway(serviceTaskBuilder, childNode);
            }  else {
                throw new CommonException("流程JSON解析格式错误，不支持的类型：{}", childNode.getType());
            }
        }
    }
}
