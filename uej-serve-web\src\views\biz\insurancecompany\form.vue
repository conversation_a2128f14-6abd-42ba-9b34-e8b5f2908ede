<template>
    <xn-form-container
        :title="formData.id ? 'edit insurance company' : 'add insurance company'"
        :width="700"
        v-model:open="open"
        :destroy-on-close="true"
        @close="onClose"
    >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="name：" name="name">
                        <a-input v-model:value="formData.name" placeholder="Please enter name" allow-clear />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
        <a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
            <a-row :gutter="16">
                <a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
                    <xn-form-item :fieldConfig="item" :formData="dynamicFormData"/>
                </a-col>
            </a-row>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
        </template>
    </xn-form-container>
</template>

<script setup name="bizInsuranceCompanyForm">
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import bizInsuranceCompanyApi from '@/api/biz/bizInsuranceCompanyApi'
    // 抽屉状态
    const open = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    // 动态表单
    const dynamicFormRef = ref()
    const dynamicFieldConfigList = ref([])
    const dynamicFormData = ref({})

    // 打开抽屉
    const onOpen = (record) => {
        open.value = true
        bizInsuranceCompanyApi.bizInsuranceCompanyDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
            dynamicFieldConfigList.value = data
        })
        if (record) {
            let recordData = cloneDeep(record)
            formData.value = Object.assign({}, recordData)
            dynamicFormData.value = JSON.parse(formData.value.extJson) || {}
        }
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        dynamicFormData.value = {}
        open.value = false
    }
    // 默认要校验的
    const formRules = {
    }
    // 验证并提交数据
    const onSubmit = () => {
        const promiseList = []
        promiseList.push(
            new Promise((resolve, reject) => {
                formRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        promiseList.push(
            new Promise((resolve, reject) => {
                dynamicFormRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        submitLoading.value = true
        Promise.all(promiseList)
            .then(() => {
            const formDataParam = cloneDeep(formData.value)
            formDataParam.extJson = JSON.stringify(dynamicFormData.value)
            bizInsuranceCompanyApi
                .bizInsuranceCompanySubmitForm(formDataParam, formDataParam.id)
                .then(() => {
                    onClose()
                    emit('successful')
                })
                .finally(() => {
                    submitLoading.value = false
                })
            })
            .catch(() => {})
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
