
package vip.xiaonuo.flw.modular.process.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程转办参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:45
 */
@Getter
@Setter
public class FlwProcessTurnParam {

    /** 流程id */
    @Schema(description = "流程id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 要转给的用户Id */
    @Schema(description = "要转给的用户Id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "userId不能为空")
    private String userId;
}
