
package vip.xiaonuo.biz.modular.voucher.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 代金券信息编辑参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:17
 **/
@Getter
@Setter
public class BizVoucherEditParam {

    /** ID */
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** Voucher Number */
    @Schema(description = "Voucher Number")
    private String voucherNumber;

    /** Create Date */
    @Schema(description = "Create Date")
    private String createDate;

    /** Used Date */
    @Schema(description = "Used Date")
    private String usedDate;

    /** Value */
    @Schema(description = "Value")
    private String value;

    private String validityDate;

    private String threshold;

    private String status;

    private String extJson;

    private String orgId;
}
