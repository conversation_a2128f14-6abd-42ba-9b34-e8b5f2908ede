
package vip.xiaonuo.dev.modular.dfc.provider;

import cn.hutool.json.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.dev.modular.dfc.service.DevDfcService;

import java.util.List;

/**
 * 动态字段API接口实现类
 *
 * <AUTHOR>
 * @date 2022/9/2 16:05
 */
@Service
public class DevDfcApiProvider implements DevDfcApi {

    @Resource
    private DevDfcService devDfcService;

    @Override
    public List<JSONObject> getList(String dbsId, String tableName, String columnName) {
        return devDfcService.getList(dbsId, tableName, columnName);
    }
}
