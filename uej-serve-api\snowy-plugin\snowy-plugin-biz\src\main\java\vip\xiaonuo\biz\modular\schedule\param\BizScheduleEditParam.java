
package vip.xiaonuo.biz.modular.schedule.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 排班编辑参数
 *
 * <AUTHOR>
 * @date  2024/06/18 16:16
 **/
@Getter
@Setter
public class BizScheduleEditParam {

    /** ID */
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** Date ID */
    @Schema(description = "Date ID")
    private String dateId;

    /** Staff ID */
    @Schema(description = "Staff ID")
    private String staffId;

    /** Staff Name */
    @Schema(description = "Staff Name")
    private String staffName;

    private String organizationId;

    private String organizationName;

    /** Work Time */
    @Schema(description = "Work Time")
    private String workTime;

    /** Open Time */
    @Schema(description = "Open Time")
    private String openTime;

    /** Rest Time */
    @Schema(description = "Rest Time")
    private String restTime;

    /** Assign Date */
    @Schema(description = "Assign Date")
    private String assignDate;

    /** Accept */
    @Schema(description = "Accept")
    private String accept;

    private String extJson;
}
