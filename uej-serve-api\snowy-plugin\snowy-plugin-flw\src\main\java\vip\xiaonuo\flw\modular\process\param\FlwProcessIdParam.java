
package vip.xiaonuo.flw.modular.process.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程Id参数
 *
 * <AUTHOR>
 * @date 2022/8/1 10:32
 */
@Getter
@Setter
public class FlwProcessIdParam {

    /** 流程id */
    @Schema(description = "流程id")
    @NotBlank(message = "id不能为空")
    private String id;
}
