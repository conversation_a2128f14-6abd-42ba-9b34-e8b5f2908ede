
package vip.xiaonuo.biz.modular.dailybalance.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.dailybalance.entity.BizDailyBalance;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceAddParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceEditParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalanceIdParam;
import vip.xiaonuo.biz.modular.dailybalance.param.BizDailyBalancePageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 项目结款记录表Service接口
 *
 * <AUTHOR>
 * @date  2024/12/02 13:50
 **/
public interface BizDailyBalanceService extends IService<BizDailyBalance> {

    /**
     * 获取项目结款记录表分页
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    Page<BizDailyBalance> page(BizDailyBalancePageParam bizDailyBalancePageParam);

    /**
     * 添加项目结款记录表
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    void add(BizDailyBalanceAddParam bizDailyBalanceAddParam);

    /**
     * 编辑项目结款记录表
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    void edit(BizDailyBalanceEditParam bizDailyBalanceEditParam);

    /**
     * 删除项目结款记录表
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    void delete(List<BizDailyBalanceIdParam> bizDailyBalanceIdParamList);

    /**
     * 获取项目结款记录表详情
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     */
    BizDailyBalance detail(BizDailyBalanceIdParam bizDailyBalanceIdParam);

    /**
     * 获取项目结款记录表详情
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
     **/
    BizDailyBalance queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/12/02 13:50
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);

    /**
     * 检查当天是否已提交日扎帐
     *
     * <AUTHOR>
     * @date  2024/12/19
     */
    Boolean checkTodaySubmitted(String orgId, String date);

    /**
     * 解锁日扎帐提交
     *
     * <AUTHOR>
     * @date  2024/12/19
     */
    void unlockDailyBalance(String orgId, String date);
}
