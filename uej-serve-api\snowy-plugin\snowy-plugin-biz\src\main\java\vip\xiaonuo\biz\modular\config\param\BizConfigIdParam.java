
package vip.xiaonuo.biz.modular.config.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 业务配置Id参数
 *
 * <AUTHOR>
 * @date  2024/07/08 19:09
 **/
@Getter
@Setter
public class BizConfigIdParam {

    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;
}
