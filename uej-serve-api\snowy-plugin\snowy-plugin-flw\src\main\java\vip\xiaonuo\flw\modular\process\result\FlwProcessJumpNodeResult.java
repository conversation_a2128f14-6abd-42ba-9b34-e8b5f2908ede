
package vip.xiaonuo.flw.modular.process.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 跳转节点信息结果
 *
 * <AUTHOR>
 * @date 2022/5/22 16:19
 */
@Getter
@Setter
public class FlwProcessJumpNodeResult {

    /** 节点id */
    @Schema(description = "节点id")
    private String id;

    /** 节点名称 */
    @Schema(description = "节点名称")
    private String name;
}
