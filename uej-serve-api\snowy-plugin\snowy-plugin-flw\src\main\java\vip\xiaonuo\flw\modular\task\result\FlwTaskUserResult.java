
package vip.xiaonuo.flw.modular.task.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户选择器结果
 *
 * <AUTHOR>
 * @date 2022/7/22 14:29
 **/
@Getter
@Setter
public class FlwTaskUserResult {

    /** id */
    @Schema(description = "id")
    private String id;

    /** 组织id */
    @Schema(description = "组织id")
    private String orgId;

    /** 名称 */
    @Schema(description = "名称")
    private String name;

    /** 账号 */
    @Schema(description = "账号")
    private String account;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;
}
