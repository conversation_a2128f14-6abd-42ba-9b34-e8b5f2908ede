package vip.xiaonuo.dev.modular.dm.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.dev.modular.dm.param.*;
import vip.xiaonuo.dev.modular.dm.result.DevDmColumnsResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmInfoListResult;
import vip.xiaonuo.dev.modular.dm.result.DevDmTablesResult;
import vip.xiaonuo.dev.modular.dm.service.DevDmService;

import javax.validation.Valid;
import java.sql.SQLException;
import java.util.List;

/**
 * 数据库管理控制器
 *
 * <AUTHOR>
 * @date  2023/08/04 08:18
 */
@Tag(name = "数据建模控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 11)
@RestController
@Validated
public class DevDmController {
    @Resource
    private DevDmService devDmService;

    /**
     * 获取所有数据源信息
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取所有数据源信息")
    @GetMapping("/dev/dm/dbInfoList")
    public CommonResult<List<DevDmInfoListResult>> dbInfoList() {
        return CommonResult.data(devDmService.dbInfoList());
    }

    /**
     * 获取所有表信息
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 2)
    @Operation(summary = "获取所有表信息")
    @GetMapping("/dev/dm/tables")
    public CommonResult<List<DevDmTablesResult>> tables(DevDmTablesParam devDmTablesParam) {
        return CommonResult.data(devDmService.tables(devDmTablesParam));
    }

    /**
     * 添加数据库表
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    @ApiOperationSupport(order = 3)
    @Operation(summary = "添加数据库表")
    @PostMapping("/dev/dm/addTable")
    public CommonResult<String> addTable(@RequestBody @Valid DevDmAddTableParam devDmAddTableParam) {
        devDmService.addTable(devDmAddTableParam);
        return CommonResult.ok();
    }

    /**
     * 修改数据库表
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    @ApiOperationSupport(order = 4)
    @Operation(summary = "编辑数据库表")
    @PostMapping("/dev/dm/editTable")
    public CommonResult<String> editTable(@RequestBody @Valid DevDmEditTableParam devDmEditTableParam) {
        devDmService.editTable(devDmEditTableParam);
        return CommonResult.ok();
    }
    /**
     * 修改数据库表
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    @ApiOperationSupport(order = 5)
    @Operation(summary = "删除数据库表")
    @PostMapping("/dev/dm/deleteTable")
    public CommonResult<String> deleteTable(@RequestBody @Valid DevDmDeleteTableParam devDmDeleteTableParam) {
        devDmService.deleteTable(devDmDeleteTableParam);
        return CommonResult.ok();
    }


    /**
     * 获取表内所有字段信息
     *
     * <AUTHOR>
     * @date  2023/08/04 08:18
     */
    @ApiOperationSupport(order = 6)
    @Operation(summary = "获取表内所有字段信息")
    @GetMapping("/dev/dm/columns")
    public CommonResult<List<DevDmColumnsResult>> columns(DevDmColumnsParam devDmColumnsParam) {
        return CommonResult.data(devDmService.columns(devDmColumnsParam));
    }

    /**
     * 添加数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    @ApiOperationSupport(order = 7)
    @Operation(summary = "添加数据库字段")
    @PostMapping("/dev/dm/addColumn")
    public CommonResult<String> addColumn(@RequestBody @Valid DevDmAddColumnParam devDmAddColumnParam) {
        devDmService.addColumn(devDmAddColumnParam);
        return CommonResult.ok();
    }

    /**
     * 编辑数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    @ApiOperationSupport(order = 8)
    @Operation(summary = "编辑数据库字段")
    @PostMapping("/dev/dm/editColumn")
    public CommonResult<String> editColumn(@RequestBody @Valid DevDmEditColumnParam devDmEditColumnParam) {
        devDmService.editColumn(devDmEditColumnParam);
        return CommonResult.ok();
    }

    /**
     * 删除数据库字段
     *
     * <AUTHOR>
     * @date 2024/3/21 23:31
     */
    @ApiOperationSupport(order = 9)
    @Operation(summary = "删除数据库字段")
    @PostMapping("/dev/dm/deleteColumn")
    public CommonResult<String> deleteColumn(@RequestBody @Valid DevDmDeleteColumnParam devDmDeleteColumnParam) {
        devDmService.deleteColumn(devDmDeleteColumnParam);
        return CommonResult.ok();
    }
}
