package vip.xiaonuo.biz.modular.webhook.service;

import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.webhook.param.WordPressWebhookParam;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Webhook服务接口
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
public interface WebhookService {

    /**
     * 验证webhook签名
     *
     * @param request 请求对象
     * @param webhookParam webhook参数
     * @return 验证结果
     */
    boolean verifyWebhookSignature(HttpServletRequest request, WordPressWebhookParam webhookParam);

    /**
     * 验证API Key（URL参数）
     *
     * @param request 请求对象
     * @return 验证结果
     */
    boolean verifyWebhookApiKey(HttpServletRequest request);

    /**
     * 从WordPress创建任务
     *
     * @param webhookParam WordPress传来的数据
     * @return 创建的任务ID
     */
    String createTaskFromWordPress(WordPressWebhookParam webhookParam);

    /**
     * 从WordPress修改任务
     *
     * @param webhookParam WordPress传来的数据
     * @return 修改的任务ID
     */
    String updateTaskFromWordPress(WordPressWebhookParam webhookParam);

    /**
     * 从WordPress取消任务
     *
     * @param webhookParam WordPress传来的数据
     * @return 处理结果
     */
    String cancelTaskFromWordPress(WordPressWebhookParam webhookParam);

    /**
     * 通过WordPress预约ID查找任务
     *
     * @param wordpressId WordPress预约ID
     * @return 任务对象
     */
    BizTask findTaskByWordPressId(String wordpressId);

    /**
     * 映射WordPress员工ID到系统用户ID
     *
     * @param wordpressStaffId WordPress员工ID
     * @return 系统用户ID
     */
    String mapWordPressStaffToSystemUser(String wordpressStaffId);

    /**
     * 映射WordPress服务ID到系统产品ID
     *
     * @param wordpressServiceId WordPress服务ID
     * @return 系统产品ID
     */
    String mapWordPressServiceToSystemProduct(String wordpressServiceId);

    /**
     * 通过Amelia location名称映射到系统组织ID
     *
     * @param locationName Amelia中的location名称
     * @return 系统组织ID
     */
    String mapAmeliaLocationToSystemOrg(String locationName);
} 