<template>
	<a-spin :spinning="loading">
		<a-result title="Log in first" v-if="!token">
			<template #extra>
				<a-button type="primary" @click="gohome">{{ $t('login.backLogin') }}</a-button>
			</template>
		</a-result>
		<a-result :title="text" v-if="token">
			<template #icon>
				<smile-twoTone />
			</template>
			<template #extra>
				<div class="flex flex-col gap-2 items-center">
					<a-button type="primary" @click="check('clockIn')" class="w-[200px]">ClockIn</a-button>
					<a-button type="primary" @click="check('clockOut')" class="w-[200px]">ClockOut</a-button>
					<a-button type="primary" @click="replace" class="w-[200px]">{{ $t('login.backLogin') }}</a-button>
				</div>
			</template>
		</a-result>
	</a-spin>
</template>

<script name="clockCheck" setup>
	import tool from '@/utils/tool'
	import bizAttendanceApi from '@/api/biz/bizAttendanceApi'
	import { useRoute } from 'vue-router';
	const token = tool.data.get('TOKEN')
	const loading = ref(false)
	const text = ref('')
	const route = useRoute()
	const date=ref('')
	const gohome = () => {
		tool.data.set('LAST_VIEWS_PATH', '/biz/clockCheck?date='+date.value)
		window.location.href = '/login'
	}

	onMounted(() => {
	    date.value= route.query.date
	})

	const check = (val) => {
		loading.value = true
		text.value = `${val} in...`
		bizAttendanceApi
			.bizAttendanceClock({ time: date.value, type: val })
			.then((res) => {
				text.value = `${val} successfully`
				loading.value = false
				setTimeout(() => {
					text.value = ''
				}, 5000)
			})
			.catch(() => {
				loading.value = false
				text.value = ''
			})
	}
	const replace = () => {
		window.location.href = '/'
	}
</script>

<style lang="less" scoped></style>
