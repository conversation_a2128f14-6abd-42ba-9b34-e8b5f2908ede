
package vip.xiaonuo.dbs.modular.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据源添加参数
 *
 * <AUTHOR>
 * @date 2022/7/29 9:59
 */
@Getter
@Setter
public class DbsStorageAddParam {

    /** 名称 */
    @Schema(description = "名称")
    @NotBlank(message = "poolName不能为空")
    private String poolName;

    /** 连接URL */
    @Schema(description = "连接URL")
    @NotBlank(message = "url不能为空")
    private String url;

    /** 用户名 */
    @Schema(description = "用户名")
    @NotBlank(message = "username不能为空")
    private String username;

    /** 密码 */
    @Schema(description = "密码")
    @NotBlank(message = "password不能为空")
    private String password;

    /** 驱动名称 */
    @Schema(description = "驱动名称")
    @NotBlank(message = "driverName不能为空")
    private String driverName;

    /** 分类 */
    @Schema(description = "分类")
    @NotBlank(message = "category不能为空")
    private String category;

    /** 排序码 */
    @Schema(description = "排序码")
    @NotNull(message = "sortCode不能为空")
    private Integer sortCode;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;
}
