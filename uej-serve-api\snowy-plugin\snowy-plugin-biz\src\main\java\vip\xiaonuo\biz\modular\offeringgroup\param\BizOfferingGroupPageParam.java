
package vip.xiaonuo.biz.modular.offeringgroup.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.biz.modular.offering.entity.BizOffering;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 服务或产品组合查询参数
 *
 * <AUTHOR>
 * @date  2024/06/12 16:57
 **/
@Getter
@Setter
public class BizOfferingGroupPageParam {

    /** 当前页 */
    @Schema(description = "当前页码")
    private Integer current;

    /** 每页条数 */
    @Schema(description = "每页条数")
    private Integer size;

    /** 排序字段 */
    @Schema(description = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @Schema(description = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @Schema(description = "关键词")
    private String searchKey;

    /** Category */
    @Schema(description = "Category")
    private String category;

    /** Name */
    @Schema(description = "Name")
    private String name;

    private List<BizOffering> offerings;

}
