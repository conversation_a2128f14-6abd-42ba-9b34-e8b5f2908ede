
package vip.xiaonuo.biz.modular.offering.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 服务或产品清单Id参数
 *
 * <AUTHOR>
 * @date  2024/06/12 16:09
 **/
@Getter
@Setter
public class BizOfferingIdParam {

    /** ID */
    @Schema(description = "ID")
    @NotBlank(message = "id不能为空")
    private String id;
}
