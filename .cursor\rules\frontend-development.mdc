---
description:
globs:
alwaysApply: false
---
# 前端开发规范

## 项目技术栈

前端项目基于以下技术栈开发：
- Vue框架
- Vite构建工具
- Tailwind CSS
- Ant Design Vue组件库

## 开发规范

### 组件开发
- 组件文件应放置在 [src/components/](mdc:uej-serve-web/src/components/) 目录下
- 页面视图应放置在 [src/views/](mdc:uej-serve-web/src/views/) 目录下
- 组件应遵循单一职责原则，每个组件只负责单一功能
- 组件命名应使用PascalCase格式

### API接口
- API接口应定义在 [src/api/](mdc:uej-serve-web/src/api/) 目录下
- 接口应按照功能模块进行分类

### 路由
- 路由配置应在 [src/router/](mdc:uej-serve-web/src/router/) 目录下管理
- 路由应按照功能模块进行分类

### 状态管理
- 全局状态应在 [src/store/](mdc:uej-serve-web/src/store/) 目录下管理
- 状态应按照功能模块进行分类

## 启动与构建

- 安装依赖：`npm install`
- 开发模式启动：`npm run serve`
- 本地访问地址：http://localhost:81
