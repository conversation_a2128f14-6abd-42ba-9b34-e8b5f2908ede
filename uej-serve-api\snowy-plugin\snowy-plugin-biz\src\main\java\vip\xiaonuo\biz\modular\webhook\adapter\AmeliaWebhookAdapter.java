package vip.xiaonuo.biz.modular.webhook.adapter;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vip.xiaonuo.biz.modular.webhook.param.AmeliaWebhookParam;
import vip.xiaonuo.biz.modular.webhook.param.WordPressWebhookParam;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Amelia WordPress插件Webhook数据适配器
 * 将Amelia的官方格式转换为系统通用格式
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Component
@Slf4j
public class AmeliaWebhookAdapter {

    private static final DateTimeFormatter AMELIA_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 检测是否为Amelia格式的数据
     */
    public static boolean isAmeliaFormat(String jsonData) {
        try {
            AmeliaWebhookParam param = JSONUtil.toBean(jsonData, AmeliaWebhookParam.class);

            // 检查关键字段是否存在
            return param.getMessage() != null &&
                   param.getData() != null &&
                   param.getData().getType() != null &&
                   (param.getData().getType().equals("appointment") ||
                    param.getData().getType().equals("event") ||
                    param.getData().getType().equals("package"));

        } catch (Exception e) {
            log.debug("数据不是Amelia格式: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 将Amelia格式转换为系统通用格式
     */
    public static WordPressWebhookParam convertToStandard(AmeliaWebhookParam ameliaParam) {
        try {
            WordPressWebhookParam standardParam = new WordPressWebhookParam();

            // 设置基本信息
            standardParam.setAction(determineActionType(ameliaParam));
            standardParam.setTimestamp(System.currentTimeMillis() / 1000);

            // 根据类型处理不同的数据
            String type = ameliaParam.getData().getType();
            switch (type) {
                case "appointment":
                    convertAppointmentData(ameliaParam, standardParam);
                    break;
                case "event":
                    convertEventData(ameliaParam, standardParam);
                    break;
                case "package":
                    convertPackageData(ameliaParam, standardParam);
                    break;
                default:
                    log.warn("未知的Amelia数据类型: {}", type);
                    convertDefaultData(ameliaParam, standardParam);
            }

            log.info("Amelia数据转换完成: {} -> {}", type, standardParam.getAction());
            return standardParam;

        } catch (Exception e) {
            log.error("Amelia数据转换失败", e);
            throw new RuntimeException("数据转换失败: " + e.getMessage());
        }
    }

    /**
     * 确定操作类型
     */
    private static String determineActionType(AmeliaWebhookParam ameliaParam) {
        String message = ameliaParam.getMessage();
        String type = ameliaParam.getData().getType();

        // 根据消息内容判断操作类型
        if (message != null) {
            if (message.contains("Successfully added")) {
                return "create";
            } else if (message.contains("Successfully updated")) {
                return "update";
            } else if (message.contains("canceled")) {
                return "cancel";
            } else if (message.contains("rescheduled")) {
                return "update";
            }
        }

        // 根据状态变化判断
        Boolean statusChanged = ameliaParam.getData().getAppointmentStatusChanged();
        if (statusChanged != null && statusChanged) {
            return "update";
        }

        // 默认为创建操作
        return "create";
    }

    /**
     * 转换预约数据
     */
    private static void convertAppointmentData(AmeliaWebhookParam ameliaParam, WordPressWebhookParam standardParam) {
        AmeliaWebhookParam.AmeliaAppointment appointment = ameliaParam.getData().getAppointment();
        AmeliaWebhookParam.AmeliaBooking booking = ameliaParam.getData().getBooking();

        if (appointment == null) {
            log.warn("预约数据为空");
            return;
        }

        // 设置预约ID
        standardParam.setAppointmentId(appointment.getId() != null ? appointment.getId().toString() : null);

        // 设置状态
        standardParam.setStatus(mapAmeliaStatus(appointment.getStatus()));

        // 设置备注
        if (appointment.getInternalNotes() != null) {
            standardParam.setNotes(appointment.getInternalNotes());
        }

        // 从第一个booking获取客户信息（如果存在）
        AmeliaWebhookParam.AmeliaCustomer ameliaCustomer = null;
        if (booking != null && booking.getCustomer() != null) {
            ameliaCustomer = booking.getCustomer();
        } else if (appointment.getBookings() != null && !appointment.getBookings().isEmpty()) {
            AmeliaWebhookParam.AmeliaBooking firstBooking = appointment.getBookings().get(0);
            if (firstBooking.getCustomer() != null) {
                ameliaCustomer = firstBooking.getCustomer();
            }
        }

        // 设置客户信息
        if (ameliaCustomer != null) {
            WordPressWebhookParam.CustomerInfo customer = new WordPressWebhookParam.CustomerInfo();
            customer.setId(ameliaCustomer.getId() != null ? ameliaCustomer.getId().toString() : null);
            customer.setName(buildFullName(ameliaCustomer.getFirstName(), ameliaCustomer.getLastName()));
            customer.setEmail(ameliaCustomer.getEmail());
            customer.setPhone(ameliaCustomer.getPhone());
            standardParam.setCustomer(customer);
        }

        // 设置员工信息 - 使用员工名称而不是ID进行映射
        if (appointment.getProvider() != null) {
            WordPressWebhookParam.StaffInfo staff = new WordPressWebhookParam.StaffInfo();
            // 重要：使用员工的全名作为ID，这样在映射时可以直接与系统用户账户匹配
            String employeeName = buildFullName(appointment.getProvider().getFirstName(), appointment.getProvider().getLastName());
            staff.setId(employeeName); // 传递员工名称用于映射
            staff.setName(employeeName);
            staff.setEmail(appointment.getProvider().getEmail());
            standardParam.setStaff(staff);
            log.info("设置Amelia员工信息: 员工名[{}] 将用于匹配系统用户账户", employeeName);
        } else if (appointment.getProviderId() != null) {
            // 如果只有ID没有详细信息，尝试通过ID查找员工名称
            WordPressWebhookParam.StaffInfo staff = new WordPressWebhookParam.StaffInfo();
            staff.setId(appointment.getProviderId().toString());
            standardParam.setStaff(staff);
            log.warn("Amelia员工信息不完整，只有ID: {}", appointment.getProviderId());
        }

        // 设置位置信息 - 如果有location信息，添加到metadata中
        if (appointment.getLocationId() != null) {
            // 这里需要根据Amelia的数据结构获取location名称
            // 由于当前数据结构中没有location对象，我们先记录locationId
            log.info("Amelia预约包含位置ID: {}", appointment.getLocationId());
            // 可以在这里添加位置映射逻辑
        }

        // 设置服务信息
        if (appointment.getService() != null) {
            WordPressWebhookParam.ServiceInfo service = new WordPressWebhookParam.ServiceInfo();
            service.setId(appointment.getService().getId() != null ? appointment.getService().getId().toString() : null);
            service.setName(appointment.getService().getName());

            // 智能处理duration：保持原始值，让WebhookController统一处理
            if (appointment.getService().getDuration() != null) {
                Double duration = Double.parseDouble(String.valueOf(appointment.getService().getDuration()));
                service.setDuration(duration); // 保持原始值，不进行转换
                log.info("Amelia服务Duration: {} (保持原始值，由Controller统一处理)", duration);
            }

            service.setPrice(appointment.getService().getPrice() != null ? appointment.getService().getPrice().doubleValue() : null);
            service.setQuantity(1);

            java.util.List<WordPressWebhookParam.ServiceInfo> services = new java.util.ArrayList<>();
            services.add(service);
            standardParam.setServices(services);
        }

        // 设置时间信息
        if (appointment.getBookingStart() != null && appointment.getBookingEnd() != null) {
            WordPressWebhookParam.AppointmentTime appointmentTime = new WordPressWebhookParam.AppointmentTime();
            appointmentTime.setStartTime(appointment.getBookingStart());
            appointmentTime.setEndTime(appointment.getBookingEnd());
            appointmentTime.setTimezone("UTC"); // 默认时区
            standardParam.setAppointmentTime(appointmentTime);
        }

        // 设置支付信息
        if (booking != null && booking.getPayments() != null && !booking.getPayments().isEmpty()) {
            AmeliaWebhookParam.AmeliaPayment ameliaPayment = booking.getPayments().get(0);
            WordPressWebhookParam.PaymentInfo payment = new WordPressWebhookParam.PaymentInfo();
            payment.setTotalAmount(ameliaPayment.getAmount() != null ? ameliaPayment.getAmount().doubleValue() : 0.0);
            payment.setPaymentStatus(ameliaPayment.getStatus());
            payment.setCash(ameliaPayment.getAmount() != null ? ameliaPayment.getAmount().doubleValue() : 0.0);
            payment.setCard(0.0);
            payment.setSurchargeFee(0.0);
            payment.setGiftCard(0.0);
            payment.setVoucher(0.0);
            payment.setInsurance(0.0);
            payment.setTip(0.0);
            standardParam.setPayment(payment);
        }
    }

    /**
     * 转换事件数据
     */
    private static void convertEventData(AmeliaWebhookParam ameliaParam, WordPressWebhookParam standardParam) {
        // 事件数据转换逻辑（简化实现）
        convertDefaultData(ameliaParam, standardParam);
        log.info("处理事件数据");
    }

    /**
     * 转换套餐数据
     */
    private static void convertPackageData(AmeliaWebhookParam ameliaParam, WordPressWebhookParam standardParam) {
        // 套餐数据转换逻辑（简化实现）
        convertDefaultData(ameliaParam, standardParam);
        log.info("处理套餐数据，套餐ID: {}", ameliaParam.getData().getPackageId());
    }

    /**
     * 默认数据转换
     */
    private static void convertDefaultData(AmeliaWebhookParam ameliaParam, WordPressWebhookParam standardParam) {
        // 设置基本的客户信息
        if (ameliaParam.getData().getCustomer() != null) {
            AmeliaWebhookParam.AmeliaCustomer ameliaCustomer = ameliaParam.getData().getCustomer();
            WordPressWebhookParam.CustomerInfo customer = new WordPressWebhookParam.CustomerInfo();
            customer.setId(ameliaCustomer.getId() != null ? ameliaCustomer.getId().toString() : null);
            customer.setName(buildFullName(ameliaCustomer.getFirstName(), ameliaCustomer.getLastName()));
            customer.setEmail(ameliaCustomer.getEmail());
            customer.setPhone(ameliaCustomer.getPhone());
            standardParam.setCustomer(customer);
        }

        // 设置基本状态
        standardParam.setStatus("pending");
        standardParam.setAppointmentId("unknown");
    }

    /**
     * 解析日期时间
     */
    private static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            return LocalDateTime.parse(dateTimeStr, AMELIA_DATE_FORMAT);
        } catch (Exception e) {
            log.warn("日期时间解析失败: {}", dateTimeStr);
            return null;
        }
    }

    /**
     * 映射Amelia状态到系统状态
     */
    private static String mapAmeliaStatus(String ameliaStatus) {
        if (ameliaStatus == null) {
            return "pending";
        }

        switch (ameliaStatus.toLowerCase()) {
            case "approved":
                return "confirmed";
            case "pending":
                return "pending";
            case "canceled":
            case "cancelled":
                return "cancelled";
            case "rejected":
                return "rejected";
            default:
                log.warn("未知的Amelia状态: {}", ameliaStatus);
                return ameliaStatus;
        }
    }

    /**
     * 构建全名
     */
    private static String buildFullName(String firstName, String lastName) {
        StringBuilder fullName = new StringBuilder();
        if (firstName != null && !firstName.trim().isEmpty()) {
            fullName.append(firstName.trim());
        }
        if (lastName != null && !lastName.trim().isEmpty()) {
            if (fullName.length() > 0) {
                fullName.append(" ");
            }
            fullName.append(lastName.trim());
        }
        return fullName.length() > 0 ? fullName.toString() : "Unknown";
    }
}
