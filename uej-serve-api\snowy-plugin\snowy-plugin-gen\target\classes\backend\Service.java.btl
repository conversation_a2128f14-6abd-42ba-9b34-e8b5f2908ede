
package ${packageName}.${moduleName}.modular.${busName}.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import ${packageName}.${moduleName}.modular.${busName}.entity.${className};
import ${packageName}.${moduleName}.modular.${busName}.param.${className}AddParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}EditParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}IdParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}PageParam;
<% if (dfcWhether == 'Y') { %>
import cn.hutool.json.JSONObject;
<% } %>

import java.util.List;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${genTime}
 **/
public interface ${className}Service extends IService<${className}> {

    /**
     * 获取${functionName}分页
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    Page<${className}> page(${className}PageParam ${classNameFirstLower}PageParam);

    /**
     * 添加${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    void add(${className}AddParam ${classNameFirstLower}AddParam);

    /**
     * 编辑${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    void edit(${className}EditParam ${classNameFirstLower}EditParam);

    /**
     * 删除${functionName}
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    void delete(List<${className}IdParam> ${classNameFirstLower}IdParamList);

    /**
     * 获取${functionName}详情
     *
     * <AUTHOR>
     * @date ${genTime}
     */
    ${className} detail(${className}IdParam ${classNameFirstLower}IdParam);

    /**
     * 获取${functionName}详情
     *
     * <AUTHOR>
     * @date ${genTime}
     **/
    ${className} queryEntity(String id);

    <% if (dfcWhether == 'Y') { %>
    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date ${genTime}
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
    <% } %>
}
