
package vip.xiaonuo.biz.modular.offeringgroup.controller;

import camundajar.impl.scala.Int;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.offering.entity.BizOffering;
import vip.xiaonuo.biz.modular.offeringgroupitem.entity.BizOfferingGroupItem;
import vip.xiaonuo.biz.modular.offeringgroupitem.service.BizOfferingGroupItemService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.offeringgroup.entity.BizOfferingGroup;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupAddParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupEditParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupIdParam;
import vip.xiaonuo.biz.modular.offeringgroup.param.BizOfferingGroupPageParam;
import vip.xiaonuo.biz.modular.offeringgroup.service.BizOfferingGroupService;
import cn.hutool.json.JSONObject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 服务或产品组合控制器
 *
 * <AUTHOR>
 * @date  2024/06/12 16:57
 */
@Tag(name = "服务或产品组合控制器")
@RestController
@Validated
public class BizOfferingGroupController {

    @Resource
    private BizOfferingGroupService bizOfferingGroupService;

    @Resource
    private BizOfferingGroupItemService bizOfferingGroupItemService;

    /**
     * 获取服务或产品组合分页
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    @Operation(summary = "获取服务或产品组合分页")
    @SaCheckPermission("/biz/offeringgroup/page")
    @GetMapping("/biz/offeringgroup/page")
    public CommonResult<Page<BizOfferingGroup>> page(BizOfferingGroupPageParam bizOfferingGroupPageParam) {
        return CommonResult.data(bizOfferingGroupService.page(bizOfferingGroupPageParam));
    }

    /**
     * 添加服务或产品组合
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    @Operation(summary = "添加服务或产品组合")
    @CommonLog("添加服务或产品组合")
    @SaCheckPermission("/biz/offeringgroup/add")
    @PostMapping("/biz/offeringgroup/add")
    public CommonResult<String> add(@RequestBody @Valid BizOfferingGroup bizOfferingGroup) {
        bizOfferingGroupService.save(bizOfferingGroup);

        List<BizOfferingGroupItem> itemList = new ArrayList<>();
        for (BizOfferingGroupItem offering : bizOfferingGroup.getOfferings()) {
            BizOfferingGroupItem offeringGroupItem = new BizOfferingGroupItem();
            offeringGroupItem.setMainId(bizOfferingGroup.getId());
            offeringGroupItem.setOfferingId(offering.getId());
            offeringGroupItem.setOfferingName(offering.getOfferingName());
            offeringGroupItem.setListPrice(offering.getListPrice());
            offeringGroupItem.setPlanningHours(offering.getPlanningHours());
            offeringGroupItem.setTotalPrice(offering.getTotalPrice());
            offeringGroupItem.setNum(offering.getNum());
            itemList.add(offeringGroupItem);
        }
        bizOfferingGroupItemService.saveBatch(itemList);
        return CommonResult.ok();
    }

    /**
     * 编辑服务或产品组合
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    @Operation(summary = "编辑服务或产品组合")
    @CommonLog("编辑服务或产品组合")
    @SaCheckPermission("/biz/offeringgroup/edit")
    @PostMapping("/biz/offeringgroup/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizOfferingGroupEditParam bizOfferingGroupEditParam) {
        bizOfferingGroupService.edit(bizOfferingGroupEditParam);
        List<BizOfferingGroupItem> itemList = new ArrayList<>();
        for (BizOfferingGroupItem offering : bizOfferingGroupEditParam.getOfferings()) {
            BizOfferingGroupItem offeringGroupItem = new BizOfferingGroupItem();
            offeringGroupItem.setMainId(bizOfferingGroupEditParam.getId());
            offeringGroupItem.setOfferingId(offering.getId());
            offeringGroupItem.setOfferingName(offering.getOfferingName());
            offeringGroupItem.setListPrice(offering.getListPrice());
            offeringGroupItem.setPlanningHours(offering.getPlanningHours());
            offeringGroupItem.setTotalPrice(offering.getTotalPrice());
            offeringGroupItem.setNum(offering.getNum());
            itemList.add(offeringGroupItem);
        }
        bizOfferingGroupItemService.remove(new QueryWrapper<BizOfferingGroupItem>().lambda().eq(BizOfferingGroupItem::getMainId, bizOfferingGroupEditParam.getId()));
        bizOfferingGroupItemService.saveBatch(itemList);
        return CommonResult.ok();
    }

    /**
     * 删除服务或产品组合
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    @Operation(summary = "删除服务或产品组合")
    @CommonLog("删除服务或产品组合")
    @SaCheckPermission("/biz/offeringgroup/delete")
    @PostMapping("/biz/offeringgroup/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizOfferingGroupIdParam> bizOfferingGroupIdParamList) {
        bizOfferingGroupService.delete(bizOfferingGroupIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取服务或产品组合详情
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    @Operation(summary = "获取服务或产品组合详情")
    @SaCheckPermission("/biz/offeringgroup/detail")
    @GetMapping("/biz/offeringgroup/detail")
    public CommonResult<BizOfferingGroup> detail(@Valid BizOfferingGroupIdParam bizOfferingGroupIdParam) {
        BizOfferingGroup detail = bizOfferingGroupService.detail(bizOfferingGroupIdParam);
        List<BizOfferingGroupItem> list = bizOfferingGroupItemService.list(new QueryWrapper<BizOfferingGroupItem>().lambda().eq(BizOfferingGroupItem::getMainId, detail.getId())).stream().toList();
        detail.setOfferings(list);
        return CommonResult.data(detail);
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/12 16:57
     */
    @Operation(summary = "获取服务或产品组合动态字段的配置")
    @SaCheckPermission("/biz/offeringgroup/dynamicFieldConfigList")
    @GetMapping("/biz/offeringgroup/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizOfferingGroupService.dynamicFieldConfigList(columnName));
    }
}
