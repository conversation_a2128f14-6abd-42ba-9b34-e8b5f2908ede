
package vip.xiaonuo.biz.modular.taskdatastatistics.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 任务数据统计编辑参数
 *
 * <AUTHOR>
 * @date  2024/06/13 15:48
 **/
@Getter
@Setter
public class BizTaskDataStatisticsEditParam {

    /** ID */
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 主表ID(task表) */
    @Schema(description = "主表ID(task表)")
    private String mainId;

    /** Status */
    @Schema(description = "Status")
    private String status;

    /** Start Time */
    @Schema(description = "Start Time")
    private String startTime;

    @Schema(description = "Pause Time")
    private String pauseTime;

    /** Planning Hours */
    @Schema(description = "Planning Hours")
    private String planningHours;

    private String extJson;

    @Schema(description = "会员id")
    private String customerId;

    @Schema(description = "会员姓名")
    private String customerName;
}
