
export const required = (message, trigger = ['blur', 'change']) => ({
	required: true,
	message,
	trigger
})

// 常用正则规则大全：https://any86.github.io/any-rule/

export const rules = {
	phone: {
		pattern: /^04\d{8}$/,
		message: 'Please enter a mobile phone number with 10 digits starting with 04',
		trigger: 'blur'
	},
	email: {
		pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
		message: 'Please fill in the correct email number',
		trigger: 'blur'
	},
	idCard: {
		pattern:
			/(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/,
		message: 'Please fill in the required ID number',
		trigger: 'blur'
	},
	lettersNum: {
		pattern: /^[A-Za-z0-9]+$/,
		message: 'The content must be composed of letters or numbers',
		trigger: 'blur'
	},
	number: {
		pattern: /^\d{1,}$/,
		message: 'The field must be numeric only',
		trigger: 'blur'
	},
	price: {
		pattern: /(?:^[1-9]([0-9]+)?(?:\.[0-9]{1,2})?$)|(?:^(?:0)$)|(?:^[0-9]\.[0-9](?:[0-9])?$)/,
		message: 'Only positive amounts are supported',
		trigger: 'blur'
	}
}
