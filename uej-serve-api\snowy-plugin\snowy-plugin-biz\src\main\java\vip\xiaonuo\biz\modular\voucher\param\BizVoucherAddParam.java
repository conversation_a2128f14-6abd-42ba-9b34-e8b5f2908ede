
package vip.xiaonuo.biz.modular.voucher.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 代金券信息添加参数
 *
 * <AUTHOR>
 * @date  2024/06/12 17:17
 **/
@Getter
@Setter
public class BizVoucherAddParam {
    private String id;

    private String pid;

    /** Voucher Number */
    @Schema(description = "Voucher Number")
    private String voucherNumber;

    /** Create Date */
    @Schema(description = "Create Date")
    private String createDate;

    /** Used Date */
    @Schema(description = "Used Date")
    private String usedDate;

    /** Value */
    @Schema(description = "Value")
    private String value;

    private String validityDate;

    private String threshold;

    private String status;

    private String deleteFlag;

    private String tenantId;

    private String extJson;

    private String orgId;

    private String taskId;
}
