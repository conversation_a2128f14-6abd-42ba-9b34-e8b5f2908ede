package vip.xiaonuo.biz.modular.productcategory.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 产品类目添加参数
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Getter
@Setter
public class BizProductCategoryAddParam {

    /** 父id */
    @Schema(description = "父id")
    @NotBlank(message = "Parent ID cannot be empty")
    private String parentId;

    /** 类目名称 */
    @Schema(description = "类目名称")
    @NotBlank(message = "Category name cannot be empty")
    private String dictLabel;

    /** 类目编码 */
    @Schema(description = "类目编码")
    @NotBlank(message = "Category code cannot be empty")
    private String dictValue;

    /** 排序码 */
    @Schema(description = "排序码")
    @NotNull(message = "Sort code cannot be empty")
    private Integer sortCode;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;
} 