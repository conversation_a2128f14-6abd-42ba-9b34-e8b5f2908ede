
package vip.xiaonuo.biz.modular.taskdatastatistics.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springdoc.core.parsers.ReturnTypeParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;
import vip.xiaonuo.biz.modular.config.service.BizConfigService;
import vip.xiaonuo.biz.modular.devConfig.config.entity.BizDevConfig;
import vip.xiaonuo.biz.modular.devConfig.config.service.BizDevConfigService;
import vip.xiaonuo.biz.modular.org.entity.BizOrg;
import vip.xiaonuo.biz.modular.org.service.BizOrgService;
import vip.xiaonuo.biz.modular.schedule.service.BizScheduleService;
import vip.xiaonuo.biz.modular.scheduledate.service.BizScheduleDateService;
import vip.xiaonuo.biz.modular.task.entity.BizTask;
import vip.xiaonuo.biz.modular.task.service.BizTaskService;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;
import vip.xiaonuo.biz.modular.taskitem.service.BizTaskItemService;
import vip.xiaonuo.biz.modular.user.entity.BizUser;
import vip.xiaonuo.biz.modular.user.service.BizUserService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.taskdatastatistics.entity.BizTaskDataStatistics;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsAddParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsEditParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsIdParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.param.BizTaskDataStatisticsPageParam;
import vip.xiaonuo.biz.modular.taskdatastatistics.service.BizTaskDataStatisticsService;
import cn.hutool.json.JSONObject;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import vip.xiaonuo.dev.api.DevSseApi;
import vip.xiaonuo.flw.modular.template.service.FlwTemplateSnService;
import vip.xiaonuo.sys.modular.index.service.SysIndexService;
import vip.xiaonuo.sys.modular.user.entity.SysUser;
import vip.xiaonuo.sys.modular.user.service.SysUserService;

import java.util.List;
import java.util.concurrent.*;

/**
 * 任务数据统计控制器
 *
 * <AUTHOR>
 * @date  2024/06/13 15:48
 */
@Tag(name = "任务数据统计控制器")
@RestController
@Validated
public class BizTaskDataStatisticsController {

    private static final ScheduledExecutorService heartbeatExecutors = Executors.newScheduledThreadPool(10);

    @Resource
    private BizTaskDataStatisticsService bizTaskDataStatisticsService;

    @Resource
    private BizTaskService bizTaskService;

    @Resource
    private SysIndexService sysIndexService;

    @Resource
    private BizTaskItemService bizTaskItemService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private DevSseApi devSseApi;

    @Resource
    private BizScheduleService bizScheduleService;

    @Resource
    private BizScheduleDateService bizScheduleDateService;

    @Resource
    private BizConfigService bizConfigService;
    @Qualifier("genericReturnTypeParser")
    @Autowired
    private ReturnTypeParser genericReturnTypeParser;

    @Resource
    private BizOrgService bizOrgService;

    @Resource
    private BizUserService bizUserService;

    @Resource
    private FlwTemplateSnService flwTemplateSnService;

    @Resource
    private BizDevConfigService devConfigService;

    /**
     * 获取任务数据统计分页
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    @Operation(summary = "获取任务数据统计分页")
    @SaCheckPermission("/biz/taskdatastatistics/page")
    @GetMapping("/biz/taskdatastatistics/page")
    public CommonResult<Page<BizTaskDataStatistics>> page(BizTaskDataStatisticsPageParam bizTaskDataStatisticsPageParam) {
        return CommonResult.data(bizTaskDataStatisticsService.page(bizTaskDataStatisticsPageParam));
    }

    /**
     * 添加任务数据统计
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    @Operation(summary = "添加任务数据统计")
    @CommonLog("添加任务数据统计")
    @SaCheckPermission("/biz/taskdatastatistics/add")
    @PostMapping("/biz/taskdatastatistics/add")
    public CommonResult<String> add(@RequestBody @Valid BizTaskDataStatisticsAddParam bizTaskDataStatisticsAddParam) {
        bizTaskDataStatisticsService.add(bizTaskDataStatisticsAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑任务数据统计
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    @Operation(summary = "编辑任务数据统计")
    @CommonLog("编辑任务数据统计")
    @SaCheckPermission("/biz/taskdatastatistics/edit")
    @PostMapping("/biz/taskdatastatistics/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizTaskDataStatisticsEditParam bizTaskDataStatisticsEditParam) {
        bizTaskDataStatisticsService.edit(bizTaskDataStatisticsEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除任务数据统计
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    @Operation(summary = "删除任务数据统计")
    @CommonLog("删除任务数据统计")
    @SaCheckPermission("/biz/taskdatastatistics/delete")
    @PostMapping("/biz/taskdatastatistics/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizTaskDataStatisticsIdParam> bizTaskDataStatisticsIdParamList) {
        String id = bizTaskDataStatisticsIdParamList.get(0).getId();
        String mainId = bizTaskDataStatisticsService.queryEntity(id).getMainId();
        List<BizTaskDataStatistics> statistics = bizTaskDataStatisticsService.lambdaQuery().eq(BizTaskDataStatistics::getMainId, mainId).list();
        boolean b = statistics.stream().filter(o->o.getParentId()!=null).anyMatch(o -> o.getStatus().equals("TASK_STATE_PROCESS") || o.getStatus().equals("TASK_STATE_NEW") || o.getStatus().equals("TASK_STATE_PAID"));
        if (!b) {
            BizTaskDataStatistics taskStatisticsMain = statistics.get(0);
            taskStatisticsMain.setStatus("TASK_STATE_FINISH");
            bizTaskDataStatisticsService.updateById(taskStatisticsMain);
        }
        bizTaskDataStatisticsService.delete(bizTaskDataStatisticsIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取任务数据统计详情
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    @Operation(summary = "获取任务数据统计详情")
    @SaCheckPermission("/biz/taskdatastatistics/detail")
    @GetMapping("/biz/taskdatastatistics/detail")
    public CommonResult<BizTaskDataStatistics> detail(@Valid BizTaskDataStatisticsIdParam bizTaskDataStatisticsIdParam) {
        return CommonResult.data(bizTaskDataStatisticsService.detail(bizTaskDataStatisticsIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/06/13 15:48
     */
    @Operation(summary = "获取任务数据统计动态字段的配置")
    @SaCheckPermission("/biz/taskdatastatistics/dynamicFieldConfigList")
    @GetMapping("/biz/taskdatastatistics/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizTaskDataStatisticsService.dynamicFieldConfigList(columnName));
    }

    @Operation(summary = "任务超时提醒")
    @GetMapping("/biz/taskdatastatistics/remind")
    public CommonResult createSseConnect() {
        BizDevConfig remind = devConfigService.lambdaQuery().eq(BizDevConfig::getConfigKey, "REMIND").eq(BizDevConfig::getCategory, "BIZ_DEFINE").one();
        if (ObjectUtil.isEmpty(remind) || remind.getConfigValue().equals("false")) {
            return CommonResult.data(null);
        }
        String id = StpLoginUserUtil.getLoginUser().getId();
        List<String> ids = new ArrayList<>();
        ids.add(id);
        List<String> orgId = bizOrgService.lambdaQuery().eq(BizOrg::getDirectorId, id).list().stream().map(BizOrg::getId).toList();
        if (!orgId.isEmpty()) {
            List<String> idsFromOrg = bizUserService.lambdaQuery().in(BizUser::getOrgId, orgId).list().stream().map(BizUser::getId).toList();
            ids.addAll(idsFromOrg);
        }
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        //超时任务修改状态为remind
        List<BizTaskDataStatistics> taskRemind = bizTaskDataStatisticsService.lambdaQuery()
                .ne(BizTaskDataStatistics::getStatus, "TASK_STATE_FINISH")
                .ne(BizTaskDataStatistics::getStatus, "TASK_STATE_STOP")
                .isNotNull(BizTaskDataStatistics::getParentId)
                .lt(BizTaskDataStatistics::getPlanningEndTime, date)
                .like(BizTaskDataStatistics::getStartTime, DateUtil.format(new Date(), "yyyy-MM-dd")).list();
        List<BizTask> mainTaskReminds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(taskRemind)) {
            for (BizTaskDataStatistics taskDataStatistics : taskRemind) {
                taskDataStatistics.setStatus("TASK_STATE_REMIND");
                BizTask mainTaskRemind = new BizTask();
                mainTaskRemind.setId(taskDataStatistics.getMainId());
                mainTaskRemind.setState("TASK_STATE_REMIND");
                mainTaskReminds.add(mainTaskRemind);
            }
            bizTaskDataStatisticsService.updateBatchById(taskRemind);
            bizTaskService.updateBatchById(mainTaskReminds);
        }

        List<BizTaskDataStatistics> result = bizTaskDataStatisticsService.lambdaQuery()
                .eq(BizTaskDataStatistics::getStatus, "TASK_STATE_REMIND")
                .in(BizTaskDataStatistics::getStaffId, ids)
                //.isNull(BizTaskDataStatistics::getParentId)
                .like(BizTaskDataStatistics::getStartTime, DateUtil.format(new Date(), "yyyy-MM-dd"))
                .list().stream().map(obj -> {
            BizTaskDataStatistics taskTimeout = new BizTaskDataStatistics();
            taskTimeout.setMainId(obj.getMainId());
            taskTimeout.setTaskName(obj.getTaskName());
            taskTimeout.setStaff(obj.getStaff());
            taskTimeout.setPlanningEndTime(obj.getPlanningEndTime());
            return taskTimeout;
        }).toList();
        return CommonResult.data(result);
    }

    @Operation(summary = "获取甘特图信息")
    @GetMapping("/biz/taskdatastatistics/getGantt")
    public CommonResult<List<BizTaskDataStatistics>> getGantt(String date, String orgId) {
        List<BizTaskDataStatistics> tasks = bizTaskDataStatisticsService.lambdaQuery().eq(BizTaskDataStatistics::getOrgId, orgId).like(BizTaskDataStatistics::getStartTime, date).isNotNull(BizTaskDataStatistics::getParentId).orderByAsc(BizTaskDataStatistics::getStaffId).list();
        List<BizTaskDataStatistics> Unregistered = bizTaskDataStatisticsService.lambdaQuery().eq(BizTaskDataStatistics::getOrgId, "Unregistered").like(BizTaskDataStatistics::getStartTime, date).isNotNull(BizTaskDataStatistics::getParentId).orderByAsc(BizTaskDataStatistics::getStaffId).list();
        tasks.addAll(Unregistered);
        if (ObjectUtil.isEmpty(tasks)) {
            BizTaskDataStatistics result = new BizTaskDataStatistics();
            result.setPaidAll(BigDecimal.ZERO);
            result.setPaid(BigDecimal.ZERO);
            result.setId("Unregistered");
            tasks.add(result);
            return CommonResult.data(null);
        }
        List<BizTask> bizTasks = bizTaskService.lambdaQuery().eq(BizTask::getOrgId, orgId).like(BizTask::getStartTime, date).list();
        List<BizTask> bizTaskUnregistered = bizTaskService.lambdaQuery().eq(BizTask::getOrgId, "Unregistered").like(BizTask::getStartTime, date).list();
        bizTasks.addAll(bizTaskUnregistered);
        BigDecimal paidAll = new BigDecimal("0");
        for (BizTaskDataStatistics task : tasks) {
            for (BizTask bizTask : bizTasks) {
                if (task.getMainId().equals(bizTask.getId())) {
                    task.setVoucher(bizTask.getVoucher());
                    task.setGiftCard(bizTask.getGiftCard());
                    task.setCash(bizTask.getCash());
                    task.setCard(bizTask.getCard());
                    task.setTip(bizTask.getTip());
                    task.setInsurance(bizTask.getInsuranceValue());
                    BigDecimal paid = Convert.toBigDecimal(bizTask.getVoucher())
                            .add(Convert.toBigDecimal(bizTask.getGiftCard()))
                            .add(Convert.toBigDecimal(bizTask.getCash()))
                            .add(Convert.toBigDecimal(bizTask.getCard()))
                            .add(Convert.toBigDecimal(bizTask.getInsuranceValue()));
                    task.setPaid(paid);
                    paidAll = paidAll.add(paid);
                }
            }
        }
        tasks.get(0).setPaidAll(paidAll);
        return CommonResult.data(tasks);
    }

    @Operation(summary = "修改任务状态，点继续会新增一条任务信息")
    @PostMapping("/biz/taskdatastatistics/statusChange")
    public CommonResult<String> statusChange(@RequestBody BizTaskDataStatistics bizTaskDataStatistics) {
        String status = bizTaskDataStatistics.getStatus();
        List<BizTaskDataStatistics> tasks = bizTaskDataStatisticsService.lambdaQuery().eq(BizTaskDataStatistics::getMainId, bizTaskDataStatistics.getMainId()).list();
        if (tasks.get(tasks.size() - 1).getStatus().equals(bizTaskDataStatistics.getStatus())
                || tasks.get(tasks.size() - 1).getStatus().equals("TASK_STATE_FINISH") && bizTaskDataStatistics.getStatus().equals("TASK_STATE_STOP")
                || tasks.get(0).getStatus().equals("TASK_STATE_FINISH")) {
            // 最后一级子任务状态与更改状态相同，或者最后一级子任务状态为完成且更改状态为暂停，或者主任务已完成
            return CommonResult.error("The task has been completed, paused or in progress");
        }
        BizTaskDataStatistics taskMain = tasks.get(0);
        BizTaskDataStatistics lastTask = tasks.get(tasks.size() - 1);
        String nowTime = DateUtil.now();
        if (status.equals("TASK_STATE_STOP") || status.equals("TASK_STATE_FINISH")) {
            Double actualHours = getHours(lastTask.getStartTime(), nowTime);
            //Double totalActualHours = actualHours + tasks.stream().filter(o -> ObjectUtil.isNotEmpty(o.getActualHours())).mapToDouble(o -> Double.parseDouble(o.getActualHours())).sum();
            Double totalActualHours = actualHours + Convert.toDouble(taskMain.getActualHours());

            lastTask.setPlanningEndTime(nowTime);
            List<BizTaskDataStatistics> taskNew = new ArrayList<>();
            lastTask.setPauseTime(nowTime);
            lastTask.setPlanningHours(null);
            lastTask.setStatus("TASK_STATE_STOP");
            lastTask.setProgress("1"); //子任务已完成
            lastTask.setStaffId(taskMain.getStaffId());
            taskNew.add(lastTask);
            taskMain.setStatus(status);
            //taskMain.setProgress(String.format("%.3f", getProgress(actualHours + Double.parseDouble(taskMain.getActualHours()), Double.parseDouble(taskMain.getPlanningHours())))); // 累计工时除以计划工时
            //taskMain.setActualHours(String.format("%.3f", actualHours + Double.parseDouble(taskMain.getActualHours()))); // 实际工时，包含暂停时间
            taskMain.setActualHours(String.format("%.3f", totalActualHours)); // 实际工时，不包含暂停时间
            taskNew.add(taskMain);
            bizTaskDataStatisticsService.updateBatchById(taskNew);
        }
        if (status.equals("TASK_STATE_PROGRESS")) {
            bizTaskDataStatistics.setStartTime(nowTime);
            bizTaskDataStatistics.setPlanningEndTime(getEndTime(nowTime, taskMain.getPlanningHours()));
            bizTaskDataStatistics.setPlanningHours(null);
            bizTaskDataStatistics.setStatus("TASK_STATE_PROGRESS");
            bizTaskDataStatistics.setParentId(taskMain.getId());
            bizTaskDataStatistics.setId(null);
            bizTaskDataStatistics.setStaffId(taskMain.getStaffId());
            bizTaskDataStatistics.setStaff(taskMain.getStaff());
            bizTaskDataStatistics.setOrgId(taskMain.getOrgId());
            bizTaskDataStatistics.setTaskName(taskMain.getTaskName());
            bizTaskDataStatistics.setProduct(taskMain.getProduct());
            bizTaskDataStatisticsService.save(bizTaskDataStatistics);
            taskMain.setStatus("TASK_STATE_PROGRESS");
            lastTask.setStatus("TASK_STATE_FINISH");
            List<BizTaskDataStatistics> taskStatistics = new ArrayList<>();
            taskStatistics.add(taskMain);
            taskStatistics.add(lastTask);
            bizTaskDataStatisticsService.updateBatchById(taskStatistics);
        }
        // 更新任务主表状态
        BizTask bizTask = bizTaskService.getOne(new QueryWrapper<BizTask>().lambda().eq(BizTask::getId, bizTaskDataStatistics.getMainId()));
        bizTask.setState(status);
        bizTaskService.updateById(bizTask);
        return CommonResult.ok();
    }


    @Operation(summary = "获取该任务包含的产品或服务")
    @GetMapping("/biz/taskdatastatistics/getOfferingByTaskStatisticId")
    public CommonResult<List<BizTaskItem>> getOfferingByTaskStatisticId(@Valid String mainId) {
        return CommonResult.data(bizTaskItemService.list(new QueryWrapper<BizTaskItem>().lambda().eq(BizTaskItem::getMainId, mainId)));
    }

    @Operation(summary = "转接任务")
    @PostMapping("/biz/taskdatastatistics/taskReassign")
    public CommonResult<String> taskReassign(@RequestBody BizTaskDataStatistics taskStatistics) {
        List<BizTaskDataStatistics> list = bizTaskDataStatisticsService.list(new QueryWrapper<BizTaskDataStatistics>().lambda().eq(BizTaskDataStatistics::getMainId, taskStatistics.getMainId()));
        BizTaskDataStatistics taskStatisticsMain = list.get(0);
        BizTaskDataStatistics taskStatisticsOld = list.get(list.size() - 1);
        if (taskStatisticsOld.getStatus().equals("TASK_STATE_PROGRESS")) {
            return CommonResult.error("Please pause this task before reassigning");
        }
        if (taskStatisticsMain.getStatus().equals("TASK_STATE_FINISH")) {
            return CommonResult.error("This task has already been finished");
        }
        SysUser user = sysUserService.queryEntity(taskStatistics.getStaffId());
        String staffName = user.getName();
        String orgId = user.getOrgId();
        List<BizTaskItem> products = taskStatistics.getBizTaskItemList();

        // 修改旧的任务统计
        for (BizTaskDataStatistics taskDataStatistics : list) {
            taskDataStatistics.setStatus("TASK_STATE_FINISH");
        }
        bizTaskDataStatisticsService.updateBatchById(list);

        // 结束旧任务，新增新任务
        BizTask task = bizTaskService.queryEntity(taskStatistics.getMainId());
        BizTask taskNew = new BizTask();
        task.setState("TASK_STATE_FINISH");
        bizTaskService.updateById(task);
        BeanUtil.copyProperties(task, taskNew);
        taskNew.setState("TASK_STATE_PROGRESS");
        taskNew.setTotalPlanHours(taskStatistics.getPlanningHours()); //todo
        taskNew.setId(null);
        taskNew.setItemName(getTaskName(products.get(0).getProductId()));
        taskNew.setAssign(taskStatistics.getStaffId());
        taskNew.setStaff(staffName);
        taskNew.setOrgId(orgId);
        bizTaskService.save(taskNew);


        // 新增任务统计
        Long planHours = 0L;
        for (BizTaskItem taskItem : products) {
            String hour = taskItem.getPlanningHours().split(":")[0];
            String minute = taskItem.getPlanningHours().split(":")[1];
            long totalHours = Long.parseLong(hour) + Long.parseLong(minute) / 60;
            planHours += totalHours;
        }
        LocalDateTime start = LocalDateTime.parse(taskStatistics.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        BizTaskDataStatistics taskStatisticsNew = new BizTaskDataStatistics();
        BizTaskDataStatistics taskStatisticsNewMain = new BizTaskDataStatistics();
        taskStatisticsNewMain.setStatus("TASK_STATE_PROGRESS");
        taskStatisticsNewMain.setMainId(taskNew.getId());
        taskStatisticsNewMain.setTaskName(taskNew.getItemName());
        taskStatisticsNewMain.setStartTime(taskStatistics.getStartTime());
        taskStatisticsNewMain.setStaffId(taskStatistics.getStaffId());
        taskStatisticsNewMain.setStaff(staffName);
        taskStatisticsNewMain.setOrgId(orgId);
        taskStatisticsNewMain.setActualHours("0");
        taskStatisticsNewMain.setPlanningHours(planHours.toString());
        taskStatisticsNewMain.setPlanningEndTime(start.plus(planHours, ChronoUnit.HOURS).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        List<BizTaskItem> bizTaskItemList = products.stream().map(obj -> {
            BizTaskItem product = new BizTaskItem();
            product.setProductId(obj.getProductId());
            product.setProductName(obj.getProductName());
            product.setListPrice(obj.getListPrice());
            product.setPlanningHours(obj.getPlanningHours());
            product.setType(obj.getType());
            return product;
        }).toList();
        taskStatisticsNewMain.setProduct(JSON.toJSONString(bizTaskItemList));
        BeanUtil.copyProperties(taskStatisticsNewMain, taskStatisticsNew);
        bizTaskDataStatisticsService.save(taskStatisticsNewMain);
        taskStatisticsNew.setParentId(taskStatisticsNewMain.getId());
        bizTaskDataStatisticsService.save(taskStatisticsNew);


        // 删除子任务，新增新的
        bizTaskItemService.remove(new LambdaQueryWrapper<BizTaskItem>().in(BizTaskItem::getProductId, bizTaskItemList.stream().map(BizTaskItem::getProductId).toList()));
        for (BizTaskItem product : products) {
            product.setMainId(taskNew.getId());
            product.setId(null);
        }
        bizTaskItemService.saveBatch(products);
        return CommonResult.ok();
    }

        /** 计算主任务进度 */
    private static Double getProgress(Double actualHours, Double planningHours) {
        if (planningHours == 0 || planningHours == null) {
            return 0.0;
        }
        return actualHours / planningHours;
    }

    /**
     * 计算时间差，返回小时
     */
    private static Double getHours(String startTime, String endTime) {
        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        long seconds = Duration.between(start, end).toSeconds();
        return seconds / 3600.0;
    }

    /**
     * 计算结束时间
     */
    private static String getEndTime(String startTime, String hour) {
        LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime endTime = start.plus(Long.parseLong(hour), ChronoUnit.HOURS);
        return endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private String getTaskName(String productId) {
        String taskName = flwTemplateSnService.getTemplateValueByCode("FLJpIOdlIX");
        if (taskName == null) {
            return new Date().getTime() + "";
        }
        // 去除"TASK"前缀以及年份，在月日与序号中间拼接第一个产品号
        return new StringBuffer(taskName.substring(8)).insert(4, "-" + productId + "-").toString();
    }
}
