
package vip.xiaonuo.biz.modular.attendance.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤记录实体
 *
 * <AUTHOR>
 * @date  2024/08/01 13:54
 **/
@Getter
@Setter
@TableName("biz_attendance")
public class BizAttendance {

    /** 主键 */
    @TableId
    @Schema(description = "主键")
    private String id;

    /** Staff ID */
    @Schema(description = "Staff ID")
    private String staffId;

    /** Staff Name */
    @Schema(description = "Staff Name")
    private String staffName;

    /** Org ID */
    @Schema(description = "Org ID")
    private String orgId;

    /** Org Name */
    @Schema(description = "Org Name")
    private String orgName;

    /** Work Time */
    @Schema(description = "Work Time")
    private Date workTime;

    /** Closing Time */
    @Schema(description = "Closing Time")
    private Date closingTime;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private String extJson;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 更新用户 */
    @Schema(description = "更新用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;
}
