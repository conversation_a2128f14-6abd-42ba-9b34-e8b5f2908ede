
package vip.xiaonuo.dev.modular.job.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vip.xiaonuo.biz.modular.config.entity.BizConfig;
import vip.xiaonuo.biz.modular.config.service.BizConfigService;
import vip.xiaonuo.biz.modular.schedule.entity.BizSchedule;
import vip.xiaonuo.biz.modular.schedule.service.BizScheduleService;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;
import vip.xiaonuo.dev.modular.config.entity.DevConfig;
import vip.xiaonuo.dev.modular.config.service.DevConfigService;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时器的一个示例
 *
 * <AUTHOR>
 * @date 2022/8/5 15:52
 **/
@Slf4j
@Component
public class DevJobTimerTaskRunner implements CommonTimerTaskRunner {

    @Resource
    private BizScheduleService bizScheduleService;

    @Resource
    private DevConfigService devConfigService;

    private int n = 1;

    @Override
    public void action() {
        System.err.println("我是一个定时任务，正在在被执行第" + n + "次");
        n = n + 1;
        DevConfig copyConfig = devConfigService.lambdaQuery().eq(DevConfig::getConfigKey, "copySchedule").one();
        if (ObjectUtil.isEmpty(copyConfig)) {
            return;
        }
        List<String> orgIds = JSONUtil.toList(copyConfig.getConfigValue(), String.class);
        if (orgIds.isEmpty()) {
            return;
        }

        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        List<BizSchedule> lastWeekSchedules = bizScheduleService.lambdaQuery()
                .in(BizSchedule::getOrganizationId, orgIds)
                .between(BizSchedule::getAssignDate, LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd")).minusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), date)
                .list();
        // 查询到的上周排班，对每天的排班日期加7
        lastWeekSchedules.stream().map(
                        obj -> {
                            obj.setId(null);
                            obj.setAssignDate(LocalDate.parse(obj.getAssignDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                            return obj;
                        })
                .collect(Collectors.toList());
        bizScheduleService.saveBatch(lastWeekSchedules);
    }

    /**
     * 判断当前时间是否是周五23~24
     */
    private static boolean timeToCopy() {
        Calendar now = Calendar.getInstance();
        // 是否为周五
        boolean isFriday = now.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY;
        if (!isFriday) {
            return false;
        }
        // 是否在23:00至23:59之间
        boolean isBetween2200And2359 = now.get(Calendar.HOUR_OF_DAY) >= 23 && now.get(Calendar.HOUR_OF_DAY) < 24
                && now.get(Calendar.MINUTE) >= 0 && now.get(Calendar.MINUTE) <= 59;
        return isBetween2200And2359;
    }
}
