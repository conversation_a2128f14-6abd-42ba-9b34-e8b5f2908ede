package vip.xiaonuo.biz.modular.webhook.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * WordPress映射配置实体
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Getter
@Setter
@TableName("biz_wordpress_mapping")
public class BizWordPressMapping {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** 映射类型：staff（员工映射）、service（服务映射）、customer（客户映射） */
    @Schema(description = "映射类型")
    private String mappingType;

    /** WordPress侧的ID */
    @Schema(description = "WordPress侧的ID")
    private String wordpressId;

    /** WordPress侧的名称 */
    @Schema(description = "WordPress侧的名称")
    private String wordpressName;

    /** 系统侧的ID */
    @Schema(description = "系统侧的ID")
    private String systemId;

    /** 系统侧的名称 */
    @Schema(description = "系统侧的名称")
    private String systemName;

    /** 映射状态：active（激活）、inactive（停用） */
    @Schema(description = "映射状态")
    private String status;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 扩展字段 */
    @Schema(description = "扩展字段")
    private String extJson;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;
} 