package vip.xiaonuo.biz.modular.offeringgroupitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.offeringgroupitem.entity.BizOfferingGroupItem;
import vip.xiaonuo.biz.modular.offeringgroupitem.mapper.BizOfferingGroupItemMapper;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemAddParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemEditParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemIdParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.param.BizOfferingGroupItemPageParam;
import vip.xiaonuo.biz.modular.offeringgroupitem.service.BizOfferingGroupItemService;
import cn.hutool.json.JSONObject;
import cn.hutool.core.annotation.AnnotationUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.annotation.Resource;
import vip.xiaonuo.dbs.api.DbsApi;
import vip.xiaonuo.dev.api.DevDfcApi;
import vip.xiaonuo.auth.core.util.StpLoginUserUtil;

import java.util.List;

/**
 * 套餐明细Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/06/13 09:58
 **/
@Service
public class BizOfferingGroupItemServiceImpl extends ServiceImpl<BizOfferingGroupItemMapper, BizOfferingGroupItem> implements BizOfferingGroupItemService {

    @Resource
    private DbsApi dbsApi;

    @Resource
    private DevDfcApi devDfcApi;

    @Override
    public Page<BizOfferingGroupItem> page(BizOfferingGroupItemPageParam bizOfferingGroupItemPageParam) {
        QueryWrapper<BizOfferingGroupItem> queryWrapper = new QueryWrapper<BizOfferingGroupItem>().checkSqlInjection();
        if(ObjectUtil.isNotEmpty(bizOfferingGroupItemPageParam.getOfferingName())) {
            queryWrapper.lambda().like(BizOfferingGroupItem::getOfferingName, bizOfferingGroupItemPageParam.getOfferingName());
        }
        if(ObjectUtil.isNotEmpty(bizOfferingGroupItemPageParam.getMainId())) {
            queryWrapper.lambda().eq(BizOfferingGroupItem::getMainId, bizOfferingGroupItemPageParam.getMainId());
        }
        if(ObjectUtil.isAllNotEmpty(bizOfferingGroupItemPageParam.getSortField(), bizOfferingGroupItemPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(bizOfferingGroupItemPageParam.getSortOrder());
            queryWrapper.orderBy(true, bizOfferingGroupItemPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(bizOfferingGroupItemPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BizOfferingGroupItem::getId);
        }
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：基于orgId进行权限控制
            queryWrapper.lambda().in(BizOfferingGroupItem::getOrgId, loginUserDataScope);
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                queryWrapper.lambda().eq(BizOfferingGroupItem::getCreateUser, userId);
            } catch (Exception e) {
                // 获取用户信息失败，返回空结果
                queryWrapper.lambda().eq(BizOfferingGroupItem::getId, "IMPOSSIBLE_ID");
            }
        }
        
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BizOfferingGroupItemAddParam bizOfferingGroupItemAddParam) {
        BizOfferingGroupItem bizOfferingGroupItem = BeanUtil.toBean(bizOfferingGroupItemAddParam, BizOfferingGroupItem.class);
        
        // 自动设置orgId为当前登录用户的orgId
        if(ObjectUtil.isEmpty(bizOfferingGroupItem.getOrgId())) {
            try {
                String currentUserOrgId = StpLoginUserUtil.getLoginUser().getOrgId();
                if(ObjectUtil.isNotEmpty(currentUserOrgId)) {
                    bizOfferingGroupItem.setOrgId(currentUserOrgId);
                }
            } catch (Exception e) {
                // 获取用户信息失败，继续执行但不设置orgId
            }
        }
        
        this.save(bizOfferingGroupItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BizOfferingGroupItemEditParam bizOfferingGroupItemEditParam) {
        BizOfferingGroupItem bizOfferingGroupItem = this.queryEntity(bizOfferingGroupItemEditParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查套餐明细所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizOfferingGroupItem.getOrgId()) && !loginUserDataScope.contains(bizOfferingGroupItem.getOrgId())) {
                throw new CommonException("您没有权限编辑该套餐明细，明细名称：{}", bizOfferingGroupItem.getOfferingName());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizOfferingGroupItem.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限编辑该套餐明细，明细名称：{}", bizOfferingGroupItem.getOfferingName());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限编辑该套餐明细，明细名称：{}", bizOfferingGroupItem.getOfferingName());
            }
        }
        
        BeanUtil.copyProperties(bizOfferingGroupItemEditParam, bizOfferingGroupItem);
        this.updateById(bizOfferingGroupItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BizOfferingGroupItemIdParam> bizOfferingGroupItemIdParamList) {
        List<String> offeringGroupItemIdList = CollStreamUtil.toList(bizOfferingGroupItemIdParamList, BizOfferingGroupItemIdParam::getId);
        if(ObjectUtil.isNotEmpty(offeringGroupItemIdList)) {
            // 校验数据范围
            List<String> loginUserDataScope = null;
            try {
                loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
            } catch (Exception e) {
                // 获取用户信息失败，按无权限处理
            }
            
            List<BizOfferingGroupItem> offeringGroupItemList = this.listByIds(offeringGroupItemIdList);
            
            if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
                // 有数据范围权限：检查套餐明细所属机构是否在权限范围内
                for(BizOfferingGroupItem offeringGroupItem : offeringGroupItemList) {
                    if(ObjectUtil.isNotEmpty(offeringGroupItem.getOrgId()) && !loginUserDataScope.contains(offeringGroupItem.getOrgId())) {
                        throw new CommonException("您没有权限删除该套餐明细，明细名称：{}", offeringGroupItem.getOfferingName());
                    }
                }
            } else {
                // 无数据范围权限：基于createUser进行权限控制（向后兼容）
                try {
                    String userId = StpLoginUserUtil.getLoginUser().getId();
                    for(BizOfferingGroupItem offeringGroupItem : offeringGroupItemList) {
                        if(!offeringGroupItem.getCreateUser().equals(userId)) {
                            throw new CommonException("您没有权限删除该套餐明细，明细名称：{}", offeringGroupItem.getOfferingName());
                        }
                    }
                } catch (Exception e) {
                    throw new CommonException("您没有权限删除套餐明细");
                }
            }
        }
        
        // 执行删除
        this.removeByIds(offeringGroupItemIdList);
    }

    @Override
    public BizOfferingGroupItem detail(BizOfferingGroupItemIdParam bizOfferingGroupItemIdParam) {
        BizOfferingGroupItem bizOfferingGroupItem = this.queryEntity(bizOfferingGroupItemIdParam.getId());
        
        // 校验数据范围
        List<String> loginUserDataScope = null;
        try {
            loginUserDataScope = StpLoginUserUtil.getLoginUserDataScope();
        } catch (Exception e) {
            // 获取用户信息失败，按无权限处理
        }
        
        if(ObjectUtil.isNotEmpty(loginUserDataScope)) {
            // 有数据范围权限：检查套餐明细所属机构是否在权限范围内
            if(ObjectUtil.isNotEmpty(bizOfferingGroupItem.getOrgId()) && !loginUserDataScope.contains(bizOfferingGroupItem.getOrgId())) {
                throw new CommonException("您没有权限查看该套餐明细，明细名称：{}", bizOfferingGroupItem.getOfferingName());
            }
        } else {
            // 无数据范围权限：基于createUser进行权限控制（向后兼容）
            try {
                String userId = StpLoginUserUtil.getLoginUser().getId();
                if(!bizOfferingGroupItem.getCreateUser().equals(userId)) {
                    throw new CommonException("您没有权限查看该套餐明细，明细名称：{}", bizOfferingGroupItem.getOfferingName());
                }
            } catch (Exception e) {
                throw new CommonException("您没有权限查看该套餐明细，明细名称：{}", bizOfferingGroupItem.getOfferingName());
            }
        }
        
        return bizOfferingGroupItem;
    }

    @Override
    public BizOfferingGroupItem queryEntity(String id) {
        BizOfferingGroupItem bizOfferingGroupItem = this.getById(id);
        if(ObjectUtil.isEmpty(bizOfferingGroupItem)) {
            throw new CommonException("套餐明细不存在，id值为：{}", id);
        }
        return bizOfferingGroupItem;
    }

    @Override
    public List<JSONObject> dynamicFieldConfigList(String columnName) {
        // 获取注解上的数据源
        String annDataSourceName = AnnotationUtil.getAnnotationValue(BizOfferingGroupItemServiceImpl.class, DS.class);
        if (StrUtil.isNotEmpty(annDataSourceName)) {
            // 执行切换租户的数据源为master
            dbsApi.changeDataSource(dbsApi.getDefaultDataSourceName());
        }
        // 该段代码必须在切换数据源之后执行
        String currentDataSourceId = dbsApi.getCurrentDataSourceId();
        if (StrUtil.isNotEmpty(annDataSourceName)){
            // 重新获取数据源id
            currentDataSourceId = dbsApi.dbsDetailByPoolName(annDataSourceName).getStr("id");
        }
        String tableName = AnnotationUtil.getAnnotationValue(BizOfferingGroupItem.class, TableName.class);
        return devDfcApi.getList(currentDataSourceId, tableName, columnName);
    }
}

