package vip.xiaonuo.common.util;

import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * Jakarta Mail 邮件工具类
 * 直接使用 Jakarta Mail API，不依赖 Hutool
 * 
 * <AUTHOR>
 * @date 2025/01/15
 */
public class JakartaEmailUtil {

    /**
     * 发送邮件
     *
     * @param host     SMTP服务器地址
     * @param port     SMTP服务器端口
     * @param from     发件人邮箱
     * @param user     发件人用户名
     * @param password 发件人密码
     * @param to       收件人邮箱地址
     * @param subject  邮件主题
     * @param content  邮件内容
     * @param isHtml   是否为HTML内容
     */
    public static void sendEmail(String host, int port, String from, String user, String password,
                                String to, String subject, String content, boolean isHtml) {
        try {
            // 设置邮件服务器属性
            Properties props = new Properties();
            props.put("mail.smtp.host", host);
            props.put("mail.smtp.port", String.valueOf(port));
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.ssl.enable", "true");
            
            // 创建认证器
            Authenticator authenticator = new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(user, password);
                }
            };
            
            // 创建会话
            Session session = Session.getInstance(props, authenticator);
            
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(from));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to));
            message.setSubject(subject);
            
            if (isHtml) {
                message.setContent(content, "text/html;charset=utf-8");
            } else {
                message.setText(content, "utf-8");
            }
            
            // 发送邮件
            Transport.send(message);
            System.out.println("邮件发送成功");
            
        } catch (MessagingException e) {
            throw new RuntimeException("邮件发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送邮件（使用邮件配置对象）
     *
     * @param emailHost SMTP服务器地址
     * @param emailPort SMTP服务器端口
     * @param emailFrom 发件人邮箱
     * @param emailUser 发件人用户名
     * @param emailPass 发件人密码
     * @param to        收件人邮箱地址
     * @param subject   邮件主题
     * @param content   邮件内容
     */
    public static void sendEmail(String emailHost, Integer emailPort, String emailFrom, 
                                String emailUser, String emailPass, String to, String subject, String content) {
        sendEmail(emailHost, emailPort, emailFrom, emailUser, emailPass, to, subject, content, true);
    }
} 