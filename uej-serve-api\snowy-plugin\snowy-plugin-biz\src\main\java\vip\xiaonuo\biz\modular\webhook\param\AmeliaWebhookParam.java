package vip.xiaonuo.biz.modular.webhook.param;

import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 * Amelia WordPress插件Webhook参数类
 * 基于官方API文档的真实数据格式设计
 * 
 * <AUTHOR>
 * @date 2025/01/27
 */
@Data
public class AmeliaWebhookParam {
    
    /** 响应消息，如"Successfully added booking" */
    private String message;
    
    /** 主要数据内容 */
    private AmeliaWebhookData data;
    
    @Data
    public static class AmeliaWebhookData {
        /** 预约类型：appointment, event, package */
        private String type;
        
        /** 预约信息（当type为appointment时） */
        private AmeliaAppointment appointment;
        
        /** 事件信息（当type为event时） */
        private AmeliaEvent event;
        
        /** 预订信息 */
        private AmeliaBooking booking;
        
        /** UTC时间信息 */
        private List<AmeliaUtcTime> utcTime;
        
        /** 预约状态是否改变 */
        private Boolean appointmentStatusChanged;
        
        /** 循环预约信息 */
        private List<Object> recurring;
        
        /** 套餐信息 */
        private List<Object> packageData;
        
        /** 套餐ID */
        private Long packageId;
        
        /** 客户信息 */
        private AmeliaCustomer customer;
        
        /** 可预订项目信息 */
        private AmeliaBookable bookable;
        
        /** 支付ID */
        private Long paymentId;
        
        /** 套餐客户ID */
        private Long packageCustomerId;
        
        /** 支付信息 */
        private AmeliaPayment payment;
        
        /** 客户面板URL */
        private String customerCabinetUrl;
        
        /** 是否为购物车预订 */
        private Boolean isCart;
    }
    
    @Data
    public static class AmeliaAppointment {
        /** 预约ID */
        private Long id;
        
        /** 预订列表 */
        private List<AmeliaBooking> bookings;
        
        /** 是否通知参与者 */
        private Integer notifyParticipants;
        
        /** 内部备注 */
        private String internalNotes;
        
        /** 状态：approved, pending, canceled, rejected */
        private String status;
        
        /** 服务ID */
        private Long serviceId;
        
        /** 父预约ID */
        private Long parentId;
        
        /** 员工ID */
        private Long providerId;
        
        /** 位置ID */
        private Long locationId;
        
        /** 员工信息 */
        private AmeliaProvider provider;
        
        /** 服务信息 */
        private AmeliaService service;
        
        /** 位置信息 */
        private AmeliaLocation location;
        
        /** Google日历事件ID */
        private String googleCalendarEventId;
        
        /** Google Meet URL */
        private String googleMeetUrl;
        
        /** Outlook日历事件ID */
        private String outlookCalendarEventId;
        
        /** Zoom会议信息 */
        private AmeliaZoomMeeting zoomMeeting;
        
        /** LessonSpace信息 */
        private Object lessonSpace;
        
        /** 预约开始时间 */
        private String bookingStart;
        
        /** 预约结束时间 */
        private String bookingEnd;
        
        /** 类型 */
        private String type;
        
        /** 是否重新安排 */
        private Boolean isRescheduled;
        
        /** 是否已满 */
        private Boolean isFull;
        
        /** 资源列表 */
        private List<Object> resources;
    }
    
    @Data
    public static class AmeliaEvent {
        /** 事件ID */
        private Long id;
        
        /** 事件名称 */
        private String name;
        
        /** 事件描述 */
        private String description;
        
        /** 颜色 */
        private String color;
        
        /** 价格 */
        private BigDecimal price;
        
        /** 预订列表 */
        private List<AmeliaBooking> bookings;
        
        /** 时间段列表 */
        private List<AmeliaEventPeriod> periods;
        
        /** 状态 */
        private String status;
        
        /** 最大容量 */
        private Integer maxCapacity;
        
        /** 自定义票务 */
        private List<AmeliaEventTicket> customTickets;
        
        /** 位置ID */
        private Long locationId;
        
        /** 位置信息 */
        private AmeliaLocation location;
        
        /** 自定义位置 */
        private String customLocation;
        
        /** 创建时间 */
        private String created;
    }
    
    @Data
    public static class AmeliaBooking {
        /** 预订ID */
        private Long id;
        
        /** 客户ID */
        private Long customerId;
        
        /** 客户信息 */
        private AmeliaCustomer customer;
        
        /** 状态：approved, pending, canceled, rejected */
        private String status;
        
        /** 附加服务列表 */
        private List<Object> extras;
        
        /** 优惠券ID */
        private Long couponId;
        
        /** 价格 */
        private BigDecimal price;
        
        /** 优惠券信息 */
        private Object coupon;
        
        /** 自定义字段（JSON字符串） */
        private String customFields;
        
        /** 预订信息（JSON字符串） */
        private String info;
        
        /** 预约ID */
        private Long appointmentId;
        
        /** 人数 */
        private Integer persons;
        
        /** 令牌 */
        private String token;
        
        /** 支付列表 */
        private List<AmeliaPayment> payments;
        
        /** UTC偏移 */
        private Integer utcOffset;
        
        /** 聚合价格 */
        private Boolean aggregatedPrice;
        
        /** 状态是否改变 */
        private Boolean isChangedStatus;
        
        /** 是否最后一次预订 */
        private Boolean isLastBooking;
        
        /** 套餐客户服务 */
        private Object packageCustomerService;
        
        /** 票务数据 */
        private List<AmeliaTicketData> ticketsData;
        
        /** 持续时间 */
        private Integer duration;
        
        /** 创建时间 */
        private String created;
        
        /** 操作是否完成 */
        private Boolean actionsCompleted;
        
        /** 是否更新 */
        private Boolean isUpdated;
    }
    
    @Data
    public static class AmeliaCustomer {
        /** 客户ID */
        private Long id;
        
        /** 名字 */
        private String firstName;
        
        /** 姓氏 */
        private String lastName;
        
        /** 生日 */
        private String birthday;
        
        /** 邮箱 */
        private String email;
        
        /** 电话 */
        private String phone;
        
        /** 类型 */
        private String type;
        
        /** 状态 */
        private String status;
        
        /** 备注 */
        private String note;
        
        /** Zoom用户ID */
        private String zoomUserId;
        
        /** 国家电话ISO */
        private String countryPhoneIso;
        
        /** 外部ID */
        private String externalId;
        
        /** 头像完整路径 */
        private String pictureFullPath;
        
        /** 头像缩略图路径 */
        private String pictureThumbPath;
        
        /** 翻译信息 */
        private String translations;
        
        /** 性别 */
        private String gender;
        
        /** 语言环境 */
        private String locale;
        
        /** 时区 */
        private String timeZone;
    }
    
    @Data
    public static class AmeliaProvider {
        /** 员工ID */
        private Long id;
        
        /** 名字 */
        private String firstName;
        
        /** 姓氏 */
        private String lastName;
        
        /** 邮箱 */
        private String email;
        
        /** 电话 */
        private String phone;
        
        /** 类型 */
        private String type;
        
        /** 状态 */
        private String status;
        
        /** 描述 */
        private String description;
    }
    
    @Data
    public static class AmeliaService {
        /** 服务ID */
        private Long id;
        
        /** 服务名称 */
        private String name;
        
        /** 服务描述 */
        private String description;
        
        /** 颜色 */
        private String color;
        
        /** 价格 */
        private BigDecimal price;
        
        /** 持续时间（秒） */
        private Integer duration;
        
        /** 最小容量 */
        private Integer minCapacity;
        
        /** 最大容量 */
        private Integer maxCapacity;
        
        /** 状态 */
        private String status;
        
        /** 分类ID */
        private Long categoryId;
    }
    
    @Data
    public static class AmeliaLocation {
        /** 位置ID */
        private Long id;
        
        /** 状态 */
        private String status;
        
        /** 名称 */
        private String name;
        
        /** 描述 */
        private String description;
        
        /** 地址 */
        private String address;
        
        /** 电话 */
        private String phone;
        
        /** 纬度 */
        private Double latitude;
        
        /** 经度 */
        private Double longitude;
    }
    
    @Data
    public static class AmeliaPayment {
        /** 支付ID */
        private Long id;
        
        /** 客户预订ID */
        private Long customerBookingId;
        
        /** 套餐客户ID */
        private Long packageCustomerId;
        
        /** 父支付ID */
        private Long parentId;
        
        /** 金额 */
        private BigDecimal amount;
        
        /** 支付网关 */
        private String gateway;
        
        /** 网关标题 */
        private String gatewayTitle;
        
        /** 支付时间 */
        private String dateTime;
        
        /** 状态：pending, paid, partiallyPaid, refunded */
        private String status;
        
        /** 支付数据 */
        private String data;
        
        /** 实体类型 */
        private String entity;
        
        /** 创建时间 */
        private String created;
        
        /** 操作是否完成 */
        private Boolean actionsCompleted;
        
        /** 交易ID */
        private String transactionId;
    }
    
    @Data
    public static class AmeliaBookable {
        /** 可预订项目ID */
        private Long id;
        
        /** 名称 */
        private String name;
        
        /** 描述 */
        private String description;
        
        /** 颜色 */
        private String color;
        
        /** 价格 */
        private BigDecimal price;
        
        /** 类型 */
        private String type;
        
        /** 状态 */
        private String status;
    }
    
    @Data
    public static class AmeliaUtcTime {
        /** 开始时间 */
        private String start;
        
        /** 结束时间 */
        private String end;
    }
    
    @Data
    public static class AmeliaZoomMeeting {
        /** 会议ID */
        private Long id;
        
        /** 开始URL */
        private String startUrl;
        
        /** 加入URL */
        private String joinUrl;
    }
    
    @Data
    public static class AmeliaEventPeriod {
        /** 时间段ID */
        private Long id;
        
        /** 事件ID */
        private Long eventId;
        
        /** 时间段开始 */
        private String periodStart;
        
        /** 时间段结束 */
        private String periodEnd;
    }
    
    @Data
    public static class AmeliaEventTicket {
        /** 票务ID */
        private Long id;
        
        /** 事件ID */
        private Long eventId;
        
        /** 票务名称 */
        private String name;
        
        /** 是否启用 */
        private Boolean enabled;
        
        /** 价格 */
        private BigDecimal price;
        
        /** 票数 */
        private Integer spots;
        
        /** 已售票数 */
        private Integer sold;
    }
    
    @Data
    public static class AmeliaTicketData {
        /** 票务数据ID */
        private Long id;
        
        /** 事件票务ID */
        private Long eventTicketId;
        
        /** 客户预订ID */
        private Long customerBookingId;
        
        /** 人数 */
        private Integer persons;
        
        /** 价格 */
        private BigDecimal price;
    }
} 