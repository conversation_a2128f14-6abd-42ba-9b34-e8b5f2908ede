
package vip.xiaonuo.flw.core.listener;

import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;
import vip.xiaonuo.common.exception.CommonException;

/**
 * 测试执行监听器
 *
 * <AUTHOR>
 * @date 2023/5/22 21:41
 */
@Slf4j
@Component
public class FlwTestExecutionListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        try {
            log.info(">>> 执行监听器：{}事件触发，流程id为：{}，当前活动节点为：{}",
                    execution.getEventName(), execution.getProcessInstanceId(), execution.getCurrentActivityName());
        } catch (Exception e) {
            throw new CommonException("执行监听器{}出现异常：{}", FlwTestExecutionListener.class.getName(), e.getMessage());
        }
    }
}
