<template>
    <xn-form-container
        :title="formData.id ? '编辑考勤记录' : '增加考勤记录'"
        :width="700"
        v-model:open="open"
        :destroy-on-close="true"
        @close="onClose"
    >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-row :gutter="16">
                <a-col :span="12">
                    <a-form-item label="Staff ID：" name="staffId">
                        <a-input v-model:value="formData.staffId" placeholder="Please enter Staff ID" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="Staff Name：" name="staffName">
                        <a-input v-model:value="formData.staffName" placeholder="Please enter Staff Name" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="Org ID：" name="orgId">
                        <a-input v-model:value="formData.orgId" placeholder="Please enter Org ID" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="Org Name：" name="orgName">
                        <a-input v-model:value="formData.orgName" placeholder="Please enter Org Name" allow-clear />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="Work Time：" name="workTime">
                        <a-date-picker v-model:value="formData.workTime" value-format="YYYY-MM-DD HH:mm:ss" show-time placeholder="Please selectWork Time" style="width: 100%" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="Closing Time：" name="closingTime">
                        <a-date-picker v-model:value="formData.closingTime" value-format="YYYY-MM-DD HH:mm:ss" show-time placeholder="Please selectClosing Time" style="width: 100%" />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
        <a-form ref="dynamicFormRef" :model="dynamicFormData" layout="vertical">
            <a-row :gutter="16">
                <a-col :span="12" v-for="(item, index) in dynamicFieldConfigList" :key="index" :index="index">
                    <xn-form-item :fieldConfig="item" :formData="dynamicFormData"/>
                </a-col>
            </a-row>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">{{ $t('common.cancel') }}</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">{{ $t('common.save') }}</a-button>
        </template>
    </xn-form-container>
</template>

<script setup name="bizAttendanceForm">
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import bizAttendanceApi from '@/api/biz/bizAttendanceApi'
    // 抽屉状态
    const open = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    // 动态表单
    const dynamicFormRef = ref()
    const dynamicFieldConfigList = ref([])
    const dynamicFormData = ref({})

    // 打开抽屉
    const onOpen = (record) => {
        open.value = true
        bizAttendanceApi.bizAttendanceDynamicFieldConfigList({ columnName: 'EXT_JSON' }).then((data) => {
            dynamicFieldConfigList.value = data
        })
        if (record) {
            let recordData = cloneDeep(record)
            formData.value = Object.assign({}, recordData)
            dynamicFormData.value = JSON.parse(formData.value.extJson) || {}
        }
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        dynamicFormData.value = {}
        open.value = false
    }
    // 默认要校验的
    const formRules = {
    }
    // 验证并提交数据
    const onSubmit = () => {
        const promiseList = []
        promiseList.push(
            new Promise((resolve, reject) => {
                formRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        promiseList.push(
            new Promise((resolve, reject) => {
                dynamicFormRef.value
                    .validate()
                    .then((result) => {
                        resolve(result)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        )
        submitLoading.value = true
        Promise.all(promiseList)
            .then(() => {
            const formDataParam = cloneDeep(formData.value)
            formDataParam.extJson = JSON.stringify(dynamicFormData.value)
            bizAttendanceApi
                .bizAttendanceSubmitForm(formDataParam, formDataParam.id)
                .then(() => {
                    onClose()
                    emit('successful')
                })
                .finally(() => {
                    submitLoading.value = false
                })
            })
            .catch(() => {})
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
