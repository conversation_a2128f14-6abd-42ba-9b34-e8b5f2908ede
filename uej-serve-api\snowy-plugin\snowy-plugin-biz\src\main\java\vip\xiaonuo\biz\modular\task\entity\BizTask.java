package vip.xiaonuo.biz.modular.task.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.biz.modular.taskitem.entity.BizTaskItem;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 任务信息实体
 *
 * <AUTHOR>
 * @date  2024/06/12 15:42
 **/
@Getter
@Setter
@TableName("biz_task")
public class BizTask {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** Item Name */
    @Schema(description = "Item Name")
    private String itemName;

    /** Create by */
    @Schema(description = "Create by")
    private String creator;

    /** Start Time */
    @Schema(description = "Start Time")
    private String startTime;

    /** Planning end Time */
    @Schema(description = "Planning end Time")
    private String planningEndTime;

    /** actual finish Time */
    @Schema(description = "actual finish Time")
    private String actualFinishTime;

    /** assign */
    @Schema(description = "assign")
    private String assign;

    private String staff;

    private String orgId;

    /** state */
    @Schema(description = "state")
    private String state;

    /** Stop Beginning Time */
    @Schema(description = "Stop Beginning Time")
    private String stopBeginningTime;

    /** Stop End Time */
    @Schema(description = "Stop End Time")
    private String stopEndTime;

    /** Product Name */
    @Schema(description = "Product Name")
    private String extJson;

    /** Total Price */
    @Schema(description = "Total Price")
    private String totalPrice;

    /** Total Cost Price */
    @Schema(description = "Total Cost Price")
    private String totalCostPrice;

    private String totalDiscount;

    /** Total Planning Hours */
    @Schema(description = "Total Planning Hours")
    private String totalPlanHours;

    /** Total Actual Hours */
    @Schema(description = "Total Actual Hours")
    private String totalActualHours;

    /** Total Invoice Hours */
    @Schema(description = "Total Invoice Hours")
    private String totalInvoiceHours;

    /** Cash */
    @Schema(description = "Cash")
    private String cash;

    /** Card */
    @Schema(description = "Card")
    private String card;

    private String rate;

    /** Surcharge Fee */
    @Schema(description = "Surcharge Fee")
    private String surchargeFee;

    /** Gift_Card */
    @Schema(description = "Gift_Card")
    private String giftCard;

    /** Voucher */
    @Schema(description = "Voucher")
    private String voucher;

    /** Processing */
    @Schema(description = "Processing")
    private String processing;

    /** 备注 */
    private String description;

    @Schema(description = "会员id")
    private String customerId;

    @Schema(description = "会员姓名")
    private String customerName;

    @Schema(description = "小费")
    private String tip;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    @Schema(description = "保险公司金额")
    private double insuranceValue;

    @Schema(description = "已付金额")
    private double paidValue;

    @Schema(description = "任务子表")
    @TableField(exist = false)
    private List<BizTaskItem> bizTaskItemList;

    @TableField(exist = false)
    private String voucherNo;

    @TableField(exist = false)
    private String giftCardNo;

    @TableField(exist = false)
    private String mainId;

    @Schema(description = "卡密")
    @TableField(exist = false)
    private String pin;

    @Schema(description = "营业额")
    @TableField(exist = false)
    private double money;

    @Schema(description = "礼品卡列表")
    @TableField(exist = false)
    private List<GiftCardInfo> giftCardList;

    /**
     * 礼品卡信息内部类
     */
    public static class GiftCardInfo {
        private String cardNo;
        private String pin;
        private double amount;
        private double balance;
        private double availableBalance;
        private boolean verified;

        // Getter和Setter方法
        public String getCardNo() {
            return cardNo;
        }

        public void setCardNo(String cardNo) {
            this.cardNo = cardNo;
        }

        public String getPin() {
            return pin;
        }

        public void setPin(String pin) {
            this.pin = pin;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public double getBalance() {
            return balance;
        }

        public void setBalance(double balance) {
            this.balance = balance;
        }

        public double getAvailableBalance() {
            return availableBalance;
        }

        public void setAvailableBalance(double availableBalance) {
            this.availableBalance = availableBalance;
        }

        public boolean isVerified() {
            return verified;
        }

        public void setVerified(boolean verified) {
            this.verified = verified;
        }
    }
}
