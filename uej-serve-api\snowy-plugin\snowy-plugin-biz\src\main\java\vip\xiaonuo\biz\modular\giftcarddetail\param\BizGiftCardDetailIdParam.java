package vip.xiaonuo.biz.modular.giftcarddetail.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 礼品卡信息Id参数
 *
 * <AUTHOR>
 * @date  2024/07/01 18:08
 **/
@Getter
@Setter
public class BizGiftCardDetailIdParam {

    /** ID */
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "ID cannot be empty")
    private String id;
}
