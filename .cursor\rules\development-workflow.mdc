---
description:
globs:
alwaysApply: false
---
# 开发流程

## 环境配置

### 前端环境

1. 安装Node.js和npm
2. 进入前端项目目录：`cd uej-serve-web`
3. 安装依赖：`npm install`
4. 启动开发服务器：`npm run serve`
5. 访问开发环境：http://localhost:81

### 后端环境

1. 安装Java JDK和Maven
2. 进入后端项目目录：`cd uej-serve-api`
3. 使用Maven构建项目：`mvn clean install`
4. 运行应用：`java -jar snowy-web-app/target/snowy-web-app.jar`
5. 导入数据库：使用 [uej_serve_foreign.sql](mdc:uej_serve_foreign.sql) 创建数据库结构

## 开发流程

1. **需求分析**：理解业务需求和功能设计
2. **任务分配**：按模块和功能点分配开发任务
3. **开发实现**：
   - 后端API开发
   - 前端界面开发
   - 前后端联调
4. **代码审查**：进行代码审查，确保代码质量
5. **测试**：
   - 单元测试
   - 集成测试
   - 功能测试
6. **部署**：部署到测试/生产环境

## 版本控制

使用Git进行版本控制：
- 主分支：master
- 开发分支：develop
- 功能分支：feature/*
- 修复分支：bugfix/*
- 发布分支：release/*

## 常见问题解决

### 前端常见问题

- 依赖安装失败：检查npm源或使用`npm install --registry=https://registry.npm.taobao.org`
- 构建错误：检查Node.js版本兼容性

### 后端常见问题

- Maven构建失败：检查Maven仓库配置
- 数据库连接问题：检查数据库配置和连接参数
