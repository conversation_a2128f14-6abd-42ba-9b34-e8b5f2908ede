
package vip.xiaonuo.biz.modular.attendance.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.attendance.entity.BizAttendance;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceAddParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceEditParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendanceIdParam;
import vip.xiaonuo.biz.modular.attendance.param.BizAttendancePageParam;
import cn.hutool.json.JSONObject;

import java.util.List;

/**
 * 考勤记录Service接口
 *
 * <AUTHOR>
 * @date  2024/08/01 13:54
 **/
public interface BizAttendanceService extends IService<BizAttendance> {

    /**
     * 获取考勤记录分页
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    Page<BizAttendance> page(BizAttendancePageParam bizAttendancePageParam);

    /**
     * 添加考勤记录
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    void add(BizAttendanceAddParam bizAttendanceAddParam);

    /**
     * 编辑考勤记录
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    void edit(BizAttendanceEditParam bizAttendanceEditParam);

    /**
     * 删除考勤记录
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    void delete(List<BizAttendanceIdParam> bizAttendanceIdParamList);

    /**
     * 获取考勤记录详情
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     */
    BizAttendance detail(BizAttendanceIdParam bizAttendanceIdParam);

    /**
     * 获取考勤记录详情
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
     **/
    BizAttendance queryEntity(String id);

    /**
     * 获取动态字段配置
     *
     * <AUTHOR>
     * @date  2024/08/01 13:54
    **/
    List<JSONObject> dynamicFieldConfigList(String columnName);
}
