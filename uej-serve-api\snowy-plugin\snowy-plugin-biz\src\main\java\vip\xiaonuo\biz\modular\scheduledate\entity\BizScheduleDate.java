
package vip.xiaonuo.biz.modular.scheduledate.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 排班表当日属性实体
 *
 * <AUTHOR>
 * @date  2024/06/18 16:34
 **/
@Getter
@Setter
@TableName("biz_schedule_date")
public class BizScheduleDate {

    /** ID */
    @TableId
    @Schema(description = "ID")
    private String id;

    /** Schedule Date */
    @Schema(description = "Schedule Date")
    private String scheduleDate;

    private String openTime;

    private String organizationId;

    /** Holiday */
    @Schema(description = "Holiday")
    private String holiday;

    /** Num */
    @Schema(description = "Num")
    private String num;

    /** Remark */
    @Schema(description = "Remark")
    private String remark;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 删除标志 */
    @Schema(description = "删除标志")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String deleteFlag;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 创建用户 */
    @Schema(description = "创建用户")
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 修改用户 */
    @Schema(description = "修改用户")
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    private String extJson;
}
