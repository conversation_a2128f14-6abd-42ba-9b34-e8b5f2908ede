
package vip.xiaonuo.biz.modular.attendance.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

/**
 * 考勤记录Id参数
 *
 * <AUTHOR>
 * @date  2024/08/01 13:54
 **/
@Getter
@Setter
public class BizAttendanceIdParam {

    /** 主键 */
    @Schema(description = "主键")
    @NotBlank(message = "id不能为空")
    private String id;
}
