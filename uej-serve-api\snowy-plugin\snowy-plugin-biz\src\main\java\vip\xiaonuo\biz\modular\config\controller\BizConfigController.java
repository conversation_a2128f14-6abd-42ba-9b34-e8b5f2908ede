
package vip.xiaonuo.biz.modular.config.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.biz.modular.config.entity.BizConfig;
import vip.xiaonuo.biz.modular.config.param.BizConfigAddParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigEditParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigIdParam;
import vip.xiaonuo.biz.modular.config.param.BizConfigPageParam;
import vip.xiaonuo.biz.modular.config.service.BizConfigService;
import cn.hutool.json.JSONObject;
import java.util.List;

import jakarta.annotation.Resource;
import javax.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 业务配置控制器
 *
 * <AUTHOR>
 * @date  2024/07/08 19:09
 */
@Tag(name = "业务配置控制器")
@RestController
@Validated
public class BizConfigController {

    @Resource
    private BizConfigService bizConfigService;

    /**
     * 获取业务配置分页
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    @Operation(summary = "获取业务配置分页")
    @SaCheckPermission("/biz/config/page")
    @GetMapping("/biz/config/page")
    public CommonResult<Page<BizConfig>> page(BizConfigPageParam bizConfigPageParam) {
        return CommonResult.data(bizConfigService.page(bizConfigPageParam));
    }

    /**
     * 添加业务配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    @Operation(summary = "添加业务配置")
    @CommonLog("添加业务配置")
    @SaCheckPermission("/biz/config/add")
    @PostMapping("/biz/config/add")
    public CommonResult<String> add(@RequestBody @Valid BizConfigAddParam bizConfigAddParam) {
        bizConfigService.add(bizConfigAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑业务配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    @Operation(summary = "编辑业务配置")
    @CommonLog("编辑业务配置")
    @SaCheckPermission("/biz/config/edit")
    @PostMapping("/biz/config/edit")
    public CommonResult<String> edit(@RequestBody @Valid BizConfigEditParam bizConfigEditParam) {
        bizConfigService.edit(bizConfigEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除业务配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    @Operation(summary = "删除业务配置")
    @CommonLog("删除业务配置")
    @SaCheckPermission("/biz/config/delete")
    @PostMapping("/biz/config/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                   List<BizConfigIdParam> bizConfigIdParamList) {
        bizConfigService.delete(bizConfigIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取业务配置详情
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    @Operation(summary = "获取业务配置详情")
    @SaCheckPermission("/biz/config/detail")
    @GetMapping("/biz/config/detail")
    public CommonResult<BizConfig> detail(@Valid BizConfigIdParam bizConfigIdParam) {
        return CommonResult.data(bizConfigService.detail(bizConfigIdParam));
    }

    /**
     * 获取动态字段的配置
     *
     * <AUTHOR>
     * @date  2024/07/08 19:09
     */
    @Operation(summary = "获取业务配置动态字段的配置")
    @SaCheckPermission("/biz/config/dynamicFieldConfigList")
    @GetMapping("/biz/config/dynamicFieldConfigList")
    public CommonResult<List<JSONObject>> dynamicFieldConfigList(String columnName) {
        return CommonResult.data(bizConfigService.dynamicFieldConfigList(columnName));
    }
}
