
package vip.xiaonuo.flw.modular.template.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 流水号模板编辑参数
 *
 * <AUTHOR>
 * @date 2022/8/1 14:15
 */
@Getter
@Setter
public class FlwTemplateSnEditParam {

    /** id */
    @Schema(description = "id")
    @NotBlank(message = "id不能为空")
    private String id;

    /** 名称 */
    @Schema(description = "名称")
    @NotBlank(message = "name不能为空")
    private String name;

    /** 前缀 */
    @Schema(description = "前缀")
    @NotBlank(message = "prefix不能为空")
    private String prefix;

    /** 年月格式 */
    @Schema(description = "年月格式")
    @NotBlank(message = "dateFormat不能为空")
    private String dateFormat;

    /** 后缀位数 */
    @Schema(description = "后缀位数")
    @NotNull(message = "suffixDigits不能为空")
    private Integer suffixDigits;

    /** 后缀初始值 */
    @Schema(description = "后缀初始值")
    @NotNull(message = "suffixInitialValue不能为空")
    private Integer suffixInitialValue;

    /** 后缀增量 */
    @Schema(description = "后缀增量")
    @NotNull(message = "suffixIncrementalValue不能为空")
    private Integer suffixIncrementalValue;

    /** 显示值 */
    @Schema(description = "显示值")
    @NotBlank(message = "previewValue不能为空")
    private String previewValue;

    /** 分类 */
    @Schema(description = "分类")
    @NotBlank(message = "category不能为空")
    private String category;

    /** 排序码 */
    @Schema(description = "排序码")
    @NotNull(message = "sortCode不能为空")
    private Integer sortCode;

    /** 扩展信息 */
    @Schema(description = "扩展JSON")
    private String extJson;
}
