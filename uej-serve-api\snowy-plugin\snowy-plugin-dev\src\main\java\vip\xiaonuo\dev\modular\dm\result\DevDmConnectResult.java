package vip.xiaonuo.dev.modular.dm.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DevDmConnectResult {

    /**
     * 连接URL
     */
    @Schema(description = "连接URL")
    private String url;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;

}
