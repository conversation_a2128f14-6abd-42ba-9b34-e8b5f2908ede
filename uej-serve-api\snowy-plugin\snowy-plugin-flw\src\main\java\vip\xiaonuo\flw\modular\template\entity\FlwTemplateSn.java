
package vip.xiaonuo.flw.modular.template.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

/**
 * 流水号模板实体
 *
 * <AUTHOR>
 * @date 2022/4/21 16:13
 **/
@Getter
@Setter
@TableName("ACT_EXT_TEMPLATE_SN")
public class FlwTemplateSn extends CommonEntity {

    /** id */
    @Schema(description = "主键")
    private String id;

    /** 租户id */
    @Schema(description = "租户id")
    private String tenantId;

    /** 名称 */
    @Schema(description = "名称")
    private String name;

    /** 编码 */
    @Schema(description = "编码")
    private String code;

    /** 前缀 */
    @Schema(description = "前缀")
    private String prefix;

    /** 年月格式 */
    @Schema(description = "年月格式")
    private String dateFormat;

    /** 后缀位数 */
    @Schema(description = "后缀位数")
    private Integer suffixDigits;

    /** 后缀初始值 */
    @Schema(description = "后缀初始值")
    private Integer suffixInitialValue;

    /** 后缀增量 */
    @Schema(description = "后缀增量")
    private Integer suffixIncrementalValue;

    /** 显示值 */
    @Schema(description = "显示值")
    private String previewValue;

    /** 最新值 */
    @Schema(description = "最新值")
    private String latestValue;

    /** 分类 */
    @Schema(description = "分类")
    private String category;

    /** 排序码 */
    @Schema(description = "排序码")
    private Integer sortCode;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private String extJson;

}
