.login-icon-gray {
  color: rgba(0, 0, 0, 0.25);
}
.login-validCode-img {
  border: 1px solid var(--border-color-split);
  cursor: pointer;
  width: 100%;
  height: 40px;
}
.login_background {
  width: 100%;
	min-height: 100vh;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-image: url(/img/login_background.png);
}
.login_background_front {
  width: 450px;
  height: 450px;
  margin-left: 100px;
  margin-top: 15%;
  overflow: hidden;
  /*position: relative;*/
  background-size: cover;
  background-position: center;
  background-image: url(/img/login_background_front.png);
  animation-name: myfirst;
  animation-duration: 5s;
  animation-timing-function: linear;
  animation-delay: 1s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-play-state: running;
}
@keyframes myfirst {
  0% {
    left: 0px;
    top: 0px;
  }
  50% {
    left: 50px;
    top: 0px;
  }
  100% {
    left: 0px;
    top: 0px;
  }
}
@-webkit-keyframes myfirst /* Safari and Chrome */ {
  0% {
    left: 0px;
    top: 0px;
  }
  50% {
    left: 50px;
    top: 0px;
  }
  100% {
    left: 0px;
    top: 0px;
  }
}
.login_adv__title h2 {
  font-size: 40px;
}
.login_adv__title h4 {
  font-size: 18px;
  margin-top: 10px;
  font-weight: normal;
}
.login_adv__title p {
  font-size: 14px;
  margin-top: 10px;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.6);
}
.login_adv__title div {
  margin-top: 10px;
  display: flex;
  align-items: center;
}
.login_adv__title div span {
  margin-right: 15px;
}
.login_adv__title div i {
  font-size: 40px;
}
.login_adv__title div i.add {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.6);
}
/*background-image:linear-gradient(transparent, #000);*/
.login_main {
  flex: 1;
  overflow: auto;
  display: flex;
}
.login-form {
  top: 15%;
  right: 15%;
  position: absolute;
  width: 450px;
  margin-left: 10%;
  margin-top: 20px;
  padding: 10px 0;
}
.login-header {
  margin-bottom: 20px;
}
.login-header .logo {
  display: flex;
  align-items: center;
}
.login-header .logo img {
  width: 35px;
  height: 35px;
  vertical-align: bottom;
  margin-right: 10px;
}
.login-header .logo label {
  font-size: 24px;
}
.login-header h2 {
  font-size: 24px;
  font-weight: bold;
  margin-top: 40px;
}
.login_config {
  position: absolute;
  top: 20px;
  right: 20px;
}
@media (max-width: 1200px) {
  .login-form {
    width: 340px;
  }
}
@media (max-width: 1000px) {
  .login_main {
    display: block;
  }
  .login_background_front {
    display: none;
  }
  .login-form {
    width: 100%;
    padding: 20px 40px;
    right: 0 !important;
    top: 0 !important;
  }
}
