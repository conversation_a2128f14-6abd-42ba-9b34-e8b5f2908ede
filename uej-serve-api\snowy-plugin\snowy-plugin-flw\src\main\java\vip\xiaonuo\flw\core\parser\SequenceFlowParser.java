
package vip.xiaonuo.flw.core.parser;

import cn.hutool.core.util.StrUtil;
import org.camunda.bpm.model.bpmn.builder.ExclusiveGatewayBuilder;
import vip.xiaonuo.flw.core.node.FlwNode;

public class SequenceFlowParser {

    /**
     * 构建单个条件连接线
     *
     * <AUTHOR>
     * @date 2022/3/24 9:24
     **/
    public static ExclusiveGatewayBuilder buildSequenceFlowSingle(ExclusiveGatewayBuilder exclusiveGatewayBuilder, FlwNode flwNode) {
        StringBuilder conditionResult = StrUtil.builder();
        conditionResult.append("${");
        StringBuilder finalConditionResult = conditionResult;
        flwNode.getProperties().getConditionInfo().forEach(flwNodeConditionProps -> {
            StringBuilder conditionItem = StrUtil.builder();
            conditionItem.append("(");
            StringBuilder finalConditionItem = conditionItem;
            flwNodeConditionProps.forEach(flwNodeConditionProp -> {
                String fieldOrigin;
                if(flwNodeConditionProp.getField().contains(StrUtil.UNDERLINE)) {
                    fieldOrigin = StrUtil.toCamelCase(flwNodeConditionProp.getField().toLowerCase());
                } else {
                    fieldOrigin = flwNodeConditionProp.getField().toLowerCase();
                }
                String field = StrUtil.replace(fieldOrigin, StrUtil.DOT, StrUtil.UNDERLINE);
                String logic = flwNodeConditionProp.getOperator();
                String value = flwNodeConditionProp.getValue();
                if(logic.equals("==") || logic.equals(">") || logic.equals(">=") || logic.equals("<") || logic.equals("<=")) {
                    finalConditionItem.append(field).append(logic).append(value).append("&&");
                } else {
                    if(logic.equals("include")) {
                        finalConditionItem.append(field).append(".contains(\"").append(value).append("\")").append("&&");
                    } else if(logic.equals("notInclude")) {
                        finalConditionItem.append("!").append(field).append(".contains(\"").append(value).append("\")").append("&&");
                    } else {
                        finalConditionItem.append(field).append(logic).append(value).append("&&");
                    }
                }
            });
            conditionItem = new StringBuilder(StrUtil.removeSuffix(conditionItem, "&&"));
            conditionItem.append(")");
            finalConditionResult.append(conditionItem).append("||");
        });
        conditionResult = new StringBuilder(StrUtil.removeSuffix(conditionResult, "||"));
        conditionResult.append("}");
        if(conditionResult.toString().equals("${}")) {
            return exclusiveGatewayBuilder.condition(flwNode.getTitle(), "${true}").sequenceFlowId(flwNode.getId());
        } else {
            return exclusiveGatewayBuilder.condition(flwNode.getTitle(), conditionResult.toString()).sequenceFlowId(flwNode.getId());
        }
    }
}
